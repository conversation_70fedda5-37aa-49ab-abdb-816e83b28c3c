"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const sampQuery_1 = require("../../utils/sampQuery");
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('ip')
        .setDescription('Display server information and connection details'),
    async execute(interaction, client) {
        await interaction.deferReply();
        try {
            // Fallback to interaction.client if client parameter is not provided
            const botClient = client || interaction.client;
            const config = botClient.config;
            const sampQuery = new sampQuery_1.SAMPQuery(botClient.logger);
            const result = await sampQuery.queryDetailedInfo(config.SAMP_SERVER_IP, config.SAMP_SERVER_PORT, 8000);
            const embed = new discord_js_1.EmbedBuilder()
                .setTitle('🎮 Server Information')
                .setColor(result.success ? discord_1.EmbedColors.SUCCESS : discord_1.EmbedColors.WARNING)
                .setThumbnail(config.ICON_URL || null)
                .setTimestamp();
            embed.addFields([
                {
                    name: '🌐 Server IP',
                    value: `\`${config.SAMP_SERVER_IP}:${config.SAMP_SERVER_PORT}\``,
                    inline: true,
                },
                {
                    name: '📋 Copy IP',
                    value: `\`${config.SAMP_SERVER_IP}:${config.SAMP_SERVER_PORT}\``,
                    inline: true,
                }
            ]);
            if (result.success && result.info) {
                const info = result.info;
                embed.addFields([
                    {
                        name: '✅ Status',
                        value: 'Online',
                        inline: true,
                    },
                    {
                        name: '👥 Players',
                        value: `${info.players}/${info.maxPlayers}`,
                        inline: true,
                    },
                    {
                        name: '🎯 Gamemode',
                        value: info.gamemode || 'Unknown',
                        inline: true,
                    },
                    {
                        name: '🏠 Hostname',
                        value: info.hostname || 'Unknown',
                        inline: false,
                    },
                    {
                        name: '🌍 Language',
                        value: info.language || 'Unknown',
                        inline: true,
                    },
                    {
                        name: '🔒 Password',
                        value: info.password ? 'Yes' : 'No',
                        inline: true,
                    },
                    {
                        name: '📦 Version',
                        value: info.version || '0.3.7/0.3DL',
                        inline: true,
                    }
                ]);
                const playerPercentage = (info.players / info.maxPlayers) * 100;
                let playerStatus = '';
                if (playerPercentage >= 80) {
                    playerStatus = '🔴 High Population';
                }
                else if (playerPercentage >= 50) {
                    playerStatus = '🟡 Medium Population';
                }
                else if (playerPercentage >= 20) {
                    playerStatus = '🟢 Low Population';
                }
                else {
                    playerStatus = '⚪ Very Low Population';
                }
                embed.addFields([
                    {
                        name: '📊 Population Status',
                        value: playerStatus,
                        inline: false,
                    }
                ]);
                // Add player list if available and not too many players
                if (result.players && result.players.length > 0 && result.players.length <= 10) {
                    const playerList = result.players
                        .sort((a, b) => a.id - b.id)
                        .map(player => `\`${player.id}\` **${player.name}** (Score: ${player.score}, Ping: ${player.ping}ms)`)
                        .join('\n');
                    embed.addFields([
                        {
                            name: '👥 Online Players',
                            value: playerList.length > 1024 ? playerList.substring(0, 1021) + '...' : playerList,
                            inline: false,
                        }
                    ]);
                }
                else if (result.players && result.players.length > 10) {
                    embed.addFields([
                        {
                            name: '👥 Online Players',
                            value: `Too many players to display (${result.players.length} online). Use in-game player list.`,
                            inline: false,
                        }
                    ]);
                }
            }
            else {
                embed.addFields([
                    {
                        name: '❌ Status',
                        value: 'Offline or Unreachable',
                        inline: true,
                    },
                    {
                        name: '⚠️ Error',
                        value: result.error || 'Unknown error',
                        inline: false,
                    }
                ]);
            }
            embed.addFields([
                {
                    name: '📱 How to Connect',
                    value: '1. Open SA-MP Client\n2. Add Server: `' + config.SAMP_SERVER_IP + ':' + config.SAMP_SERVER_PORT + '`\n3. Connect and enjoy!',
                    inline: false,
                }
            ]);
            const footerData = {
                text: `Server: ${config.SAMP_SERVER_IP}:${config.SAMP_SERVER_PORT} • Queried at`
            };
            if (botClient.user) {
                footerData.iconURL = botClient.user.displayAvatarURL();
            }
            embed.setFooter(footerData);
            await interaction.editReply({ embeds: [embed] });
            botClient.logger.info('Server info command executed', {
                userId: interaction.user.id,
                username: interaction.user.username,
                guildId: interaction.guildId,
                serverOnline: result.success,
                players: result.info?.players || 0,
            });
        }
        catch (error) {
            const botClient = client || interaction.client;
            botClient.logger.error('Error in serverinfo command', {
                error: error.message,
                userId: interaction.user.id,
            });
            const errorEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('❌ Error')
                .setDescription('Failed to retrieve server information. Please try again later.')
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp();
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            }
            else {
                await interaction.reply({ embeds: [errorEmbed], flags: 64 });
            }
        }
    },
};
//# sourceMappingURL=serverinfo.js.map