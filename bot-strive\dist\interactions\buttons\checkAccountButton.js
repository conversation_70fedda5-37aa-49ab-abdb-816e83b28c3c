"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const AccountRepository_1 = require("../../database/repositories/AccountRepository");
const embeds_1 = require("../../utils/embeds");
/**
 * Check account button handler
 */
const checkAccountButton = {
    customId: 'button-checkaccount',
    async execute(interaction) {
        try {
            const client = interaction.client;
            const accountRepo = new AccountRepository_1.AccountRepository(client.db, client.logger);
            const accountResult = await accountRepo.findByDiscordId(interaction.user.id);
            if (!accountResult.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Database Error', 'There was an error checking your account status. Please try again later.', true);
                return;
            }
            if (!accountResult.data) {
                await embeds_1.InteractionUtils.replyWarning(interaction, 'No Account Found', '⚠️ You do not have a registered account. Please use the Register button to create one.', true);
                return;
            }
            const account = accountResult.data;
            const embed = new discord_js_1.EmbedBuilder()
                .setTitle('Your Account Information')
                .setColor(discord_1.EmbedColors.INFO)
                .addFields([
                {
                    name: 'Username',
                    value: `\`${account.username}\``,
                    inline: true,
                },
                {
                    name: 'Verification Code',
                    value: `\`${account.discord_code}\``,
                    inline: true,
                },
                {
                    name: 'Server Information',
                    value: `**IP:** \`${client.config.SAMP_SERVER_IP}:${client.config.SAMP_SERVER_PORT}\``,
                    inline: false,
                },
            ])
                .setFooter({ text: 'Strive Roleplay' })
                .setTimestamp();
            if (client.config.ICON_URL) {
                embed.setThumbnail(client.config.ICON_URL);
            }
            await interaction.reply({
                embeds: [embed],
                flags: 64,
            });
        }
        catch (error) {
            if (!interaction.replied && !interaction.deferred) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Error', 'An unexpected error occurred while checking your account.', true);
            }
            const client = interaction.client;
            client.logger.error('Error in check account button', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
        }
    },
};
exports.default = checkAccountButton;
//# sourceMappingURL=checkAccountButton.js.map