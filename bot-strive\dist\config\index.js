"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConfig = exports.getConfigManager = exports.ConfigManager = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const config_1 = require("../types/config");
dotenv_1.default.config();
/**
 * Configuration manager with validation and type safety
 */
class ConfigManager {
    config;
    logger;
    constructor(logger) {
        this.logger = logger;
        this.config = this.loadAndValidateConfig();
    }
    /**
     * Load and validate configuration from environment variables
     */
    loadAndValidateConfig() {
        try {
            const rawConfig = {
                DISCORD_TOKEN: process.env['DISCORD_TOKEN'],
                DISCORD_CLIENT_ID: process.env['DISCORD_CLIENT_ID'],
                DISCORD_GUILD_ID: process.env['DISCORD_GUILD_ID'],
                BOT_PREFIX: process.env['BOT_PREFIX'],
                NODE_ENV: process.env['NODE_ENV'],
                LOG_LEVEL: process.env['LOG_LEVEL'],
                DB_HOST: process.env['DB_HOST'],
                DB_PORT: process.env['DB_PORT'],
                DB_USER: process.env['DB_USER'],
                DB_PASSWORD: process.env['DB_PASSWORD'],
                DB_NAME: process.env['DB_NAME'],
                SAMP_SERVER_IP: process.env['SAMP_SERVER_IP'],
                SAMP_SERVER_PORT: process.env['SAMP_SERVER_PORT'],
                ERROR_WEBHOOK_ID: process.env['ERROR_WEBHOOK_ID'],
                ERROR_WEBHOOK_TOKEN: process.env['ERROR_WEBHOOK_TOKEN'],
                ROLE_UCP: process.env['ROLE_UCP'],
                ROLE_UNVERIFIED: process.env['ROLE_UNVERIFIED'],
                WELCOME_ENABLED: process.env['WELCOME_ENABLED'],
                WELCOME_CHANNEL_ID: process.env['WELCOME_CHANNEL_ID'],
                WELCOME_MESSAGE: process.env['WELCOME_MESSAGE'],
                WELCOME_BANNER_URL: process.env['WELCOME_BANNER_URL'],
                LEAVE_ENABLED: process.env['LEAVE_ENABLED'],
                CHANGELOG_CHANNEL_ID: process.env['CHANGELOG_CHANNEL_ID'],
                ICON_URL: process.env['ICON_URL'],
            };
            const validatedConfig = config_1.ConfigSchema.parse(rawConfig);
            if (this.logger) {
                this.logger.info('Configuration loaded and validated successfully', {
                    nodeEnv: validatedConfig.NODE_ENV,
                    logLevel: validatedConfig.LOG_LEVEL,
                });
            }
            return validatedConfig;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown configuration error';
            if (this.logger) {
                this.logger.error('Configuration validation failed', { error: errorMessage });
            }
            else {
                console.error('Configuration validation failed:', errorMessage);
            }
            throw new Error(`Configuration validation failed: ${errorMessage}`);
        }
    }
    /**
     * Get the complete configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Get Discord configuration
     */
    getDiscordConfig() {
        return {
            token: this.config.DISCORD_TOKEN,
            clientId: this.config.DISCORD_CLIENT_ID,
            guildId: this.config.DISCORD_GUILD_ID,
        };
    }
    /**
     * Get database configuration
     */
    getDatabaseConfig() {
        return {
            host: this.config.DB_HOST,
            port: this.config.DB_PORT,
            user: this.config.DB_USER,
            password: this.config.DB_PASSWORD,
            database: this.config.DB_NAME,
            connectionLimit: 10,
            acquireTimeout: 60000,
            timeout: 60000,
            reconnect: true,
        };
    }
    /**
     * Get SAMP server configuration
     */
    getSampServerConfig() {
        return {
            ip: this.config.SAMP_SERVER_IP,
            port: this.config.SAMP_SERVER_PORT,
        };
    }
    /**
     * Get webhook configuration for error logging
     */
    getWebhookConfig() {
        if (!this.config.ERROR_WEBHOOK_ID || !this.config.ERROR_WEBHOOK_TOKEN) {
            return null;
        }
        return {
            id: this.config.ERROR_WEBHOOK_ID,
            token: this.config.ERROR_WEBHOOK_TOKEN,
        };
    }
    /**
     * Check if the bot is in development mode
     */
    isDevelopment() {
        return this.config.NODE_ENV === 'development';
    }
    /**
     * Check if the bot is in production mode
     */
    isProduction() {
        return this.config.NODE_ENV === 'production';
    }
    /**
     * Check if the bot is in test mode
     */
    isTest() {
        return this.config.NODE_ENV === 'test';
    }
    /**
     * Get a specific configuration value
     */
    get(key) {
        return this.config[key];
    }
    /**
     * Reload configuration (useful for testing)
     */
    reload() {
        dotenv_1.default.config({ override: true });
        this.config = this.loadAndValidateConfig();
        if (this.logger) {
            this.logger.info('Configuration reloaded');
        }
    }
    /**
     * Validate that all required configuration is present
     */
    validateRequiredConfig() {
        const requiredFields = [
            'DISCORD_TOKEN',
            'DISCORD_CLIENT_ID',
            'DISCORD_GUILD_ID',
            'DB_USER',
            'DB_NAME',
        ];
        const missingFields = requiredFields.filter(field => !this.config[field]);
        if (missingFields.length > 0) {
            throw new Error(`Missing required configuration fields: ${missingFields.join(', ')}`);
        }
    }
}
exports.ConfigManager = ConfigManager;
let configManager;
const getConfigManager = (logger) => {
    if (!configManager) {
        configManager = new ConfigManager(logger);
    }
    return configManager;
};
exports.getConfigManager = getConfigManager;
const getConfig = (logger) => {
    return (0, exports.getConfigManager)(logger).getConfig();
};
exports.getConfig = getConfig;
//# sourceMappingURL=index.js.map