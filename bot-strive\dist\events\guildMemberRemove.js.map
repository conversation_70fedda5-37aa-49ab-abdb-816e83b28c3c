{"version": 3, "file": "guildMemberRemove.js", "sourceRoot": "", "sources": ["../../src/events/guildMemberRemove.ts"], "names": [], "mappings": ";;AAQA,4BAwCC;AAhDD,2CAAwF;AACxF,8CAA+C;AAG/C;;;GAGG;AACH,mBAAwB,MAAiB;IACvC,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAwC,EAAE,EAAE;QAChF,IAAI,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,+CAA+C,EAAE;gBAClE,MAAM,EAAE,MAAM,CAAC,EAAE;gBACjB,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,SAAS;gBAC5C,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;gBACxB,SAAS,EAAE,MAAM,CAAC,OAAO;gBACzB,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,KAAK;aACjC,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;gBACvB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,uDAAuD,EAAE;wBAC1E,MAAM,EAAE,MAAM,CAAC,EAAE;wBACjB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;qBACzB,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;YACH,CAAC;YACD,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;gBACrB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBACpD,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;oBACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;iBAC/B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,gBAAgB,CAAC,MAAqB,EAAE,MAAM,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACtD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,KAAK,EAAG,KAAe,CAAC,KAAK;gBAC7B,MAAM,EAAE,MAAM,CAAC,EAAE;gBACjB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;aACzB,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,gBAAgB,CAAC,MAAmB,EAAE,MAAiB;IACpE,IAAI,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;YACrD,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;YACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;YAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YACxB,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa;YACzC,gBAAgB,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB;SACnD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,EAAE;gBACnE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;gBACxB,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa;aAC1C,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,2DAA2D,EAAE;gBAC9E,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAEzF,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE;gBAC9D,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB;gBAC3C,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QAC1B,IAAI,kBAAkB,GAAG,kBAAkB,CAAC;QAE5C,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAClF,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;YAE1E,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;gBACb,kBAAkB,GAAG,GAAG,IAAI,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACrG,CAAC;iBAAM,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACrB,kBAAkB,GAAG,GAAG,KAAK,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,UAAU,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAC9G,CAAC;iBAAM,CAAC;gBACN,kBAAkB,GAAG,GAAG,OAAO,UAAU,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACtE,CAAC;QACH,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,yBAAY,EAAE;aAClC,QAAQ,CAAC,gBAAgB,CAAC;aAC1B,cAAc,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,eAAe,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;aAC3E,QAAQ,CAAC,qBAAW,CAAC,KAAK,CAAC;aAC3B,SAAS,CAAC;YACT;gBACE,IAAI,EAAE,gBAAgB;gBACtB,KAAK,EAAE;oBACL,aAAa,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACnC,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC7B,aAAa,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI;oBAC/B,gBAAgB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK;iBACzD,CAAC,IAAI,CAAC,IAAI,CAAC;gBACZ,MAAM,EAAE,IAAI;aACb;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE;oBACL,iBAAiB,kBAAkB,EAAE;oBACrC,eAAe,QAAQ,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE;oBACxF,oBAAoB,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE;oBAC9C,2BAA2B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK;iBAChF,CAAC,IAAI,CAAC,IAAI,CAAC;gBACZ,MAAM,EAAE,IAAI;aACb;SACF,CAAC;aACD,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;aAC5C,YAAY,EAAE,CAAC;QAElB,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK;iBAC7B,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC;iBACzC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAEhB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,UAAU,CAAC,SAAS,CAAC,CAAC;wBACpB,IAAI,EAAE,UAAU;wBAChB,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;wBACrE,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,SAAS,EAAE,CAAC;YACd,UAAU,CAAC,SAAS,CAAC;gBACnB,IAAI,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;gBACzC,OAAO,EAAE,SAAS;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,SAAS,CAAC;gBACnB,IAAI,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;aAC1C,CAAC,CAAC;QACL,CAAC;QAED,MAAO,YAA4B,CAAC,IAAI,CAAC;YACvC,MAAM,EAAE,CAAC,UAAU,CAAC;SACrB,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;YACpD,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;YACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;YAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YACxB,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB;YAC3C,kBAAkB;SACnB,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YACjD,KAAK,EAAG,KAAe,CAAC,OAAO;YAC/B,KAAK,EAAG,KAAe,CAAC,KAAK;YAC7B,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;YACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YACxB,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB;SAC5C,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}