/**
 * Common utility types and interfaces
 */
/**
 * Generic result type for operations that can succeed or fail
 */
export type Result<T, E = Error> = {
    success: true;
    data: T;
} | {
    success: false;
    error: E;
};
/**
 * Async result type
 */
export type AsyncResult<T, E = Error> = Promise<Result<T, E>>;
/**
 * Optional fields utility type
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
/**
 * Required fields utility type
 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
/**
 * Deep partial utility type
 */
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
/**
 * Logger interface
 */
export interface Logger {
    error(message: string, meta?: Record<string, unknown>): void;
    warn(message: string, meta?: Record<string, unknown>): void;
    info(message: string, meta?: Record<string, unknown>): void;
    debug(message: string, meta?: Record<string, unknown>): void;
}
/**
 * Event emitter interface for type-safe events
 */
export interface TypedEventEmitter<T extends Record<string, unknown[]>> {
    on<K extends keyof T>(event: K, listener: (...args: T[K]) => void): this;
    off<K extends keyof T>(event: K, listener: (...args: T[K]) => void): this;
    emit<K extends keyof T>(event: K, ...args: T[K]): boolean;
}
/**
 * Validation result
 */
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
}
/**
 * Pagination options
 */
export interface PaginationOptions {
    page: number;
    limit: number;
    offset: number;
}
/**
 * Paginated response
 */
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
/**
 * Cache interface
 */
export interface Cache<T> {
    get(key: string): T | undefined;
    set(key: string, value: T, ttl?: number): void;
    delete(key: string): boolean;
    clear(): void;
    has(key: string): boolean;
    size(): number;
}
/**
 * Rate limit configuration
 */
export interface RateLimitConfig {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
}
/**
 * Health check result
 */
export interface HealthCheckResult {
    status: 'healthy' | 'unhealthy' | 'degraded';
    timestamp: Date;
    uptime: number;
    checks: Record<string, {
        status: 'pass' | 'fail' | 'warn';
        message?: string;
        duration?: number;
    }>;
}
//# sourceMappingURL=common.d.ts.map