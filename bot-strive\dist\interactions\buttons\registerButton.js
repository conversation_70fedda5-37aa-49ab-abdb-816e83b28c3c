"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const AccountRepository_1 = require("../../database/repositories/AccountRepository");
const embeds_1 = require("../../utils/embeds");
/**
 * Register button handler
 */
const registerButton = {
    customId: 'button-register',
    async execute(interaction) {
        try {
            const client = interaction.client;
            const accountRepo = new AccountRepository_1.AccountRepository(client.db, client.logger);
            const existingAccount = await accountRepo.findByDiscordId(interaction.user.id);
            if (!existingAccount.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Database Error', 'There was an error checking your account status. Please try again later.', true);
                return;
            }
            if (existingAccount.data) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Account Registration', '❌ You have already registered an account!', true);
                return;
            }
            const modal = new discord_js_1.ModalBuilder()
                .setCustomId('modal-register')
                .setTitle('Account Registration');
            const usernameInput = new discord_js_1.TextInputBuilder()
                .setCustomId('reg-name')
                .setLabel('Username (UCP)')
                .setStyle(discord_js_1.TextInputStyle.Short)
                .setMinLength(4)
                .setMaxLength(15)
                .setPlaceholder('Enter your username (letters only)')
                .setRequired(true);
            const firstActionRow = new discord_js_1.ActionRowBuilder()
                .addComponents(usernameInput);
            modal.addComponents(firstActionRow);
            await interaction.showModal(modal);
        }
        catch (error) {
            if (!interaction.replied && !interaction.deferred) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Error', 'An unexpected error occurred. Please try again later.', true);
            }
            const client = interaction.client;
            client.logger.error('Error in register button', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
        }
    },
};
exports.default = registerButton;
//# sourceMappingURL=registerButton.js.map