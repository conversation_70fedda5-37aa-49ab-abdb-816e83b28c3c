import { Bot<PERSON>lient } from './BotClient';
import { Logger } from '../types/common';
/**
 * Command deployment utility for registering slash commands with Discord
 */
export declare class CommandDeployer {
    private rest;
    private client;
    private logger;
    constructor(client: BotClient, logger: Logger);
    /**
     * Deploy commands to Discord (guild-specific)
     */
    deployGuildCommands(): Promise<void>;
    /**
     * Deploy commands globally (takes up to 1 hour to propagate)
     */
    deployGlobalCommands(): Promise<void>;
    /**
     * Delete all guild commands
     */
    deleteGuildCommands(): Promise<void>;
    /**
     * Delete all global commands
     */
    deleteGlobalCommands(): Promise<void>;
    /**
     * Get commands data for deployment
     */
    private getCommandsData;
    /**
     * Get existing commands from Discord
     */
    getExistingGuildCommands(): Promise<unknown[]>;
    /**
     * Get existing global commands from Discord
     */
    getExistingGlobalCommands(): Promise<unknown[]>;
}
//# sourceMappingURL=CommandDeployer.d.ts.map