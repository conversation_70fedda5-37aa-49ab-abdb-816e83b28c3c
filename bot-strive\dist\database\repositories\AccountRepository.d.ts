import { DatabasePool, Account, CreateAccountInput, UpdateAccountInput } from '../../types/database';
import { Logger, AsyncResult } from '../../types/common';
/**
 * Repository for account-related database operations
 */
export declare class AccountRepository {
    private db;
    private logger;
    constructor(db: DatabasePool, logger: Logger);
    /**
     * Find an account by Discord ID
     */
    findByDiscordId(discordId: string): AsyncResult<Account | null>;
    /**
     * Find an account by username
     */
    findByUsername(username: string): AsyncResult<Account | null>;
    /**
     * Create a new account
     */
    create(accountData: CreateAccountInput): AsyncResult<Account>;
    /**
     * Update an account
     */
    update(id: number, updateData: UpdateAccountInput): AsyncResult<Account>;
    /**
     * Delete an account completely
     */
    delete(id: number): AsyncResult<boolean>;
    /**
     * Update account password
     */
    updatePassword(id: number, hashedPassword: string): AsyncResult<boolean>;
    /**
     * Check account verification status for debugging
     */
    checkAccountStatus(username: string): AsyncResult<any>;
    /**
     * Find an account by ID
     */
    findById(id: number): AsyncResult<Account | null>;
}
//# sourceMappingURL=AccountRepository.d.ts.map