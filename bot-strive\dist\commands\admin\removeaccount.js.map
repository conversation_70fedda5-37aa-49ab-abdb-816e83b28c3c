{"version": 3, "file": "removeaccount.js", "sourceRoot": "", "sources": ["../../../src/commands/admin/removeaccount.ts"], "names": [], "mappings": ";;;AAAA,2CAAiH;AAEjH,qFAAkF;AAClF,iDAAkD;AAClD,+CAAsD;AAOzC,QAAA,OAAO,GAAY;IAC9B,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC5B,OAAO,CAAC,eAAe,CAAC;SACxB,cAAc,CAAC,oDAAoD,CAAC;SACpE,gBAAgB,CAAC,MAAM,CAAC,EAAE,CACzB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;SACtB,cAAc,CAAC,0DAA0D,CAAC;SAC1E,WAAW,CAAC,IAAI,CAAC,CACrB;SACA,eAAe,CAAC,MAAM,CAAC,EAAE,CACxB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;SACvB,cAAc,CAAC,mCAAmC,CAAC;SACnD,WAAW,CAAC,KAAK,CAAC,CACtB;SACA,aAAa,CAAC,MAAM,CAAC,EAAE,CACtB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;SACnB,cAAc,CAAC,sCAAsC,CAAC;SACtD,WAAW,CAAC,KAAK,CAAC,CACtB;SACA,eAAe,CAAC,MAAM,CAAC,EAAE,CACxB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;SACrB,cAAc,CAAC,4BAA4B,CAAC;SAC5C,WAAW,CAAC,KAAK,CAAC,CACtB;SACA,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IAEjE,KAAK,CAAC,OAAO,CAAC,WAAwC,EAAE,MAAkB;QACxE,MAAM,WAAW,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAK,WAAW,CAAC,MAAc,CAAC;YACxD,MAAM,WAAW,GAAG,IAAI,qCAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YAE1E,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC3D,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,oBAAoB,CAAC;YAC/E,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAEhE,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC3E,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,eAAe,EACf,mDAAmD,EACnD,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,uBAAuB,EACvB,2IAA2I,EAC3I,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBACvB,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,eAAe,EACf,gEAAgE,EAChE,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,eAAe,CAAC;YACpB,IAAI,UAAU,GAAG,EAAE,CAAC;YAEpB,IAAI,QAAQ,EAAE,CAAC;gBACb,eAAe,GAAG,MAAM,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAC7D,UAAU,GAAG,eAAe,QAAQ,IAAI,CAAC;YAC3C,CAAC;iBAAM,IAAI,IAAI,EAAE,CAAC;gBAChB,eAAe,GAAG,MAAM,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC7D,UAAU,GAAG,iBAAiB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,eAAe,EACf,gCAAgC,EAChC,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC7B,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,gBAAgB,EAChB,kEAAkE,EAClE,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;gBAC1B,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,mBAAmB,EACnB,2BAA2B,UAAU,EAAE,EACvC,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC;YAErC,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE1D,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC1B,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,iBAAiB,EACjB,uDAAuD,EACvD,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,yBAAY,EAAE;iBACpC,QAAQ,CAAC,gCAAgC,CAAC;iBAC1C,cAAc,CAAC,aAAa,QAAQ,mDAAmD,CAAC;iBACxF,QAAQ,CAAC,qBAAW,CAAC,OAAO,CAAC;iBAC7B,SAAS,CAAC;gBACT;oBACE,IAAI,EAAE,oBAAoB;oBAC1B,KAAK,EAAE;wBACL,mBAAmB,OAAO,CAAC,QAAQ,IAAI;wBACvC,qBAAqB,OAAO,CAAC,EAAE,IAAI;wBACnC,qBAAqB,OAAO,CAAC,UAAU,IAAI;wBAC3C,uBAAuB,OAAO,CAAC,YAAY,IAAI;qBAChD,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,iBAAiB;oBACvB,KAAK,EAAE;wBACL,cAAc,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE;wBACzC,mBAAmB,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI;wBAC1C,eAAe,MAAM,EAAE;wBACvB,qBAAqB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK;qBACxD,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;aACF,CAAC;iBACD,YAAY,EAAE;iBACd,SAAS,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC,CAAC;YAEjD,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAExD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBAChD,cAAc,EAAE;oBACd,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,SAAS,EAAE,OAAO,CAAC,UAAU;oBAC7B,WAAW,EAAE,OAAO,CAAC,YAAY;iBAClC;gBACD,KAAK,EAAE;oBACL,EAAE,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;oBACvB,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;iBACpC;gBACD,MAAM;gBACN,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,MAAM,IAAK,WAAW,CAAC,MAAc,CAAC;YACxD,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBACvD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,KAAK,EAAG,KAAe,CAAC,KAAK;gBAC7B,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC3B,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC;aACpD,CAAC,CAAC;YAEH,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,eAAe,EACf,0DAA0D,EAC1D,IAAI,CACL,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC"}