"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const embeds_1 = require("../../utils/embeds");
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('clearcache')
        .setDescription('Clear bot internal cache and reset connections (Admin only)')
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    async execute(interaction, client) {
        if (!client) {
            await interaction.reply({
                content: 'Client not available',
                flags: 64
            });
            return;
        }
        // Check if user has administrator permissions
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            const errorEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('Access Denied')
                .setDescription('You need Administrator permissions to use this command.')
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp();
            await interaction.reply({ embeds: [errorEmbed], flags: 64 });
            return;
        }
        await embeds_1.InteractionUtils.deferReply(interaction, true);
        try {
            const startTime = Date.now();
            // Store original counts
            const originalCounts = {
                commands: client.commands.size,
                buttons: client.buttons.size,
                modals: client.modals.size,
                monitoringMetrics: client.monitoring ? Object.keys(client.monitoring.getMetrics()).length : 0
            };
            // Clear bot internal cache
            client.commands.clear();
            client.buttons.clear();
            client.modals.clear();
            // Reset monitoring metrics if available
            if (client.monitoring) {
                // Create new monitoring instance to reset metrics
                const { MonitoringSystem } = await Promise.resolve().then(() => __importStar(require('../../utils/monitoring')));
                client.monitoring.stop();
                client.monitoring = new MonitoringSystem(client, client.logger);
                client.monitoring.start();
            }
            // Reload all handlers after clearing cache
            const { HandlerManager } = await Promise.resolve().then(() => __importStar(require('../../client/HandlerManager')));
            const handlerManager = new HandlerManager(client, client.logger);
            await handlerManager.loadCommands();
            await handlerManager.loadButtons();
            await handlerManager.loadModals();
            // Deploy commands to Discord after reloading
            let commandsDeployed = false;
            const deployer = client.commandDeployer;
            if (deployer) {
                try {
                    await deployer.deployGuildCommands();
                    commandsDeployed = true;
                }
                catch (deployError) {
                    client.logger.warn('Failed to deploy commands after cache clear', {
                        error: deployError.message
                    });
                }
            }
            // Clear database connection pool if available
            let dbPoolCleared = false;
            if (client.db) {
                try {
                    const database = client.db;
                    // Force close all connections and recreate pool
                    await database.end();
                    // Recreate database connection
                    const { DatabaseManager } = await Promise.resolve().then(() => __importStar(require('../../database/connection')));
                    const configManager = await Promise.resolve().then(() => __importStar(require('../../config')));
                    const config = configManager.getConfigManager(client.logger);
                    const dbConfig = config.getDatabaseConfig();
                    const newDatabase = new DatabaseManager(dbConfig, client.logger);
                    // Test new connection with multiple queries
                    const testQuery = await newDatabase.query('SELECT 1 as test');
                    const tableTest = await newDatabase.query('SELECT COUNT(*) as count FROM accounts LIMIT 1');
                    if (testQuery.success && tableTest.success) {
                        // Delete the old property first to avoid "Cannot redefine property" error
                        delete client.db;
                        // Replace old database with new one
                        Object.defineProperty(client, 'db', {
                            value: newDatabase,
                            writable: false,
                            enumerable: true,
                            configurable: true // Make it configurable to allow future updates
                        });
                        dbPoolCleared = true;
                        client.logger.info('Database connection recreated successfully');
                    }
                    else {
                        client.logger.error('Database connection test failed after recreation');
                    }
                }
                catch (dbError) {
                    client.logger.error('Failed to clear database pool', {
                        error: dbError.message
                    });
                    // Try to recreate database connection without clearing the old one
                    try {
                        const { DatabaseManager } = await Promise.resolve().then(() => __importStar(require('../../database/connection')));
                        const configManager = await Promise.resolve().then(() => __importStar(require('../../config')));
                        const config = configManager.getConfigManager(client.logger);
                        const dbConfig = config.getDatabaseConfig();
                        const fallbackDatabase = new DatabaseManager(dbConfig, client.logger);
                        const fallbackTest = await fallbackDatabase.query('SELECT 1 as test');
                        if (fallbackTest.success) {
                            delete client.db;
                            Object.defineProperty(client, 'db', {
                                value: fallbackDatabase,
                                writable: false,
                                enumerable: true,
                                configurable: true
                            });
                            dbPoolCleared = true;
                            client.logger.info('Database connection recreated via fallback');
                        }
                    }
                    catch (fallbackError) {
                        client.logger.error('Fallback database recreation also failed', {
                            error: fallbackError.message
                        });
                    }
                }
            }
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            const endTime = Date.now();
            const clearTime = endTime - startTime;
            const embed = new discord_js_1.EmbedBuilder()
                .setTitle('Cache Cleared Successfully')
                .setDescription('All bot internal cache has been cleared and connections reset!')
                .setColor(discord_1.EmbedColors.SUCCESS)
                .addFields([
                {
                    name: 'Cleared Items',
                    value: [
                        `Commands: ${originalCounts.commands} cleared → ${client.commands.size} reloaded`,
                        `Buttons: ${originalCounts.buttons} cleared → ${client.buttons.size} reloaded`,
                        `Modals: ${originalCounts.modals} cleared → ${client.modals.size} reloaded`,
                        `Discord Commands: ${commandsDeployed ? 'Deployed' : 'Deployment failed'}`,
                        `Monitoring: ${client.monitoring ? 'Reset' : 'Not available'}`,
                        `Database Pool: ${dbPoolCleared ? 'Recreated' : 'Skipped'}`,
                        `Memory: ${global.gc ? 'Garbage collected' : 'GC not available'}`
                    ].join('\n'),
                    inline: false
                },
                {
                    name: 'Performance',
                    value: [
                        `Clear Time: ${clearTime}ms`,
                        `Memory Usage: ${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB`,
                        `Heap Used: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`
                    ].join('\n'),
                    inline: false
                },
                {
                    name: 'Next Steps',
                    value: [
                        'Use `/reload` to reload commands and handlers',
                        'Monitor bot performance for improvements',
                        'Check logs for any connection issues'
                    ].join('\n'),
                    inline: false
                }
            ])
                .setTimestamp()
                .setFooter({
                text: `Cache cleared by ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            });
            await interaction.editReply({ embeds: [embed] });
            // Log the cache clear action
            client.logger.info('Cache cleared by admin', {
                adminId: interaction.user.id,
                adminUsername: interaction.user.username,
                guildId: interaction.guildId,
                originalCounts,
                newCounts: {
                    commands: client.commands.size,
                    buttons: client.buttons.size,
                    modals: client.modals.size
                },
                commandsDeployed,
                dbPoolCleared,
                clearTime,
                memoryAfter: process.memoryUsage()
            });
        }
        catch (error) {
            client.logger.error('Error during cache clear command', {
                error: error.message,
                stack: error.stack,
                adminId: interaction.user.id,
                guildId: interaction.guildId
            });
            const errorEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('Cache Clear Failed')
                .setDescription('An error occurred while clearing cache.')
                .addFields([
                {
                    name: 'Error Details',
                    value: `\`\`\`${error.message}\`\`\``,
                    inline: false
                }
            ])
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp();
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            }
            else {
                await interaction.reply({ embeds: [errorEmbed], flags: 64 });
            }
        }
    },
};
//# sourceMappingURL=clearcache.js.map