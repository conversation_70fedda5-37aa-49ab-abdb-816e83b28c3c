import { Config, DatabaseConfig, SampServerConfig } from '../types/config';
import { WebhookConfig } from '../types/discord';
import { Logger } from '../types/common';
/**
 * Configuration manager with validation and type safety
 */
export declare class ConfigManager {
    private config;
    private logger;
    constructor(logger?: Logger);
    /**
     * Load and validate configuration from environment variables
     */
    private loadAndValidateConfig;
    /**
     * Get the complete configuration
     */
    getConfig(): Config;
    /**
     * Get Discord configuration
     */
    getDiscordConfig(): {
        token: string;
        clientId: string;
        guildId: string;
    };
    /**
     * Get database configuration
     */
    getDatabaseConfig(): DatabaseConfig;
    /**
     * Get SAMP server configuration
     */
    getSampServerConfig(): SampServerConfig;
    /**
     * Get webhook configuration for error logging
     */
    getWebhookConfig(): WebhookConfig | null;
    /**
     * Check if the bot is in development mode
     */
    isDevelopment(): boolean;
    /**
     * Check if the bot is in production mode
     */
    isProduction(): boolean;
    /**
     * Check if the bot is in test mode
     */
    isTest(): boolean;
    /**
     * Get a specific configuration value
     */
    get<K extends keyof Config>(key: K): Config[K];
    /**
     * Reload configuration (useful for testing)
     */
    reload(): void;
    /**
     * Validate that all required configuration is present
     */
    validateRequiredConfig(): void;
}
export declare const getConfigManager: (logger?: Logger) => ConfigManager;
export declare const getConfig: (logger?: Logger) => Config;
//# sourceMappingURL=index.d.ts.map