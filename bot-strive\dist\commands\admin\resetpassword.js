"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const AccountRepository_1 = require("../../database/repositories/AccountRepository");
const embeds_1 = require("../../utils/embeds");
const bcrypt = __importStar(require("bcrypt"));
const crypto = __importStar(require("crypto"));
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('resetpassword')
        .setDescription('Reset user password with secure bcrypt hashing (Admin only)')
        .addUserOption(option => option.setName('user')
        .setDescription('User whose password to reset')
        .setRequired(true))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    async execute(interaction, client) {
        if (!client) {
            await interaction.reply({
                content: 'Client not available',
                flags: 64
            });
            return;
        }
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            const errorEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('Access Denied')
                .setDescription('You need Administrator permissions to use this command.')
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp();
            await interaction.reply({ embeds: [errorEmbed], flags: 64 });
            return;
        }
        const targetUser = interaction.options.getUser('user', true);
        await embeds_1.InteractionUtils.deferReply(interaction, true);
        try {
            const database = client.db;
            if (!database) {
                throw new Error('Database connection not available');
            }
            const accountRepo = new AccountRepository_1.AccountRepository(database, client.logger);
            const accountResult = await accountRepo.findByDiscordId(targetUser.id);
            if (!accountResult.success) {
                throw new Error(`Database error: ${accountResult.error}`);
            }
            if (!accountResult.data) {
                const notFoundEmbed = new discord_js_1.EmbedBuilder()
                    .setTitle('User Not Found')
                    .setDescription(`User ${targetUser.username} is not registered in the system.`)
                    .setColor(discord_1.EmbedColors.WARNING)
                    .addFields([
                    {
                        name: 'Available Actions',
                        value: 'User must register first using the registration panel.',
                        inline: false
                    }
                ])
                    .setTimestamp();
                await interaction.editReply({ embeds: [notFoundEmbed] });
                return;
            }
            const account = accountResult.data;
            // Check if user has set password ingame
            if (!account.password) {
                const noPasswordEmbed = new discord_js_1.EmbedBuilder()
                    .setTitle('Password Not Set')
                    .setDescription(`User ${targetUser.username} has not set a password ingame yet.`)
                    .setColor(discord_1.EmbedColors.WARNING)
                    .addFields([
                    {
                        name: 'Account Status',
                        value: [
                            `**Username:** ${account.username}`,
                            `**Registration:** Complete`,
                            `**Password:** Not set (ingame registration pending)`
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: 'Next Steps',
                        value: 'User must complete ingame registration first before password can be reset.',
                        inline: false
                    }
                ])
                    .setTimestamp();
                await interaction.editReply({ embeds: [noPasswordEmbed] });
                return;
            }
            const tempPassword = generateSecurePassword();
            const saltRounds = 12;
            const hashedPassword = await bcrypt.hash(tempPassword, saltRounds);
            const escapedPassword = database.escape(hashedPassword);
            const escapedId = database.escape(account.id);
            const updateResult = await database.query(`UPDATE accounts SET password = ${escapedPassword} WHERE id = ${escapedId}`);
            if (!updateResult.success) {
                throw new Error(`Failed to update password: ${updateResult.error}`);
            }
            let dmSent = false;
            try {
                const dmEmbed = new discord_js_1.EmbedBuilder()
                    .setTitle('Password Reset - Strive Roleplay')
                    .setDescription('Your account password has been reset by an administrator.')
                    .setColor(discord_1.EmbedColors.INFO)
                    .addFields([
                    {
                        name: 'New Temporary Password',
                        value: `\`${tempPassword}\``,
                        inline: false
                    },
                    {
                        name: 'Important Security Notes',
                        value: [
                            '• Change this password immediately after logging in',
                            '• Do not share this password with anyone',
                            '• This password is securely hashed in our database',
                            '• Delete this message after copying the password'
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: 'Server Information',
                        value: `**IP:** ${client.config.SAMP_SERVER_IP}:${client.config.SAMP_SERVER_PORT}`,
                        inline: false
                    }
                ])
                    .setTimestamp();
                if (client.user?.displayAvatarURL()) {
                    dmEmbed.setFooter({
                        text: 'Strive Roleplay - Secure Password Reset',
                        iconURL: client.user.displayAvatarURL()
                    });
                }
                else {
                    dmEmbed.setFooter({
                        text: 'Strive Roleplay - Secure Password Reset'
                    });
                }
                await targetUser.send({ embeds: [dmEmbed] });
                dmSent = true;
            }
            catch (dmError) {
                client.logger.warn('Failed to send DM to user', {
                    userId: targetUser.id,
                    error: dmError.message
                });
            }
            const successEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('Password Reset Successful')
                .setDescription(`Password has been reset for user ${targetUser.username}`)
                .setColor(discord_1.EmbedColors.SUCCESS)
                .addFields([
                {
                    name: 'User Information',
                    value: [
                        `**Discord:** ${targetUser.username} (${targetUser.id})`,
                        `**Game Account:** ${account.username}`,
                        `**Account ID:** ${account.id}`
                    ].join('\n'),
                    inline: false
                },
                {
                    name: 'Security Details',
                    value: [
                        `**Hash Algorithm:** bcrypt (${saltRounds} rounds)`,
                        `**Password Length:** ${tempPassword.length} characters`,
                        `**DM Status:** ${dmSent ? 'Sent successfully' : 'Failed to send'}`,
                        `**Database:** Updated successfully`
                    ].join('\n'),
                    inline: false
                },
                {
                    name: 'Next Steps',
                    value: dmSent
                        ? 'User has been notified via DM with the new password.'
                        : `**Manual delivery required:** \`${tempPassword}\`\n⚠️ DM failed - provide password manually and ask user to change it immediately.`,
                    inline: false
                }
            ])
                .setTimestamp()
                .setFooter({
                text: `Reset by ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            });
            await interaction.editReply({ embeds: [successEmbed] });
            client.logger.info('Password reset completed', {
                adminId: interaction.user.id,
                adminUsername: interaction.user.username,
                targetUserId: targetUser.id,
                targetUsername: targetUser.username,
                accountId: account.id,
                gameUsername: account.username,
                guildId: interaction.guildId,
                dmSent,
                hashAlgorithm: 'bcrypt',
                saltRounds,
                passwordLength: tempPassword.length,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            client.logger.error('Error in reset password command', {
                error: error.message,
                stack: error.stack,
                adminId: interaction.user.id,
                targetUserId: targetUser.id,
                guildId: interaction.guildId
            });
            const errorEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('Password Reset Failed')
                .setDescription('An error occurred while resetting the password.')
                .addFields([
                {
                    name: 'Error Details',
                    value: `\`\`\`${error.message}\`\`\``,
                    inline: false
                },
                {
                    name: 'Troubleshooting',
                    value: [
                        '• Check if user is registered in the system',
                        '• Verify database connection is stable',
                        '• Ensure user has DMs enabled',
                        '• Contact development team if issue persists'
                    ].join('\n'),
                    inline: false
                }
            ])
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp();
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            }
            else {
                await interaction.reply({ embeds: [errorEmbed], flags: 64 });
            }
        }
    },
};
/**
 * Generate a secure temporary password
 * Compatible with SAMP requirements and user-friendly
 */
function generateSecurePassword() {
    const length = 12;
    const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let password = '';
    for (let i = 0; i < length; i++) {
        const randomIndex = crypto.randomInt(0, charset.length);
        password += charset[randomIndex];
    }
    return password;
}
//# sourceMappingURL=resetpassword.js.map