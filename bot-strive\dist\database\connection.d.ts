import { <PERSON>Pool, DatabaseConnection, QueryResult } from '../types/database';
import { DatabaseConfig } from '../types/config';
import { Logger } from '../types/common';
/**
 * Database connection manager with connection pooling
 */
export declare class DatabaseManager implements DatabasePool {
    private pool;
    private logger;
    constructor(config: DatabaseConfig, logger: Logger);
    /**
     * Set up event handlers for the connection pool
     */
    private setupEventHandlers;
    /**
     * Execute a query with automatic error handling
     */
    query<T = unknown>(sql: string, values?: unknown[]): Promise<QueryResult<T>>;
    /**
     * Execute a query (alias for query method)
     */
    execute<T = unknown>(sql: string, values?: unknown[]): Promise<QueryResult<T>>;
    /**
     * Get a connection from the pool for transactions
     */
    getConnection(): Promise<DatabaseConnection>;
    /**
     * Escape a value for SQL queries (temporary fix for MySQL2 execute issue)
     */
    escape(value: unknown): string;
    /**
     * Close all connections in the pool
     */
    end(): Promise<void>;
    /**
     * Handle database errors and convert them to standardized format
     */
    private handleDatabaseError;
}
//# sourceMappingURL=connection.d.ts.map