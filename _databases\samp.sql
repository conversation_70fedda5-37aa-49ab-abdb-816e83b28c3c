-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 10, 2025 at 12:13 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `samp`
--

-- --------------------------------------------------------

--
-- Table structure for table `acc`
--

CREATE TABLE `acc` (
  `id` int(11) DEFAULT 0,
  `accID` int(11) NOT NULL,
  `accModel` int(11) DEFAULT 0,
  `accIndex` int(11) DEFAULT 0,
  `accBone` int(11) DEFAULT 0,
  `accUse` int(11) DEFAULT 0,
  `accPosX` float DEFAULT 0,
  `accPosY` float DEFAULT 0,
  `accPosZ` float DEFAULT 0,
  `accPosRX` float DEFAULT 0,
  `accPosRY` float DEFAULT 0,
  `accPosRZ` float DEFAULT 0,
  `accPosSX` float DEFAULT 0,
  `accPosSY` float DEFAULT 0,
  `accPosSZ` float DEFAULT 0,
  `accColor1` int(11) DEFAULT 0,
  `accColor2` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `acc`
--

INSERT INTO `acc` (`id`, `accID`, `accModel`, `accIndex`, `accBone`, `accUse`, `accPosX`, `accPosY`, `accPosZ`, `accPosRX`, `accPosRY`, `accPosRZ`, `accPosSX`, `accPosSY`, `accPosSZ`, `accColor1`, `accColor2`) VALUES
(22, 1, 19138, 0, 2, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, -1, -1),
(22, 2, 18912, 1, 2, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, -1, -1),
(22, 3, 18912, 2, 2, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, -1, -1);

-- --------------------------------------------------------

--
-- Table structure for table `accounts`
--

CREATE TABLE `accounts` (
  `id` int(11) NOT NULL,
  `username` varchar(24) DEFAULT NULL,
  `password` varchar(129) DEFAULT NULL,
  `email` varchar(32) DEFAULT 'Empty',
  `createdAt` varchar(36) DEFAULT NULL,
  `LoginDate` varchar(36) DEFAULT NULL,
  `IP` varchar(16) DEFAULT 'n/a',
  `Admin` int(11) DEFAULT 0,
  `AdminHide` int(11) DEFAULT 0,
  `VIP` int(11) DEFAULT 0,
  `VIPTime` int(11) DEFAULT 0,
  `VIPCoin` int(11) DEFAULT 0,
  `isVerified` tinyint(4) NOT NULL DEFAULT 0,
  `refresh_token` text DEFAULT NULL,
  `updatedAt` datetime NOT NULL,
  `discord_id` varchar(50) DEFAULT NULL,
  `discord_code` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `accounts`
--

INSERT INTO `accounts` (`id`, `username`, `password`, `email`, `createdAt`, `LoginDate`, `IP`, `Admin`, `AdminHide`, `VIP`, `VIPTime`, `VIPCoin`, `isVerified`, `refresh_token`, `updatedAt`, `discord_id`, `discord_code`) VALUES
(11, 'JhonAndreas', '$2b$12$sZ6F.MImRGMlT5X7CDWoOe2H5gRBUWXsP3vuJHqMVEP9rVzDJchoG', 'Empty', '03/07/2025, 21:48', '06/07/2025, 00:56', '************', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1033954340244828200', 655223),
(14, 'dflx', '$2b$12$QTidESL2NWgfTZwqImsto.ijknE.i61wxm3VJYY00tdAzlSp5HX/C', 'Empty', '03/07/2025, 21:57', '09/07/2025, 22:25', '*************', 7, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '325452398787035136', 196213),
(15, 'KakZah', '$2b$12$kVBOAjn5cJobLDt0eS1m/e4bDPz0V9zrpGiJXg24o5vd0.ifdqmbm', 'Empty', '03/07/2025, 21:55', '10/07/2025, 14:25', '**************', 8, 0, 3, **********, 9000, 1, NULL, '0000-00-00 00:00:00', '1233943449074667520', 700656),
(16, 'FanID', '$2b$12$Qe5W39t1IotP.dydYdd9xOkbi.qd6ksX1qqgo.ueAlEJZxQp3jRm.', 'Empty', '03/07/2025, 22:15', '10/07/2025, 16:15', '**************', 0, 0, 1, 1754156944, 0, 1, NULL, '0000-00-00 00:00:00', '1063983803309248592', 224410),
(23, 'Nimo', '$2b$12$Avh4cA6jHLazjdEBdjzs7eOONc6U..KT.tEi154q.0EqWl4J.c01y', 'Empty', '04/07/2025, 00:26', '10/07/2025, 04:26', '36.83.22.71', 8, 1, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1266792195835433061', 515171),
(26, 'kontolop', '$2b$12$3HyogZ2U1OhOOGFZiUk7Merd1PvCDJUCGCjI6LfW.s9uZSgMLPLma', 'Empty', '04/07/2025, 01:03', '04/07/2025, 23:12', '114.10.101.46', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1155881842310914079', 302861),
(27, 'shoegaze', '$2b$12$315FuE5lbWk308VNQF.MzuCf9cbNMNdiSIx/6cv7YdIaXRTFSMR0u', 'Empty', '04/07/2025, 01:04', '10/07/2025, 14:38', '103.171.183.77', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '572377785771753493', 596783),
(28, 'Gibran', '$2b$12$wbUqZ5cQthgmmmTDBJ1vbenOBklkTfzht9KAYwwDo.85H27Pn/4am', 'Empty', '04/07/2025, 09:50', '04/07/2025, 10:18', '180.241.34.105', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1389914302957228124', 486576),
(29, 'Nandogtg', '$2b$12$hbMW8DILlubEiwvo1SFai..nFFtqmTaCcQYDHwOdprMjvdJy2ldmW', 'Empty', '04/07/2025, 09:56', '10/07/2025, 15:26', '**************', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1388881402925285511', 153249),
(30, 'alidsma', '$2b$12$RwxunZO42Z96dHMYE9NEe.R637dGcO7JGObuSs6N6pg6c5hhha0pi', 'Empty', '04/07/2025, 10:14', '09/07/2025, 13:14', '**************', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1390529086262345728', 784603),
(31, 'Bili', '$2b$12$cQpXX1ff1VbHDjxcLZN00epACncfxk8pOWXMNYGinupLDohzSgW.O', 'Empty', '04/07/2025, 10:18', '09/07/2025, 11:42', '**************', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1390531519659114507', 66144),
(32, 'Gixco', '', 'Empty', '04/07/2025, 12:28', '04/07/2025, 12:28', 'n/a', 0, 0, 0, 0, 0, 0, NULL, '0000-00-00 00:00:00', '461066175410077697', 555360),
(33, 'KillerClown', '$2b$12$o6iWLDLFxMC5N.e6SiHZLOx7FndYCZMSHFCbkCOKB/z0.JRzkJ8HG', 'Empty', '04/07/2025, 18:18', '09/07/2025, 13:44', '103.171.83.125', 7, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '663383943332102167', 501915),
(34, 'Aldo', '$2b$12$5Ol7UnSwEYFXmpWsv3EIC.wvEpjNMk8SYIKycufr6wIYDJUAbX.FW', 'Empty', '04/07/2025, 23:58', '10/07/2025, 00:46', '**************', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1390736332179116062', 658781),
(35, 'Natrigaf', '$2b$12$62Lek5s5u7EUYn76/kxWbeUQpdsyrzZO0uprGVUM9SAlxUoqJ7h1m', 'Empty', '05/07/2025, 12:31', '09/07/2025, 11:45', '36.71.140.61', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '616214410922426380', 755913),
(36, 'workid0110', '$2b$12$Gr6K5/fRKBm8e/E7Ln3lvuulmPhE3sauNGtHlYnVxlCt4rmlmWu36', 'Empty', '05/07/2025, 16:57', '09/07/2025, 01:32', '**************', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1353621092484513898', 758924),
(37, 'Xylander', '$2b$12$oVifRH9O99V7j4QqcESh7OJ6iu3NOdF4NlyfE5mEbb.isexbLLGka', 'Empty', '05/07/2025, 23:27', '08/07/2025, 00:57', '103.3.221.79', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '940612410153254952', 54350),
(38, 'Lando', '', 'Empty', '06/07/2025, 08:34', '06/07/2025, 08:34', 'n/a', 0, 0, 0, 0, 0, 0, NULL, '0000-00-00 00:00:00', '805981644871434290', 652104),
(39, 'habibisd', '$2b$12$jJugRFhPHKHAjml4QmMTS.ohHNecj/dUaQOtolroeToz4WppktGHy', 'Empty', '06/07/2025, 12:29', '10/07/2025, 11:29', '**************', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1353614445615513641', 936547),
(40, 'Ucok1318', '$2b$12$VnayB5SZ2/IJXL73HzQ2x./sgTfKjx1vjn3ooy.UNptQ7TE/sTk0S', 'Empty', '06/07/2025, 13:16', '07/07/2025, 10:00', '182.1.237.77', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1390941995522068600', 8290),
(41, 'Nagase', '$2b$12$dzGevPzzQEWrb1GSFT4m0O0WZg1SzVnqAxvX8I3WWnhFpVyKeBwo2', 'Empty', '07/07/2025, 07:37', '07/07/2025, 07:39', '202.51.197.171', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1069130985188360233', 771546),
(42, 'Idoyy', '$2b$12$QA1rUvsdJhsZOFczJcjJKeuJ3sSdoESNXHS4rSF0CGR.65C.H626m', 'Empty', '08/07/2025, 00:25', '08/07/2025, 01:56', '125.160.100.43', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1391822505043558410', 373056),
(43, 'Sannn', '$2b$12$xeC7XjoCK5U78JeQYEAlROhjhvlCsfGBYVXi4ddUhoG1oJMfg9pCC', 'Empty', '08/07/2025, 00:24', '08/07/2025, 20:40', '125.160.111.167', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1391822551306604594', 729156),
(44, 'Kaffa', '$2b$12$xHsRyiXbZBt3J.2biZtaEO.byRT7DuqXepYOdx9ZubDfO8AzzRodu', 'Empty', '08/07/2025, 00:32', '08/07/2025, 00:32', 'n/a', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1391822534663733259', 780496),
(45, 'BabaUcok', '$2b$12$BdmaIVruVej9x51XwjFMDuFtBlCMyD6N.5Eoy76vbYuhre0EfE996', 'Empty', '08/07/2025, 01:23', '10/07/2025, 15:09', '182.2.166.134', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '980174062124941363', 744197),
(46, 'Lonely', '$2b$12$Q1Tg7NxuZN0u5OXRLWf9U..eytDRQ7Pu.RuwC2TGTMNNEke/3B46e', 'Empty', '09/07/2025, 00:21', '09/07/2025, 00:21', 'n/a', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '452801392411803669', 683334),
(47, 'wokno', '$2b$12$uWYD3fVF/M/vxjZKKOOAYOr/ItV2GI2mxZfWdFBnI3r.m/xdaDhba', 'Empty', '09/07/2025, 01:51', '09/07/2025, 01:51', 'n/a', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '1368655248083910687', 911807),
(48, 'Djaa', '$2b$12$CgfKTVaUZiJseGPLDiJSEe1/82GpkbMUlpVaGdKAjYsdjk7wKjW0O', 'Empty', '09/07/2025, 22:28', '09/07/2025, 22:28', 'n/a', 0, 0, 0, 0, 0, 1, NULL, '0000-00-00 00:00:00', '769601319693451325', 231441);

-- --------------------------------------------------------

--
-- Table structure for table `actor`
--

CREATE TABLE `actor` (
  `actorID` int(11) NOT NULL,
  `actorModel` int(11) DEFAULT 0,
  `actorPosX` float DEFAULT 0,
  `actorPosY` float DEFAULT 0,
  `actorPosZ` float DEFAULT 0,
  `actorPosA` float DEFAULT 0,
  `actorName` varchar(26) DEFAULT 'Stranger',
  `actorVW` int(11) DEFAULT 0,
  `actorHealth` float DEFAULT 100,
  `actorAnim` int(11) DEFAULT 0,
  `actorAnimLib` varchar(64) DEFAULT 'None',
  `actorAnimName` varchar(64) DEFAULT 'None',
  `actorDelta` float DEFAULT 0,
  `actorLoop` int(11) DEFAULT 0,
  `actorLockx` int(11) DEFAULT 0,
  `actorLocky` int(11) DEFAULT 0,
  `actorFreeze` int(11) DEFAULT 0,
  `actorTime` int(11) DEFAULT 0,
  `actorBiz` int(11) DEFAULT -1,
  `actorDelayRob` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `actor`
--

INSERT INTO `actor` (`actorID`, `actorModel`, `actorPosX`, `actorPosY`, `actorPosZ`, `actorPosA`, `actorName`, `actorVW`, `actorHealth`, `actorAnim`, `actorAnimLib`, `actorAnimName`, `actorDelta`, `actorLoop`, `actorLockx`, `actorLocky`, `actorFreeze`, `actorTime`, `actorBiz`, `actorDelayRob`) VALUES
(1, 135, 2861.02, -1996.5, 10.9342, 179.826, 'Pull Waller si Pengepul', 0, 100, 1, 'MISC', 'Seat_talk_01', 4.0998, 1, 0, 0, 1, 0, -1, 0),
(3, 71, 2304.1, -2303.87, 13.5467, 131.731, 'Security Mechanic Cente', 0, 100, 0, 'COP_AMBIENT', 'Coplook_loop', 4.0999, 1, 0, 0, 0, 1, -1, 0),
(4, 71, 1496.81, -1184.86, 23.9974, 271.914, 'Security 1', 0, 100, 0, '', '', 0, 0, 0, 0, 0, 0, -1, 0),
(5, 71, 1496.87, -1178.51, 24.0781, 262.2, 'Security 2', 0, 100, 0, '', '', 0, 0, 0, 0, 0, 0, -1, 0),
(6, 71, 1404.39, -17.5717, 1000.92, 89.1384, 'Security 3', 3, 100, 0, '', '', 0, 0, 0, 0, 0, 0, -1, 0),
(7, 141, 1401.02, -27.481, 1000.92, 44.813, 'Teller Bank', 3, 100, 0, 'MISC', 'Seat_talk_01', 4.0999, 1, 0, 0, 1, 0, -1, 0);

-- --------------------------------------------------------

--
-- Table structure for table `animallots`
--

CREATE TABLE `animallots` (
  `animalID` int(11) NOT NULL,
  `animalType` int(11) DEFAULT 0,
  `animalHealth` float DEFAULT 100,
  `animalDeath` int(11) DEFAULT 0,
  `animalTime` int(11) DEFAULT 0,
  `animalPosX` int(11) DEFAULT 0,
  `animalPosY` int(11) DEFAULT 0,
  `animalPosZ` int(11) DEFAULT 0,
  `animalPosRX` int(11) DEFAULT 0,
  `animalPosRY` int(11) DEFAULT 0,
  `animalPosRZ` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `arrestpoints`
--

CREATE TABLE `arrestpoints` (
  `arrestID` int(11) NOT NULL,
  `arrestX` float DEFAULT 0,
  `arrestY` float DEFAULT 0,
  `arrestZ` float DEFAULT 0,
  `arrestInterior` int(11) DEFAULT 0,
  `arrestWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `atm_machines`
--

CREATE TABLE `atm_machines` (
  `id` int(11) NOT NULL,
  `x` float NOT NULL DEFAULT 0,
  `y` float NOT NULL DEFAULT 0,
  `z` float NOT NULL DEFAULT 0,
  `rx` float NOT NULL DEFAULT 0,
  `ry` float NOT NULL DEFAULT 0,
  `rz` float NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `interior` int(11) DEFAULT 0,
  `virtualworld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `atm_machines`
--

INSERT INTO `atm_machines` (`id`, `x`, `y`, `z`, `rx`, `ry`, `rz`, `created_at`, `updated_at`, `interior`, `virtualworld`) VALUES
(15, -34.8815, -57.9818, 1003.17, 0, 0, 178.5, '2025-07-06 20:19:26', '2025-07-08 06:11:31', 6, 6006),
(16, -36.0293, -31.0228, 1003.18, 0, 0, 89.4999, '2025-07-06 20:20:50', '2025-07-09 06:11:49', 4, 6014),
(17, 376.727, 167.113, 1008, 0, 0, 180, '2025-07-06 20:22:46', '2025-07-06 20:48:25', 3, 7001),
(18, 1494.32, 1309.61, 1092.88, 0, 0, 0, '2025-07-06 20:23:54', '2025-07-06 20:48:25', 3, 6002),
(19, -35.0037, -58.0013, 1003.18, 0, 0, -178.3, '2025-07-07 18:24:55', '2025-07-09 06:11:49', 6, 6010),
(20, 1398.69, -12.1281, 1000.55, 0, 0, 0, '2025-07-08 06:08:27', '2025-07-09 06:11:49', 3, 3),
(21, 1401.53, -12.121, 1000.57, 0, 0, 0, '2025-07-08 06:09:00', '2025-07-09 06:11:49', 3, 3);

-- --------------------------------------------------------

--
-- Table structure for table `backpackitems`
--

CREATE TABLE `backpackitems` (
  `id` int(11) DEFAULT 0,
  `itemID` int(11) NOT NULL,
  `itemName` varchar(32) DEFAULT NULL,
  `itemModel` int(11) DEFAULT 0,
  `itemQuantity` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `backpacks`
--

CREATE TABLE `backpacks` (
  `backpackID` int(11) NOT NULL,
  `backpackPlayer` int(11) DEFAULT 0,
  `backpackX` float DEFAULT 0,
  `backpackY` float DEFAULT 0,
  `backpackZ` float DEFAULT 0,
  `backpackInterior` int(11) DEFAULT 0,
  `backpackWorld` int(11) DEFAULT 0,
  `backpackHouse` int(11) DEFAULT 0,
  `backpackVehicle` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `billboards`
--

CREATE TABLE `billboards` (
  `bbID` int(11) NOT NULL,
  `bbExists` int(11) DEFAULT 0,
  `bbName` varchar(32) DEFAULT NULL,
  `bbOwner` int(11) NOT NULL DEFAULT 0,
  `bbPrice` int(11) NOT NULL DEFAULT 0,
  `bbRange` int(11) DEFAULT 10,
  `bbPosX` float DEFAULT 0,
  `bbPosY` float DEFAULT 0,
  `bbPosZ` float DEFAULT 0,
  `bbMessage` varchar(230) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bizvipdealer`
--

CREATE TABLE `bizvipdealer` (
  `vipID` int(11) NOT NULL,
  `vipName` varchar(32) DEFAULT NULL,
  `vipPosX` float DEFAULT 0,
  `vipPosY` float DEFAULT 0,
  `vipPosZ` float DEFAULT 0,
  `vipPosA` float DEFAULT 0,
  `vipExterior` int(11) DEFAULT 0,
  `vipExteriorVW` int(11) DEFAULT 0,
  `vipProducts` int(11) DEFAULT 0,
  `vipSpawnX` float DEFAULT 0,
  `vipSpawnY` float DEFAULT 0,
  `vipSpawnZ` float DEFAULT 0,
  `vipSpawnA` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `bizvipdealer`
--

INSERT INTO `bizvipdealer` (`vipID`, `vipName`, `vipPosX`, `vipPosY`, `vipPosZ`, `vipPosA`, `vipExterior`, `vipExteriorVW`, `vipProducts`, `vipSpawnX`, `vipSpawnY`, `vipSpawnZ`, `vipSpawnA`) VALUES
(1, 'VIP Dealership', 545.625, -1293.49, 17.242, 0.2585, 0, 0, 10, 555.452, -1282.93, 17.2482, 357.556);

-- --------------------------------------------------------

--
-- Table structure for table `blacklist`
--

CREATE TABLE `blacklist` (
  `IP` varchar(16) DEFAULT '0.0.0.0',
  `username` varchar(24) DEFAULT NULL,
  `BannedBy` varchar(24) DEFAULT NULL,
  `Reason` varchar(128) DEFAULT NULL,
  `Date` varchar(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `businesses`
--

CREATE TABLE `businesses` (
  `bizID` int(11) NOT NULL,
  `bizName` varchar(32) DEFAULT NULL,
  `bizOwner` int(11) DEFAULT 0,
  `bizType` int(11) DEFAULT 0,
  `bizPrice` int(11) DEFAULT 0,
  `bizPosX` float DEFAULT 0,
  `bizPosY` float DEFAULT 0,
  `bizPosZ` float DEFAULT 0,
  `bizPosA` float DEFAULT 0,
  `bizIntX` float DEFAULT 0,
  `bizIntY` float DEFAULT 0,
  `bizIntZ` float DEFAULT 0,
  `bizIntA` float DEFAULT 0,
  `bizInterior` int(11) DEFAULT 0,
  `bizExterior` int(11) DEFAULT 0,
  `bizExteriorVW` int(11) DEFAULT 0,
  `bizLocked` int(11) DEFAULT 0,
  `bizVault` int(11) DEFAULT 0,
  `bizProducts` int(11) DEFAULT 0,
  `bizPrice1` int(11) DEFAULT 0,
  `bizPrice2` int(11) DEFAULT 0,
  `bizPrice3` int(11) DEFAULT 0,
  `bizPrice4` int(11) DEFAULT 0,
  `bizPrice5` int(11) DEFAULT 0,
  `bizPrice6` int(11) DEFAULT 0,
  `bizPrice7` int(11) DEFAULT 0,
  `bizPrice8` int(11) DEFAULT 0,
  `bizPrice9` int(11) DEFAULT 0,
  `bizPrice10` int(11) DEFAULT 0,
  `bizSpawnX` float DEFAULT 0,
  `bizSpawnY` float DEFAULT 0,
  `bizSpawnZ` float DEFAULT 0,
  `bizSpawnA` float DEFAULT 0,
  `bizDeliverX` float DEFAULT 0,
  `bizDeliverY` float DEFAULT 0,
  `bizDeliverZ` float DEFAULT 0,
  `bizBuyPointX` float DEFAULT 0,
  `bizBuyPointY` float DEFAULT 0,
  `bizBuyPointZ` float DEFAULT 0,
  `bizMessage` varchar(128) DEFAULT NULL,
  `bizPrice11` int(11) DEFAULT 0,
  `bizPrice12` int(11) DEFAULT 0,
  `bizPrice13` int(11) DEFAULT 0,
  `bizPrice14` int(11) DEFAULT 0,
  `bizPrice15` int(11) DEFAULT 0,
  `bizPrice16` int(11) DEFAULT 0,
  `bizPrice17` int(11) DEFAULT 0,
  `bizPrice18` int(11) DEFAULT 0,
  `bizPrice19` int(11) DEFAULT 0,
  `bizPrice20` int(11) DEFAULT 0,
  `bizShipment` int(11) DEFAULT 0,
  `bizCargo` int(11) DEFAULT 0,
  `bizSeize` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `businesses`
--

INSERT INTO `businesses` (`bizID`, `bizName`, `bizOwner`, `bizType`, `bizPrice`, `bizPosX`, `bizPosY`, `bizPosZ`, `bizPosA`, `bizIntX`, `bizIntY`, `bizIntZ`, `bizIntA`, `bizInterior`, `bizExterior`, `bizExteriorVW`, `bizLocked`, `bizVault`, `bizProducts`, `bizPrice1`, `bizPrice2`, `bizPrice3`, `bizPrice4`, `bizPrice5`, `bizPrice6`, `bizPrice7`, `bizPrice8`, `bizPrice9`, `bizPrice10`, `bizSpawnX`, `bizSpawnY`, `bizSpawnZ`, `bizSpawnA`, `bizDeliverX`, `bizDeliverY`, `bizDeliverZ`, `bizBuyPointX`, `bizBuyPointY`, `bizBuyPointZ`, `bizMessage`, `bizPrice11`, `bizPrice12`, `bizPrice13`, `bizPrice14`, `bizPrice15`, `bizPrice16`, `bizPrice17`, `bizPrice18`, `bizPrice19`, `bizPrice20`, `bizShipment`, `bizCargo`, `bizSeize`) VALUES
(2, '((Newbie Dealer))', 0, 5, 1000000000, 1614.27, -2296.21, 13.5381, 1.8947, 1494.56, 1304.21, 1093.29, 0, 3, 0, 0, 0, 850000, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1613.25, -2315.74, 13.3781, 89.7589, 0, 0, 0, 1490.56, 1305.74, 1093.3, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(3, 'Electonic', 0, 9, 1000000000, 1847.36, -1871.7, 13.5781, 87.6116, -2240.5, 128.377, 1035.42, 270, 6, 0, 0, 0, 234600, 991, 7500, 1500, 1500, 10000, 5000, 15000, 10, 100, 20, 10, 1847.58, -1871.74, 13.5781, 92.0354, 0, 0, 0, -2237.31, 130.343, 1035.41, 'welcome jing', 140, 190, 150, 60, 50, 5, 10, 5, 0, 0, 0, 0, 0),
(5, 'Kak Zah Fast Food', 0, 4, 1000000000, 2105.15, -1806.5, 13.5544, 90.1427, 372.346, -133.077, 1001.49, 0.2615, 5, 0, 0, 0, 116195, 825, 300, 300, 500, 500, 400, 400, 200, 0, 0, 0, 2105.15, -1806.5, 13.5544, 90.1427, 2115.48, -1826.05, 13.5551, 375.754, -119.236, 1001.5, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(6, 'Unnamed Business', 0, 6, 1000000000, 1929.01, -1776.2, 13.5466, 272.794, -27.3383, -57.6907, 1003.55, 0, 6, 0, 0, 0, 102115, 21, 300, 300, 1000, 1000, 1500, 5000, 5000, 3500, 10, 500, 1929.01, -1776.2, 13.5466, 272.794, 1923.01, -1794.9, 13.3828, -22.7609, -55.4709, 1003.55, '', 10000, 150, 50, 40, 5, 10, 5, 0, 0, 0, 1, 0, 0),
(7, 'Boat Dealer', 2, 5, 100, 2861.27, -1989.15, 10.934, 90.8905, 1494.56, 1304.21, 1093.29, 0, 3, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2933.84, -2057.27, -0.4966, 266.545, 0, 0, 0, 1490.68, 1305.7, 1093.3, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(9, 'Trucker Dealer', 0, 5, 1000000000, -58.4603, -1112.46, 1.343, 73.2891, 1494.56, 1304.21, 1093.29, 0, 3, 0, 0, 0, 952000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -76.0485, -1109.39, 1.0779, 162.911, 0, 0, 0, 1490.67, 1305.63, 1093.3, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(10, 'Douglass Foundation', 13, 6, 1, -78.777, -1169.75, 2.1459, 67.622, -27.3383, -57.6907, 1003.55, 0, 6, 0, 0, 0, 47301, 49848, 150, 150, 100, 500, 700, 500, 1000, 1000, 50, 2000, -78.777, -1169.75, 2.1459, 67.622, -81.7154, -1175.45, 2.0697, -23.4948, -55.2985, 1003.55, 'Welcome to Gas Station Douglass Foundation, Hope you enjoy it.', 1500, 150, 50, 40, 5, 10, 5, 0, 0, 0, 0, 0, 0),
(11, 'Dealer Jeffersont', 0, 5, 1000000000, 2131.99, -1150.93, 24.1049, 355.532, 1494.56, 1304.21, 1093.29, 0, 3, 0, 0, 0, 1105000, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2118.99, -1154.8, 24.0993, 358.016, 0, 0, 0, 1490.64, 1305.55, 1093.3, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(12, 'Unnamed Business', 0, 7, 1000000000, 1978.8, -1761.59, 13.5468, 0.7947, -2240.5, 128.377, 1035.42, 270, 6, 0, 0, 0, 113, 7, 75, 115, 15, 95, 3, 2, 10, 100, 20, 10, 1978.8, -1761.59, 13.5468, 0.7947, 0, 0, 0, -2237.17, 130.341, 1035.41, '', 140, 190, 150, 60, 50, 5, 10, 5, 0, 0, 0, 0, 0),
(13, 'Dealer Rodeo', 0, 5, 1000000000, 391.112, -1374.66, 14.8097, 27.7201, 1494.56, 1304.21, 1093.29, 0, 3, 0, 0, 0, 425000, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 398.876, -1373.63, 14.8203, 34.7733, 0, 0, 0, 1490.63, 1305.68, 1093.3, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(14, 'Unnamed Business', 0, 1, 1000000000, 1684.84, -1582.83, 13.5468, 176.591, 203.866, -50.2609, 1001.8, 0.1643, 1, 0, 0, 0, 236, 3, 75, 125, 15, 100, 3, 2, 10, 100, 20, 10, 1592.37, -1862.64, 13.5271, 181.903, 0, 0, 0, 206.899, -40.7173, 1001.8, '', 150, 200, 160, 60, 50, 5, 10, 5, 0, 0, 0, 0, 0),
(15, 'Clothes Business', 0, 3, 1000000000, 2244.55, -1665.14, 15.4765, 342.515, 207.713, -110.797, 1005.13, 359.475, 15, 0, 0, 0, 160, 0, 25, 15, 10, 10, 0, 0, 0, 0, 0, 0, 2244.55, -1665.14, 15.4765, 342.515, 0, 0, 0, 208.86, -100.63, 1005.26, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `caracc`
--

CREATE TABLE `caracc` (
  `id` int(11) DEFAULT 0,
  `accID` int(11) NOT NULL,
  `accModel` int(11) DEFAULT -1,
  `accColor` int(11) DEFAULT 0,
  `accPosX` float DEFAULT 0,
  `accPosY` float DEFAULT 0,
  `accPosZ` float DEFAULT 0,
  `accPosRX` float DEFAULT 0,
  `accPosRY` float DEFAULT 0,
  `accPosRZ` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `caracc`
--

INSERT INTO `caracc` (`id`, `accID`, `accModel`, `accColor`, `accPosX`, `accPosY`, `accPosZ`, `accPosRX`, `accPosRY`, `accPosRZ`) VALUES
(26, 4, 1023, -1, 0, -2.2532, 0.2822, -1.6799, 0, 0),
(26, 5, 18652, -1, 0.945, 0.117, -0.6129, -1.0535, 0, 0),
(26, 6, 18652, -1, -1.01, 0.0307, -0.5643, -0.5732, 0, 0),
(26, 7, 1111, -1, 0, 2.2486, 0.0692, -13.2113, 0, 0),
(26, 9, 1153, -1, 0.9307, 1.8602, -0.16, 0, 0, 0),
(32, 13, 1060, -1, 0.0102, -0.2303, 0.8399, 0, 0, 0),
(46, 14, 18649, -1, -0.9609, 0.0004, -0.561, 0, 0, 0),
(46, 15, 18649, -1, 0.8808, 0.0007, -0.561, 0, 0, 0),
(46, 16, 1003, -1, 0, -2.3294, 0.1249, 358.867, 0, 0),
(56, 17, 1003, -1, 0, -2.3587, 0.1655, 359.111, 0, 0),
(56, 18, 18647, -1, 0.9006, 0.0006, -0.581, 0, 0, 0),
(56, 19, 18647, -1, -0.9309, 0.0007, -0.531, 0, 0, 0),
(57, 20, 1003, -1, 0, -2.3782, 0.1542, 358.183, 0, 0),
(57, 21, 18648, -1, 0.9409, 0.0007, -0.5311, 0, 0, 0),
(57, 22, 18648, -1, -0.9309, 0.0008, -0.5111, 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `cars`
--

CREATE TABLE `cars` (
  `carID` int(11) NOT NULL,
  `carModel` int(11) DEFAULT 0,
  `carOwner` int(11) DEFAULT 0,
  `carPosX` float DEFAULT 0,
  `carPosY` float DEFAULT 0,
  `carPosZ` float DEFAULT 0,
  `carPosR` float DEFAULT 0,
  `carColor1` int(11) DEFAULT 0,
  `carColor2` int(11) DEFAULT 0,
  `carPaintjob` int(11) DEFAULT -1,
  `carLocked` int(11) DEFAULT 0,
  `carMod1` int(11) DEFAULT 0,
  `carMod2` int(11) DEFAULT 0,
  `carMod3` int(11) DEFAULT 0,
  `carMod4` int(11) DEFAULT 0,
  `carMod5` int(11) DEFAULT 0,
  `carMod6` int(11) DEFAULT 0,
  `carMod7` int(11) DEFAULT 0,
  `carMod8` int(11) DEFAULT 0,
  `carMod9` int(11) DEFAULT 0,
  `carMod10` int(11) DEFAULT 0,
  `carMod11` int(11) DEFAULT 0,
  `carMod12` int(11) DEFAULT 0,
  `carMod13` int(11) DEFAULT 0,
  `carMod14` int(11) DEFAULT 0,
  `carImpounded` int(11) DEFAULT 0,
  `carWeapon1` int(11) DEFAULT 0,
  `carAmmo1` int(11) DEFAULT 0,
  `carWeapon2` int(11) DEFAULT 0,
  `carAmmo2` int(11) DEFAULT 0,
  `carWeapon3` int(11) DEFAULT 0,
  `carAmmo3` int(11) DEFAULT 0,
  `carWeapon4` int(11) DEFAULT 0,
  `carAmmo4` int(11) DEFAULT 0,
  `carWeapon5` int(11) DEFAULT 0,
  `carAmmo5` int(11) DEFAULT 0,
  `carImpoundPrice` int(11) DEFAULT 0,
  `carFaction` int(11) DEFAULT 0,
  `carPlate` varchar(12) DEFAULT 'None',
  `carPlateTime` int(11) DEFAULT 0,
  `carInsurance` int(11) DEFAULT 1,
  `carDeath` int(11) DEFAULT 0,
  `carClaimTime` int(11) DEFAULT 0,
  `carFuel` int(11) DEFAULT 100,
  `carHealth` float DEFAULT 1000,
  `carPanel` int(11) DEFAULT 0,
  `carDoor` int(11) DEFAULT 0,
  `carLight` int(11) DEFAULT 0,
  `carTire` int(11) DEFAULT 0,
  `carVIP` int(11) DEFAULT 0,
  `togneon` int(11) DEFAULT 0,
  `Neon` int(11) DEFAULT 0,
  `CarNeon1` int(11) DEFAULT 0,
  `CarNeon2` int(11) DEFAULT 0,
  `neonPosX` float DEFAULT 0,
  `neonPosY` float DEFAULT 0,
  `neonPosZ` float DEFAULT 0,
  `neonPosRX` float DEFAULT 0,
  `neonPosRY` float DEFAULT 0,
  `neonPosRZ` float DEFAULT 0,
  `neonPosX2` float DEFAULT 0,
  `neonPosY2` float DEFAULT 0,
  `neonPosZ2` float DEFAULT 0,
  `neonPosRX2` float DEFAULT 0,
  `neonPosRY2` float DEFAULT 0,
  `neonPosRZ2` float DEFAULT 0,
  `carWheels` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `cars`
--

INSERT INTO `cars` (`carID`, `carModel`, `carOwner`, `carPosX`, `carPosY`, `carPosZ`, `carPosR`, `carColor1`, `carColor2`, `carPaintjob`, `carLocked`, `carMod1`, `carMod2`, `carMod3`, `carMod4`, `carMod5`, `carMod6`, `carMod7`, `carMod8`, `carMod9`, `carMod10`, `carMod11`, `carMod12`, `carMod13`, `carMod14`, `carImpounded`, `carWeapon1`, `carAmmo1`, `carWeapon2`, `carAmmo2`, `carWeapon3`, `carAmmo3`, `carWeapon4`, `carAmmo4`, `carWeapon5`, `carAmmo5`, `carImpoundPrice`, `carFaction`, `carPlate`, `carPlateTime`, `carInsurance`, `carDeath`, `carClaimTime`, `carFuel`, `carHealth`, `carPanel`, `carDoor`, `carLight`, `carTire`, `carVIP`, `togneon`, `Neon`, `CarNeon1`, `CarNeon2`, `neonPosX`, `neonPosY`, `neonPosZ`, `neonPosRX`, `neonPosRY`, `neonPosRZ`, `neonPosX2`, `neonPosY2`, `neonPosZ2`, `neonPosRX2`, `neonPosRY2`, `neonPosRZ2`, `carWheels`) VALUES
(2, 462, 3, 2862.63, -2015.54, 13.5286, 182.225, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 71, 998.342, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(5, 522, 4, 2272.07, -2345.16, 13.6648, 140.068, 125, 96, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'kimak', **********, 1, 0, 0, 100, 961.577, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(6, 411, 4, 1858.79, -1883.14, 13.7712, 87.0429, 4, 2, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'bobi', **********, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(7, 468, 4, 1848.51, -1882.1, 15.0336, 116.703, 42, 40, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'crf', 1745237920, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(8, 462, 6, 2259.8, -2340, 13.1427, 134.24, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', **********, 1, 0, 0, 66, 834.091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(9, 462, 7, 1510.02, -1319.67, 13.8497, 188.72, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', **********, 1, 0, 0, 99, 911.389, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(11, 453, 3, 2924.86, -2043.05, 2.5518, 261.738, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(26, 429, 2, 2862.64, -1943.64, 10.6694, 359.414, 89, 64, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 99, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(28, 499, 2, 2857.39, -1928.66, 11.2285, 104.133, 36, 117, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 2, 1, 1751593196, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(29, 455, 2, 2935.58, -1966.66, -1.2604, 260.608, 39, 114, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 1, 1751593291, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(30, 462, 10, -72.1687, -1114.87, 0.6491, 297.828, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 35, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(32, 543, 24, 2313.39, -2282.37, 13.367, 43.966, 0, 0, -1, 1, 0, 0, 0, 0, 0, 1010, 0, 1085, 0, 1087, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 92, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(33, 522, 11, 582.201, 864.598, -43.596, 85.3247, 87, 69, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 26, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(34, 462, 16, 363.404, -2043.66, 7.7269, 186.168, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 10, 904.716, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(35, 462, 15, 1340.98, -1287.76, 13.3875, 176.51, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 96, 954.477, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(37, 462, 17, 1749.16, -2250.27, -1.3366, 91.4747, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 1, 1751684197, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(39, 586, 18, -68.2608, -1145.47, 0.5973, 158.413, 13, 10, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 0, 734.394, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(40, 586, 19, -74.1436, -1153.72, 2.1665, 336.501, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 935.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(41, 462, 17, 378.096, -2026.41, 7.4282, 227.109, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(42, 462, 20, -74.7976, -1153.16, 1.6342, 332.317, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 87, 882.062, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(43, 499, 19, 1839.85, -1872.19, 13.3745, 256.424, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 72, 515.164, 20054050, 33554948, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(44, 499, 20, 2411.59, -1426.82, 24.2852, 194.69, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 0, 0, 1751794815, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(46, 415, 12, 2326.67, -2268.83, 13.3039, 45.6809, 0, 0, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 63, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(47, 455, 11, 2316.9, -2280.2, 14.0301, 48.1149, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 98, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(48, 499, 22, 2306.66, -2288.23, 13.7086, 47.101, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 92, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(49, 521, 22, 2247.69, -1663.16, 15.0304, 258.164, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 89, 992.947, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(50, 522, 21, 164.566, -1762.1, 7.2467, 63.6776, 3, 110, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(51, 560, 0, -75.8171, -1133.59, 0.8031, 248.002, 0, 0, -1, 0, 0, 0, 0, 0, 0, 1010, 0, 1074, 0, 1087, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 'None', 0, 1, 0, 0, 96, 9797.02, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(52, 515, 21, 2112.17, -1897.99, 15.3138, 263.97, 25, 29, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 93, 994.109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(53, 426, 14, -64.7786, -1144.09, 0.8037, 150.189, 122, 42, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 24, 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 944.03, 19922962, 33686016, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(54, 499, 10, 1488.13, -1743.89, 13.5379, 97.4339, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(55, 499, 14, -38.8622, -1140.88, 1.0965, 75.0639, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(56, 415, 24, 2339.79, -2295.56, 13.3866, 44.1957, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 86, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(57, 415, 22, 2300.09, -2295.96, 13.2235, 44.2184, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 85, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(58, 429, 11, 1508.4, -1183.3, 26.4393, 268.159, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 99, 1000, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(60, 462, 26, 1833.41, -1870.24, 13.2774, 325.154, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 94, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(62, 521, 12, 1841.08, -1872.02, 12.953, 358.624, 0, 0, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 77, 950, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(63, 586, 29, 2109.21, -1908.74, 13.4394, 181.854, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 79, 897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(64, 462, 28, 1758.85, -2254.25, 0.5736, 95.4099, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 1, 1751998965, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(65, 462, 31, -71.6583, -1135.29, 0.9779, 66.3788, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 999.294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(66, 499, 31, 2147.98, -1138.43, 25.7726, 92.0991, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 49, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(67, 446, 11, 133.014, -1863.27, 4.0385, 103.757, 0, 91, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 1, 1752003530, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(68, 452, 11, 136.887, -1859.45, 2.8464, 101.584, 78, 106, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 1, 1752003550, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(69, 543, 31, -231.335, -1344.86, 8.1695, 73.9425, 1, 1, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(70, 455, 24, 2310.71, -2286.92, 14.0005, 47.2734, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 996.778, 16777216, 512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(71, 455, 12, 2320.52, -2276.42, 13.8635, 42.6547, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 97, 985.703, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(72, 586, 33, 1465.51, -1739.36, 13.0689, 262.325, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(73, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 1000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(74, 455, 18, 2095.29, -1816.5, 13.8167, 312.033, 1, 1, -1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'None', 0, 1, 0, 0, 100, 969.577, 2097185, 33554434, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `carsticker`
--

CREATE TABLE `carsticker` (
  `id` int(11) DEFAULT 0,
  `stickerID` int(11) NOT NULL,
  `stickerModel` int(11) DEFAULT 0,
  `stickerSize` int(11) DEFAULT 90,
  `stickerText` varchar(64) DEFAULT 'Sticker',
  `stickerFont` varchar(64) DEFAULT 'Arial',
  `stickerFontSize` int(11) DEFAULT 24,
  `stickerBold` int(11) DEFAULT 1,
  `stickerColor` int(11) DEFAULT 0,
  `stickerPosX` float DEFAULT 0,
  `stickerPosY` float DEFAULT 0,
  `stickerPosZ` float DEFAULT 0,
  `stickerPosRX` float DEFAULT 0,
  `stickerPosRY` float DEFAULT 0,
  `stickerPosRZ` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `carsticker`
--

INSERT INTO `carsticker` (`id`, `stickerID`, `stickerModel`, `stickerSize`, `stickerText`, `stickerFont`, `stickerFontSize`, `stickerBold`, `stickerColor`, `stickerPosX`, `stickerPosY`, `stickerPosZ`, `stickerPosRX`, `stickerPosRY`, `stickerPosRZ`) VALUES
(26, 5, 19482, 120, 'Hamzah', 'Comic Sans MS', 30, 1, -13382401, 0, 1.1405, 0.3, 0, -90.0998, 0),
(26, 6, 19482, 140, 'ProHero', 'Comic Sans MS', 20, 1, -13382401, 0.9106, 0.2102, -0.46, 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `carstorage`
--

CREATE TABLE `carstorage` (
  `id` int(11) DEFAULT 0,
  `itemID` int(11) NOT NULL,
  `itemName` varchar(32) DEFAULT NULL,
  `itemModel` int(11) DEFAULT 0,
  `itemQuantity` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `characters`
--

CREATE TABLE `characters` (
  `id` int(11) NOT NULL,
  `username` varchar(24) DEFAULT NULL,
  `character` varchar(24) DEFAULT NULL,
  `email` varchar(32) DEFAULT 'Empty',
  `Created` int(11) DEFAULT 0,
  `gender` int(11) DEFAULT 0,
  `Birthdate` varchar(32) DEFAULT '01/01/1970',
  `origin` varchar(32) DEFAULT 'Not Specified',
  `skin` int(11) DEFAULT 0,
  `PickTextdraw` int(11) DEFAULT 0,
  `Glasses` int(11) DEFAULT 0,
  `Hat` int(11) DEFAULT 0,
  `Bandana` int(11) DEFAULT 0,
  `PosX` float DEFAULT 0,
  `PosY` float DEFAULT 0,
  `PosZ` float DEFAULT 0,
  `PosA` float DEFAULT 0,
  `Interior` int(11) DEFAULT 0,
  `World` int(11) DEFAULT 0,
  `GlassesPos` varchar(100) DEFAULT NULL,
  `HatPos` varchar(100) DEFAULT NULL,
  `BandanaPos` varchar(100) DEFAULT NULL,
  `Hospital` int(11) DEFAULT -1,
  `HospitalInt` int(11) DEFAULT 0,
  `Money` int(11) DEFAULT 0,
  `BankMoney` int(11) DEFAULT 0,
  `OwnsBillboard` int(11) DEFAULT -1,
  `Savings` int(11) DEFAULT 0,
  `Admin` int(11) DEFAULT 0,
  `JailTime` int(11) DEFAULT 0,
  `JailReason` varchar(128) DEFAULT 'None',
  `Muted` int(11) DEFAULT 0,
  `CreateDate` int(11) DEFAULT 0,
  `LastLogin` int(11) DEFAULT 0,
  `Tester` int(11) DEFAULT 0,
  `Gun1` int(11) DEFAULT 0,
  `Gun2` int(11) DEFAULT 0,
  `Gun3` int(11) DEFAULT 0,
  `Gun4` int(11) DEFAULT 0,
  `Gun5` int(11) DEFAULT 0,
  `Gun6` int(11) DEFAULT 0,
  `Gun7` int(11) DEFAULT 0,
  `Gun8` int(11) DEFAULT 0,
  `Gun9` int(11) DEFAULT 0,
  `Gun10` int(11) DEFAULT 0,
  `Gun11` int(11) DEFAULT 0,
  `Gun12` int(11) DEFAULT 0,
  `Gun13` int(11) DEFAULT 0,
  `Ammo1` int(11) DEFAULT 0,
  `Ammo2` int(11) DEFAULT 0,
  `Ammo3` int(11) DEFAULT 0,
  `Ammo4` int(11) DEFAULT 0,
  `Ammo5` int(11) DEFAULT 0,
  `Ammo6` int(11) DEFAULT 0,
  `Ammo7` int(11) DEFAULT 0,
  `Ammo8` int(11) DEFAULT 0,
  `Ammo9` int(11) DEFAULT 0,
  `Ammo10` int(11) DEFAULT 0,
  `Ammo11` int(11) DEFAULT 0,
  `Ammo12` int(11) DEFAULT 0,
  `Ammo13` int(11) DEFAULT 0,
  `House` int(11) DEFAULT -1,
  `Business` int(11) DEFAULT -1,
  `Phone` int(11) DEFAULT 0,
  `PhoneCredit` int(11) DEFAULT 0,
  `Lottery` int(11) DEFAULT 0,
  `BateraiPhone` int(11) DEFAULT 100,
  `Hunger` int(11) DEFAULT 100,
  `Thirst` int(11) DEFAULT 100,
  `Bladder` int(11) DEFAULT 100,
  `PlayingHours` int(11) DEFAULT 0,
  `Minutes` int(11) DEFAULT 0,
  `ArmorStatus` float DEFAULT 0,
  `Entrance` int(11) DEFAULT 0,
  `Job` int(11) DEFAULT 0,
  `Workshop` int(11) DEFAULT -1,
  `WorkshopRank` int(11) DEFAULT 0,
  `Faction` int(11) DEFAULT -1,
  `FactionRank` int(11) DEFAULT 0,
  `Prisoned` int(11) DEFAULT 0,
  `Warrants` int(11) DEFAULT 0,
  `Injured` int(11) DEFAULT 0,
  `InjuredTime` int(11) DEFAULT 0,
  `SalaryMinute` int(11) DEFAULT 0,
  `SalaryTake` int(11) DEFAULT 0,
  `PaycheckTime` int(11) DEFAULT 0,
  `Health` float DEFAULT 0,
  `Channel` int(11) DEFAULT 0,
  `Accent` varchar(24) DEFAULT NULL,
  `Bleeding` int(11) DEFAULT 0,
  `Warnings` int(11) DEFAULT 0,
  `Warn1` varchar(32) DEFAULT NULL,
  `Warn2` varchar(32) DEFAULT NULL,
  `MaskID` int(11) DEFAULT 0,
  `FactionMod` int(11) DEFAULT 0,
  `Capacity` int(11) DEFAULT 20,
  `StyleUI` tinyint(4) NOT NULL DEFAULT 0,
  `AdminHide` int(11) DEFAULT 0,
  `LotteryB` int(11) NOT NULL DEFAULT 0,
  `Head` int(11) DEFAULT 100,
  `Torso` int(11) DEFAULT 100,
  `Groin` int(11) DEFAULT 100,
  `Left Arm` int(11) DEFAULT 100,
  `Right Arm` int(11) DEFAULT 100,
  `Left Leg` int(11) DEFAULT 100,
  `Right Leg` int(11) DEFAULT 100,
  `MedicineTime` int(11) DEFAULT 0,
  `SpawnPoint` int(11) NOT NULL DEFAULT 0,
  `SweepTime` int(11) DEFAULT 0,
  `BusTime` int(11) DEFAULT 0,
  `TrashmasterTime` int(11) DEFAULT 0,
  `ForkliftTime` int(11) DEFAULT 0,
  `FarmerTime` int(11) DEFAULT 0,
  `LumberTime` int(11) DEFAULT 0,
  `ButcherTime` int(11) DEFAULT 0,
  `Mapper` int(11) DEFAULT 0,
  `DrivingLicTime` int(11) DEFAULT 0,
  `LumberjackLicTime` int(11) DEFAULT 0,
  `ButcherLicTime` int(11) DEFAULT 0,
  `FishTime` int(11) DEFAULT 0,
  `OOCName` varchar(21) DEFAULT 'Player',
  `VIP` int(11) DEFAULT 0,
  `VIPTime` int(11) DEFAULT 0,
  `VIPCoin` int(11) DEFAULT 0,
  `Fish1` int(11) DEFAULT 0,
  `Fish2` int(11) DEFAULT 0,
  `Fish3` int(11) DEFAULT 0,
  `Fish4` int(11) DEFAULT 0,
  `Fish5` int(11) DEFAULT 0,
  `Fish6` int(11) DEFAULT 0,
  `Fish7` int(11) DEFAULT 0,
  `Fish8` int(11) DEFAULT 0,
  `Fish9` int(11) DEFAULT 0,
  `Fish10` int(11) DEFAULT 0,
  `Weight1` int(11) DEFAULT 0,
  `Weight2` int(11) DEFAULT 0,
  `Weight3` int(11) DEFAULT 0,
  `Weight4` int(11) DEFAULT 0,
  `Weight5` int(11) DEFAULT 0,
  `Weight6` int(11) DEFAULT 0,
  `Weight7` int(11) DEFAULT 0,
  `Weight8` int(11) DEFAULT 0,
  `Weight9` int(11) DEFAULT 0,
  `Weight10` int(11) DEFAULT 0,
  `age` int(11) DEFAULT 0,
  `weight` int(11) DEFAULT 0,
  `height` int(11) DEFAULT 0,
  `reward_claimed` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `characters`
--

INSERT INTO `characters` (`id`, `username`, `character`, `email`, `Created`, `gender`, `Birthdate`, `origin`, `skin`, `PickTextdraw`, `Glasses`, `Hat`, `Bandana`, `PosX`, `PosY`, `PosZ`, `PosA`, `Interior`, `World`, `GlassesPos`, `HatPos`, `BandanaPos`, `Hospital`, `HospitalInt`, `Money`, `BankMoney`, `OwnsBillboard`, `Savings`, `Admin`, `JailTime`, `JailReason`, `Muted`, `CreateDate`, `LastLogin`, `Tester`, `Gun1`, `Gun2`, `Gun3`, `Gun4`, `Gun5`, `Gun6`, `Gun7`, `Gun8`, `Gun9`, `Gun10`, `Gun11`, `Gun12`, `Gun13`, `Ammo1`, `Ammo2`, `Ammo3`, `Ammo4`, `Ammo5`, `Ammo6`, `Ammo7`, `Ammo8`, `Ammo9`, `Ammo10`, `Ammo11`, `Ammo12`, `Ammo13`, `House`, `Business`, `Phone`, `PhoneCredit`, `Lottery`, `BateraiPhone`, `Hunger`, `Thirst`, `Bladder`, `PlayingHours`, `Minutes`, `ArmorStatus`, `Entrance`, `Job`, `Workshop`, `WorkshopRank`, `Faction`, `FactionRank`, `Prisoned`, `Warrants`, `Injured`, `InjuredTime`, `SalaryMinute`, `SalaryTake`, `PaycheckTime`, `Health`, `Channel`, `Accent`, `Bleeding`, `Warnings`, `Warn1`, `Warn2`, `MaskID`, `FactionMod`, `Capacity`, `StyleUI`, `AdminHide`, `LotteryB`, `Head`, `Torso`, `Groin`, `Left Arm`, `Right Arm`, `Left Leg`, `Right Leg`, `MedicineTime`, `SpawnPoint`, `SweepTime`, `BusTime`, `TrashmasterTime`, `ForkliftTime`, `FarmerTime`, `LumberTime`, `ButcherTime`, `Mapper`, `DrivingLicTime`, `LumberjackLicTime`, `ButcherLicTime`, `FishTime`, `OOCName`, `VIP`, `VIPTime`, `VIPCoin`, `Fish1`, `Fish2`, `Fish3`, `Fish4`, `Fish5`, `Fish6`, `Fish7`, `Fish8`, `Fish9`, `Fish10`, `Weight1`, `Weight2`, `Weight3`, `Weight4`, `Weight5`, `Weight6`, `Weight7`, `Weight8`, `Weight9`, `Weight10`, `age`, `weight`, `height`, `reward_claimed`) VALUES
(10, 'JhonAndreas', 'Joao_Gomez', '', 1, 1, '13/03/2000', 'american', 21, 0, 0, 0, 0, 1480.87, -1741.75, 13.5468, 4.4417, 0, 0, NULL, NULL, NULL, -1, -1, 66117, 0, -1, 0, 0, 0, '', 0, **********, **********, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 31117, 999, 0, 44, 32, 18, 43, 2, 217, 0, -1, 1, -1, 0, -1, 0, 0, 0, 0, 0, 0, 1960, 1, 100, 0, NULL, 0, 0, '', '', 25327, 0, 20, 0, 0, 0, 100, 58, 100, 100, 100, 100, 100, 0, 0, 0, 243, 99, 0, 0, 0, 0, 0, 0, 0, 0, 564, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(11, 'KakZah', 'Jack_Heversont', '', 1, 1, '23/03/1989', 'USE', 29, 0, 0, 0, 0, 634.764, 838.416, -42.9609, 40.4958, 0, 0, NULL, NULL, NULL, -1, -1, 109161, 311000, -1, 0, 8, 0, '', 0, 1751554560, 1752132355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 90217, 1963, 0, 0, 86, 74, 74, 3, 952, 0, -1, 2, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 2, 100, 0, NULL, 0, 0, '', '', 60183, 0, 20, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '{00FF00}~PROHero~', 3, **********, 9000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(12, 'FanID', 'Yuki_Haruto', '', 1, 1, '24/04/1994', 'Jepang', 170, 0, 0, 0, 0, 1839.96, -1872.81, 13.3897, 13.5649, 0, 0, NULL, NULL, NULL, -1, -1, 85488, 520000, -1, 0, 0, 0, '', 0, 1751555716, 1752138955, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 89777, 985, 0, 0, 64, 54, 67, 4, 2080, 0, -1, 1, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 64920, 0, 20, 0, 0, 0, 70, 0, 75, 70, 70, 70, 70, 0, 0, 1829, 3185, 991, 0, 0, 0, 0, 0, 0, 0, 0, 0, '{FFFF00}ProGamers', 1, 1754156944, 0, 2, 3, 4, 5, 1, 3, 1, 1, 4, 4, 14, 21, 19, 23, 10, 23, 16, 24, 14, 15, 0, 0, 0, 1),
(13, 'dflx', 'Aaron_Douglass', '', 1, 1, '12/03/2001', 'America', 261, 0, 0, 0, 0, 368.15, -114.916, 1001.49, 18.4115, 5, 6005, NULL, NULL, NULL, -1, 0, 89109, 25000, -1, 0, 7, 0, '', 0, 1751555824, 1752074729, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 5, 66628, 1000, 0, 749, 0, 0, 5, 3, 602, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 1, 100, 0, NULL, 0, 0, '', '', 37889, 0, 20, 0, 0, 0, 88, 0, 100, 100, 100, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(14, 'Nimo', 'Giovanno_Chang', '', 1, 1, '04/02/1997', 'United States', 22, 0, 0, 0, 0, 1538.59, -1677.53, 13.5468, 79.8829, 0, 0, NULL, NULL, NULL, -1, 0, 27150, 20000, -1, 0, 8, 0, '', 0, 1751563612, 1752096371, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 18052, 1991, 0, 0, 911, 907, 41, 2, 557, 0, -1, 1, -1, 0, 1, 5, 0, 0, 0, 0, 13, 976, 3, 64.5018, 911, NULL, 0, 0, '', '', 75394, 0, 20, 0, 1, 0, 100, 0, 100, 100, 100, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(15, 'kontolop', 'Pheonte_Adams', '', 1, 1, '04/02/1998', 'USA', 2, 0, 0, 0, 0, 1336.68, -1292.45, 13.5468, 308.915, 0, 0, NULL, NULL, NULL, -1, -1, 83390, 25000, -1, 0, 0, 0, '', 0, 1751565988, 1751645579, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 999, 0, 0, 84, 93, 92, 1, 176, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 1, 54.7891, 0, NULL, 0, 0, '', '', 75266, 0, 20, 0, 0, 0, 100, 9, 100, 100, 100, 100, 100, 0, 0, 1071, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(16, 'shoegaze', 'Chase_Ayers', '', 1, 1, '09/09/1999', 'United Kingdom', 20, 0, 0, 0, 0, 349.855, -2051.73, 7.8359, 107.164, 0, 0, NULL, NULL, NULL, -1, -1, 143000, 25000, -1, 0, 0, 0, '', 0, 1751566228, 1752133092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 999, 0, 0, 78, 81, 62, 1, 168, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 1674, 2, 96, 0, NULL, 0, 0, '', '', 60137, 0, 20, 0, 0, 0, 100, 9, 100, 100, 100, 100, 100, 0, 0, 0, 0, 1459, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(17, 'Gibran', 'Wiliam_Hurly', '', 1, 1, '23/03/1989', 'USE', 203, 0, 0, 0, 0, 363.223, -2034.99, 7.83, 342.661, 0, 0, NULL, NULL, NULL, -1, -1, 6500, 25000, -1, 0, 0, 0, '', 0, 1751597430, 1751599085, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 0, 0, 56, 50, 47, 49, 1, 42, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 65522, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 321, 0, 2173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(18, 'Nandogtg', 'Kenzo_Wazoski', '', 1, 1, '23/02/1980', 'USE', 119, 0, 0, 0, 0, 375.488, -118.931, 1001.5, 318.274, 5, 6005, NULL, NULL, NULL, -1, -1, 70489, 25000, -1, 0, 0, 0, '', 0, 1751597926, 1752136005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 5, 43781, 0, 0, 0, 90, 87, 92, 2, 304, 0, -1, 1, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 1, 100, 0, NULL, 0, 0, '', '', 47049, 0, 20, 0, 0, 0, 100, 23, 100, 100, 100, 100, 100, 0, 0, 0, 1791, 1139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(19, 'alidsma', 'Alid_Raizen', '', 1, 1, '10/02/2000', 'USA', 294, 0, 0, 0, 0, 1845.07, -1873.05, 13.3897, 109.304, 0, 0, NULL, NULL, NULL, -1, -1, 13801, 0, -1, 0, 0, 0, '', 0, 1751598893, 1752041689, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 0, 0, 32, 87, 94, 96, 1, 67, 0, -1, 1, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 64766, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 0, 652, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(20, 'Bili', 'Luiz_Bearch', '', 1, 1, '23/03/1989', 'American', 294, 0, 0, 0, 0, 1662.34, -1433.35, 13.5468, 174.509, 0, 0, NULL, NULL, NULL, -1, -1, 4200, 0, -1, 0, 0, 0, '', 0, 1751599102, 1752036143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 0, 0, 33, 18, 19, 21, 1, 67, 0, -1, 1, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 85965, 0, 20, 0, 0, 0, 100, 79, 100, 100, 100, 100, 100, 0, 0, 520, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 14, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(21, 'KillerClown', 'Deandre_Zumbersg', '', 1, 1, '10/10/1999', 'USA', 3, 0, 0, 0, 0, 589.542, 864.249, -43.3498, 291.039, 0, 0, NULL, NULL, NULL, -1, -1, 8100, 25000, -1, 0, 7, 0, 'None', 0, 1751627970, 1752043448, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 999, 0, 1, 32, 34, 33, 3, 115, 0, -1, 1, -1, 0, 1, 5, 0, 0, 0, 0, 23, 0, 0, 100, 0, NULL, 0, 0, '', '', 56450, 0, 20, 0, 0, 0, 50, 43, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(22, 'Aldo', 'Mark_Olrando', '', 1, 1, '24/12/1996', 'Medan, Indonesia', 137, 0, 0, 0, 0, 2292.16, -2313.32, 13.3772, 320.307, 0, 0, NULL, NULL, NULL, -1, -1, 25403, 180000, -1, 0, 0, 0, 'None', 0, 1751648546, 1752083175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 14181, 1995, 0, 0, 94, 92, 95, 3, 1037, 0, -1, 1, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 5, 100, 0, NULL, 0, 0, '', '', 26904, 0, 20, 0, 0, 0, 58, 0, 75, 70, 70, 65, 70, 0, 0, 0, 1879, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 2, 1, 4, 4, 1, 1, 2, 4, 2, 5, 16, 17, 12, 15, 11, 20, 17, 23, 19, 19, 0, 0, 0, 1),
(23, 'Natrigaf', 'Rick_Grimes', '', 1, 1, '12/08/2000', 'California, USA', 297, 0, 0, 0, 0, 1664.67, -1918.56, 26.1072, 16.2072, 0, 0, NULL, NULL, NULL, -1, -1, 25000, 25000, -1, 0, 0, 0, '', 0, 1751693530, 1752036311, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 999, 0, 66, 63, 61, 62, 1, 33, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 18106, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(24, 'workid0110', 'Mike_Alisster', '', 1, 1, '19/09/1999', 'Jepang', 294, 0, 0, 0, 0, 2337.33, -2293.39, 13.5533, 43.4739, 0, 0, NULL, NULL, NULL, -1, 0, 154219, 1000, -1, 0, 0, 0, '', 0, 1751709600, 1751999566, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 95508, 0, 0, 0, 18, 93, 83, 3, 1041, 0, -1, 1, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 1, 59, 0, NULL, 0, 0, '', '', 88226, 0, 20, 0, 0, 0, 70, 0, 75, 70, 70, 70, 70, 0, 0, 0, 0, 2383, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(25, 'Xylander', 'Shaquille_Howard', '', 1, 1, '09/04/2000', 'USA', 79, 0, 0, 0, 0, 2095.46, -1811.82, 13.3828, 198.21, 0, 0, NULL, NULL, NULL, -1, -1, 100, 25000, -1, 0, 0, 0, '', 0, 1751733047, 1751911041, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 71717, 2954, 0, 80, 73, 34, 75, 1, 136, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 20425, 0, 20, 0, 0, 0, 100, 79, 100, 100, 100, 100, 100, 0, 0, 0, 1987, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(26, 'habibisd', 'William_Kazuki', '', 1, 1, '23/03/1999', 'USA', 29, 0, 0, 0, 0, 2046.51, -1819.1, 13.5468, 120.391, 0, 0, NULL, NULL, NULL, -1, -1, 3200, 70000, -1, 0, 0, 0, '', 0, 1751779788, 1752121756, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 20612, 1000, 0, 48, 88, 90, 98, 1, 52, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 69313, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 404, 1881, 1639, 1704, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(27, 'ucok1318', 'Ucok_Junior', '', 1, 1, '25/05/2005', 'USE', 208, 0, 0, 0, 0, 1681.31, -2283.09, -1.2368, 239.631, 0, 0, NULL, NULL, NULL, -1, -1, 25000, 25000, -1, 0, 0, 0, '', 0, 1751782609, 1751857256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 1000, 0, 91, 92, 92, 92, 1, 8, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 46032, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(28, 'Idoyy', 'Idoy_Sedunia', '', 1, 1, '02/07/2000', 'INDONESIA', 7, 0, 0, 0, 0, 82.4775, -208.861, 1.4452, 76.4681, 0, 0, NULL, NULL, NULL, -1, -1, 111000, 10000, -1, 0, 0, 0, '', 0, 1751909130, 1751914595, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 1000, 0, 19, 8, 4, 6, 1, 79, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 24.1, 0, NULL, 0, 0, '', '', 89831, 0, 20, 0, 0, 0, 100, 0, 100, 100, 100, 100, 100, 0, 0, 0, 768, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(29, 'sannn', 'Sannn_Sedunia', '', 1, 1, '07/06/2005', 'INDONESIA', 121, 0, 0, 0, 0, 138.649, -1564, 10.1977, 231.008, 0, 0, NULL, NULL, NULL, -1, -1, 68800, 25000, -1, 0, 0, 0, '', 0, 1751909558, 1751982026, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 999, 0, 5, 53, 47, 72, 1, 91, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 79.68, 0, NULL, 0, 0, '', '', 71979, 0, 20, 0, 0, 0, 100, 0, 100, 100, 100, 100, 100, 0, 0, 0, 889, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(30, 'kaffa', 'Tengok_Sedunia', '', 1, 1, '23/07/2005', 'INDONESIA', 15, 0, 0, 0, 0, 2110.35, -1936.93, 13.5255, 241.604, 0, 0, NULL, NULL, NULL, -1, -1, 25000, 25000, -1, 0, 0, 0, '', 0, 1751909642, 1751909644, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 2954, 0, 70, 67, 66, 67, 1, 28, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 28.06, 0, NULL, 0, 0, '', '', 93182, 0, 20, 0, 0, 0, 100, 0, 100, 100, 100, 100, 100, 0, 0, 1377, 960, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(31, 'BabaUcok', 'Liu_Xingsheng', '', 1, 2, '29/04/1999', 'Chinese', 263, 0, 0, 0, 0, 570.826, 283.564, 17.5953, 156.611, 0, 0, NULL, NULL, NULL, -1, -1, 59320, 0, -1, 0, 0, 0, '', 0, 1751912759, 1752134989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 999, 0, 0, 59, 76, 69, 2, 337, 0, -1, 5, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 42133, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Newbie', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(32, 'Lonely', 'Montana_Iglesias', '', 1, 1, '29/01/2001', 'USA', 29, 0, 0, 0, 0, 1592.77, -2313.91, 13.5476, 47.3107, 0, 0, NULL, NULL, NULL, -1, -1, 25000, 25000, -1, 0, 0, 0, '', 0, 1751995291, 1751995293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 1995, 0, 98, 97, 97, 97, 1, 2, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 75826, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', 0, 0, 0, 2, 1, 4, 4, 1, 1, 2, 4, 2, 5, 16, 17, 12, 15, 11, 20, 17, 23, 19, 19, 0, 0, 0, 0),
(33, 'wokno', 'Simon_Falesky', '', 1, 1, '19/09/1999', 'Kanada', 170, 0, 0, 0, 0, 1465.63, -1738.47, 13.5468, 262.325, 0, 0, NULL, NULL, NULL, -1, -1, 50000, 25000, -1, 0, 0, 0, '', 0, 1752000717, 1752000718, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 1963, 0, 93, 92, 92, 92, 1, 6, 0, -1, 0, -1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, NULL, 0, 0, '', '', 86230, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(34, 'Djaa', 'Daniel_Valhein', '', 1, 1, '05/10/2000', 'USA', 23, 0, 0, 0, 0, 1520.97, -1697.29, 13.0682, 89.7481, 0, 0, NULL, NULL, NULL, -1, 0, 55000, 10000, -1, 0, 0, 0, '', 0, 1752075115, 1752075116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1, 0, 1000, 0, 45, 39, 37, 84, 1, 53, 0, -1, 1, -1, 0, -1, 0, 0, 0, 0, 0, 0, 2735, 1, 82.2562, 0, NULL, 0, 0, '', '', 16394, 0, 20, 0, 0, 0, 100, 100, 100, 100, 100, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1);

-- --------------------------------------------------------

--
-- Table structure for table `component_storage`
--

CREATE TABLE `component_storage` (
  `id` int(11) NOT NULL,
  `stock` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `component_storage`
--

INSERT INTO `component_storage` (`id`, `stock`) VALUES
(1, 3000);

-- --------------------------------------------------------

--
-- Table structure for table `contacts`
--

CREATE TABLE `contacts` (
  `id` int(11) DEFAULT 0,
  `contactID` int(11) NOT NULL,
  `contactName` varchar(32) DEFAULT NULL,
  `contactNumber` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `contacts`
--

INSERT INTO `contacts` (`id`, `contactID`, `contactName`, `contactNumber`) VALUES
(11, 1, 'Yuki Haruto', 89777),
(14, 2, 'Shaquille', 71717),
(24, 3, 'Irfan', 89777);

-- --------------------------------------------------------

--
-- Table structure for table `crates`
--

CREATE TABLE `crates` (
  `crateID` int(11) NOT NULL,
  `crateType` int(11) DEFAULT 0,
  `crateX` float DEFAULT 0,
  `crateY` float DEFAULT 0,
  `crateZ` float DEFAULT 0,
  `crateA` float DEFAULT 0,
  `crateInterior` int(11) DEFAULT 0,
  `crateWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `crates`
--

INSERT INTO `crates` (`crateID`, `crateType`, `crateX`, `crateY`, `crateZ`, `crateA`, `crateInterior`, `crateWorld`) VALUES
(77, 4, 2517.39, -2427.87, 13.0198, 332.438, 0, 0),
(181, 3, -82.1059, -1170.52, 1.6719, 83.7122, 0, 0),
(182, 3, -81.9061, -1169.71, 1.6813, 27.5103, 0, 0),
(183, 3, -81.3262, -1170.44, 1.7271, 247.318, 0, 0),
(184, 3, -81.0727, -1170.06, 1.6955, 323.237, 0, 0),
(185, 3, -81.0823, -1170.06, 1.7243, 226.7, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `cratestorage`
--

CREATE TABLE `cratestorage` (
  `id` int(11) DEFAULT 0,
  `cargoID` int(11) NOT NULL,
  `cargoType` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `cratestorage`
--

INSERT INTO `cratestorage` (`id`, `cargoID`, `cargoType`) VALUES
(54, 46, 10),
(54, 47, 10),
(54, 48, 10),
(54, 51, 10),
(54, 52, 8),
(48, 68, 10),
(48, 69, 10),
(48, 70, 10),
(48, 71, 10),
(48, 72, 10);

-- --------------------------------------------------------

--
-- Table structure for table `dealervehicles`
--

CREATE TABLE `dealervehicles` (
  `id` int(11) DEFAULT 0,
  `vehID` int(11) NOT NULL,
  `vehModel` int(11) DEFAULT 0,
  `vehPrice` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `dealervehicles`
--

INSERT INTO `dealervehicles` (`id`, `vehID`, `vehModel`, `vehPrice`) VALUES
(2, 1, 462, 50000),
(2, 2, 586, 75000),
(7, 4, 453, 100000),
(7, 5, 454, 150000),
(7, 6, 452, 210000),
(7, 7, 493, 300000),
(9, 8, 499, 80000),
(9, 9, 455, 160000),
(11, 10, 543, 100000),
(11, 12, 415, 300000),
(11, 13, 440, 80000),
(11, 15, 467, 120000),
(11, 16, 471, 150000),
(9, 17, 498, 100000),
(9, 18, 456, 130000),
(9, 19, 414, 100000),
(13, 20, 468, 120000),
(13, 21, 463, 100000),
(13, 22, 581, 150000),
(13, 23, 461, 150000),
(13, 24, 521, 200000);

-- --------------------------------------------------------

--
-- Table structure for table `dealervipvehicles`
--

CREATE TABLE `dealervipvehicles` (
  `id` int(11) DEFAULT 0,
  `vehID` int(11) NOT NULL,
  `vehModel` int(11) DEFAULT 0,
  `vehPrice` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `dealervipvehicles`
--

INSERT INTO `dealervipvehicles` (`id`, `vehID`, `vehModel`, `vehPrice`) VALUES
(1, 1, 522, 500),
(1, 2, 429, 700);

-- --------------------------------------------------------

--
-- Table structure for table `delayjobconfig`
--

CREATE TABLE `delayjobconfig` (
  `SweeperDelay` int(11) DEFAULT 0,
  `BusDelay` int(11) DEFAULT 0,
  `ForkliftDelay` int(11) DEFAULT 0,
  `TrashmasterDelay` int(11) DEFAULT 0,
  `FishermanDelay` int(11) DEFAULT 0,
  `FarmerDelay` int(11) DEFAULT 0,
  `ButcherDelay` int(11) DEFAULT 0,
  `LumberjackDelay` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `detectors`
--

CREATE TABLE `detectors` (
  `detectorID` int(11) NOT NULL,
  `detectorX` float DEFAULT 0,
  `detectorY` float DEFAULT 0,
  `detectorZ` float DEFAULT 0,
  `detectorAngle` float DEFAULT 0,
  `detectorInterior` int(11) DEFAULT 0,
  `detectorWorld` int(11) DEFAULT 0
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `dropped`
--

CREATE TABLE `dropped` (
  `id` int(11) NOT NULL,
  `itemName` varchar(32) DEFAULT NULL,
  `itemModel` int(11) DEFAULT 0,
  `itemX` float DEFAULT 0,
  `itemY` float DEFAULT 0,
  `itemZ` float DEFAULT 0,
  `itemInt` int(11) DEFAULT 0,
  `itemWorld` int(11) DEFAULT 0,
  `itemQuantity` int(11) DEFAULT 0,
  `itemAmmo` int(11) DEFAULT 0,
  `itemWeapon` int(11) DEFAULT 0,
  `itemPlayer` varchar(24) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `economyconfig`
--

CREATE TABLE `economyconfig` (
  `SweeperSalary` int(11) DEFAULT 0,
  `BusSalary` int(11) DEFAULT 0,
  `ForkliftSalary` int(11) DEFAULT 0,
  `TrashmasterSalary` int(11) DEFAULT 0,
  `LumberPriceStart` int(11) DEFAULT 0,
  `LumberPriceEnd` int(11) DEFAULT 0,
  `FishPriceStart` int(11) DEFAULT 0,
  `FishPriceEnd` int(11) DEFAULT 0,
  `MeatPriceStart` int(11) DEFAULT 0,
  `MeatPriceEnd` int(11) DEFAULT 0,
  `PlantPriceStart` int(11) DEFAULT 0,
  `PlantPriceEnd` int(11) DEFAULT 0,
  `ComponentPrice` int(11) DEFAULT 0,
  `MarijuanaPrice` int(11) DEFAULT 0,
  `CocainePrice` int(11) DEFAULT 0,
  `HeroinPrice` int(11) DEFAULT 0,
  `SweeperSalary2` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `economyconfig`
--

INSERT INTO `economyconfig` (`SweeperSalary`, `BusSalary`, `ForkliftSalary`, `TrashmasterSalary`, `LumberPriceStart`, `LumberPriceEnd`, `FishPriceStart`, `FishPriceEnd`, `MeatPriceStart`, `MeatPriceEnd`, `PlantPriceStart`, `PlantPriceEnd`, `ComponentPrice`, `MarijuanaPrice`, `CocainePrice`, `HeroinPrice`, `SweeperSalary2`) VALUES
(5500, 20000, 15000, 2000, 10000, 50000, 100, 200, 1, 1, 1, 2, 100, 10000, 10000, 10000, 6500),
(5500, 20000, 15000, 2000, 10000, 50000, 100, 200, 1, 1, 1, 2, 100, 10000, 10000, 10000, 6500),
(5500, 20000, 15000, 2000, 10000, 50000, 100, 200, 1, 1, 1, 2, 100, 10000, 10000, 10000, 6500),
(5500, 20000, 15000, 2000, 10000, 50000, 100, 200, 1, 1, 1, 2, 100, 10000, 10000, 10000, 6500),
(5500, 20000, 15000, 2000, 10000, 50000, 100, 200, 1, 1, 1, 2, 100, 10000, 10000, 10000, 6500);

-- --------------------------------------------------------

--
-- Table structure for table `entrances`
--

CREATE TABLE `entrances` (
  `entranceID` int(11) NOT NULL,
  `entranceName` varchar(32) DEFAULT NULL,
  `entranceIcon` int(11) DEFAULT 0,
  `entrancePosX` float DEFAULT 0,
  `entrancePosY` float DEFAULT 0,
  `entrancePosZ` float DEFAULT 0,
  `entrancePosA` float DEFAULT 0,
  `entranceIntX` float DEFAULT 0,
  `entranceIntY` float DEFAULT 0,
  `entranceIntZ` float DEFAULT 0,
  `entranceIntA` float DEFAULT 0,
  `entranceInterior` int(11) DEFAULT 0,
  `entranceExterior` int(11) DEFAULT 0,
  `entranceExteriorVW` int(11) DEFAULT 0,
  `entranceType` int(11) DEFAULT 0,
  `entrancePass` varchar(32) DEFAULT NULL,
  `entranceLocked` int(11) DEFAULT 0,
  `entranceCustom` int(11) DEFAULT 0,
  `entranceWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `entrances`
--

INSERT INTO `entrances` (`entranceID`, `entranceName`, `entranceIcon`, `entrancePosX`, `entrancePosY`, `entrancePosZ`, `entrancePosA`, `entranceIntX`, `entranceIntY`, `entranceIntZ`, `entranceIntA`, `entranceInterior`, `entranceExterior`, `entranceExteriorVW`, `entranceType`, `entrancePass`, `entranceLocked`, `entranceCustom`, `entranceWorld`) VALUES
(1, 'City Hall of Los Santos', 0, 1481.09, -1771.88, 19.1034, 359.866, 389.931, 173.8, 1008.54, 91.185, 3, 0, 0, 0, '', 0, 0, 7001),
(2, 'Bank of Los Santos', 0, 1497.2, -1181.83, 24.0624, 270.394, 1386.37, -13.8494, 1001.34, 224.018, 3, 0, 0, 0, '', 0, 0, 3),
(3, 'San Andreas Police Department', 0, 1555.04, -1675.63, 16.1953, 92.2778, 1319.7, 1592.57, 10.8403, 269.91, 3, 0, 0, 0, '', 0, 0, 7003);

-- --------------------------------------------------------

--
-- Table structure for table `factions`
--

CREATE TABLE `factions` (
  `factionID` int(11) NOT NULL,
  `factionName` varchar(32) DEFAULT NULL,
  `factionColor` int(11) DEFAULT 0,
  `factionType` int(11) DEFAULT 0,
  `factionRanks` int(11) DEFAULT 0,
  `factionLockerX` float DEFAULT 0,
  `factionLockerY` float DEFAULT 0,
  `factionLockerZ` float DEFAULT 0,
  `factionLockerInt` int(11) DEFAULT 0,
  `factionLockerWorld` int(11) DEFAULT 0,
  `factionWeapon1` int(11) DEFAULT 0,
  `factionAmmo1` int(11) DEFAULT 0,
  `factionWeapon2` int(11) DEFAULT 0,
  `factionAmmo2` int(11) DEFAULT 0,
  `factionWeapon3` int(11) DEFAULT 0,
  `factionAmmo3` int(11) DEFAULT 0,
  `factionWeapon4` int(11) DEFAULT 0,
  `factionAmmo4` int(11) DEFAULT 0,
  `factionWeapon5` int(11) DEFAULT 0,
  `factionAmmo5` int(11) DEFAULT 0,
  `factionWeapon6` int(11) DEFAULT 0,
  `factionAmmo6` int(11) DEFAULT 0,
  `factionWeapon7` int(11) DEFAULT 0,
  `factionAmmo7` int(11) DEFAULT 0,
  `factionWeapon8` int(11) DEFAULT 0,
  `factionAmmo8` int(11) DEFAULT 0,
  `factionWeapon9` int(11) DEFAULT 0,
  `factionAmmo9` int(11) DEFAULT 0,
  `factionWeapon10` int(11) DEFAULT 0,
  `factionAmmo10` int(11) DEFAULT 0,
  `factionRank1` varchar(32) DEFAULT NULL,
  `factionRank2` varchar(32) DEFAULT NULL,
  `factionRank3` varchar(32) DEFAULT NULL,
  `factionRank4` varchar(32) DEFAULT NULL,
  `factionRank5` varchar(32) DEFAULT NULL,
  `factionRank6` varchar(32) DEFAULT NULL,
  `factionRank7` varchar(32) DEFAULT NULL,
  `factionRank8` varchar(32) DEFAULT NULL,
  `factionRank9` varchar(32) DEFAULT NULL,
  `factionRank10` varchar(32) DEFAULT NULL,
  `factionRank11` varchar(32) DEFAULT NULL,
  `factionRank12` varchar(32) DEFAULT NULL,
  `factionRank13` varchar(32) DEFAULT NULL,
  `factionRank14` varchar(32) DEFAULT NULL,
  `factionRank15` varchar(32) DEFAULT NULL,
  `factionSkin1` int(11) DEFAULT 0,
  `factionSkin2` int(11) DEFAULT 0,
  `factionSkin3` int(11) DEFAULT 0,
  `factionSkin4` int(11) DEFAULT 0,
  `factionSkin5` int(11) DEFAULT 0,
  `factionSkin6` int(11) DEFAULT 0,
  `factionSkin7` int(11) DEFAULT 0,
  `factionSkin8` int(11) DEFAULT 0,
  `SpawnX` float NOT NULL,
  `SpawnY` float NOT NULL,
  `SpawnZ` float NOT NULL,
  `SpawnInterior` int(11) NOT NULL,
  `SpawnVW` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `factions`
--

INSERT INTO `factions` (`factionID`, `factionName`, `factionColor`, `factionType`, `factionRanks`, `factionLockerX`, `factionLockerY`, `factionLockerZ`, `factionLockerInt`, `factionLockerWorld`, `factionWeapon1`, `factionAmmo1`, `factionWeapon2`, `factionAmmo2`, `factionWeapon3`, `factionAmmo3`, `factionWeapon4`, `factionAmmo4`, `factionWeapon5`, `factionAmmo5`, `factionWeapon6`, `factionAmmo6`, `factionWeapon7`, `factionAmmo7`, `factionWeapon8`, `factionAmmo8`, `factionWeapon9`, `factionAmmo9`, `factionWeapon10`, `factionAmmo10`, `factionRank1`, `factionRank2`, `factionRank3`, `factionRank4`, `factionRank5`, `factionRank6`, `factionRank7`, `factionRank8`, `factionRank9`, `factionRank10`, `factionRank11`, `factionRank12`, `factionRank13`, `factionRank14`, `factionRank15`, `factionSkin1`, `factionSkin2`, `factionSkin3`, `factionSkin4`, `factionSkin5`, `factionSkin6`, `factionSkin7`, `factionSkin8`, `SpawnX`, `SpawnY`, `SpawnZ`, `SpawnInterior`, `SpawnVW`) VALUES
(1, 'San Andreas Police Department', -256, 1, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'Rank 1', 'Rank 2', 'Rank 3', 'Rank 4', 'Rank 5', 'Rank 6', 'Rank 7', 'Rank 8', 'Rank 9', 'Rank 10', 'Rank 11', 'Rank 12', 'Rank 13', 'Rank 14', 'Rank 15', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `farmasilots`
--

CREATE TABLE `farmasilots` (
  `farmasiID` int(11) NOT NULL,
  `farmasiPosX` float DEFAULT 0,
  `farmasiPosY` float DEFAULT 0,
  `farmasiPosZ` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `farmer`
--

CREATE TABLE `farmer` (
  `farmerID` int(11) NOT NULL,
  `farmerType` int(11) DEFAULT 0,
  `farmerAmount` int(11) DEFAULT 0,
  `farmerTime` int(11) DEFAULT 0,
  `farmerX` float DEFAULT 0,
  `farmerY` float DEFAULT 0,
  `farmerZ` float DEFAULT 0,
  `farmerA` float DEFAULT 0,
  `farmerInterior` int(11) DEFAULT 0,
  `farmerWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `fish_warehouse`
--

CREATE TABLE `fish_warehouse` (
  `id` int(11) NOT NULL,
  `stock` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `fish_warehouse`
--

INSERT INTO `fish_warehouse` (`id`, `stock`) VALUES
(1, 224);

-- --------------------------------------------------------

--
-- Table structure for table `furniture`
--

CREATE TABLE `furniture` (
  `id` int(11) DEFAULT 0,
  `furnitureID` int(11) NOT NULL,
  `furnitureName` varchar(32) DEFAULT NULL,
  `furnitureModel` int(11) DEFAULT 0,
  `furnitureX` float DEFAULT 0,
  `furnitureY` float DEFAULT 0,
  `furnitureZ` float DEFAULT 0,
  `furnitureRX` float DEFAULT 0,
  `furnitureRY` float DEFAULT 0,
  `furnitureRZ` float DEFAULT 0,
  `furnitureType` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `furniture`
--

INSERT INTO `furniture` (`id`, `furnitureID`, `furnitureName`, `furnitureModel`, `furnitureX`, `furnitureY`, `furnitureZ`, `furnitureRX`, `furnitureRY`, `furnitureRZ`, `furnitureType`) VALUES
(6, 1, 'Television 1', 1518, 2259.94, -1205.18, 1049.02, 0, 0, 351.612, 0);

-- --------------------------------------------------------

--
-- Table structure for table `garbage`
--

CREATE TABLE `garbage` (
  `garbageID` int(11) NOT NULL,
  `garbageModel` int(11) DEFAULT 1236,
  `garbageCapacity` int(11) DEFAULT 0,
  `garbageX` float DEFAULT 0,
  `garbageY` float DEFAULT 0,
  `garbageZ` float DEFAULT 0,
  `garbageA` float DEFAULT 0,
  `garbageInterior` int(11) DEFAULT 0,
  `garbageWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `garbage`
--

INSERT INTO `garbage` (`garbageID`, `garbageModel`, `garbageCapacity`, `garbageX`, `garbageY`, `garbageZ`, `garbageA`, `garbageInterior`, `garbageWorld`) VALUES
(13, 1345, 10, 2285.05, -2316.06, 2.1567, -42.8446, 0, 0),
(14, 1343, 4, 2351.76, -2330.78, 13.8968, 316.275, 0, 0),
(16, 1345, 4, 2276.13, -2315.25, 13.8968, 46.6492, 0, 0),
(19, 1345, 10, 2548.79, -2458.74, 13.9609, 313.739, 0, 0),
(20, 1344, 10, 2621.13, -2347.74, 13.9947, -178.642, 0, 0),
(22, 1358, 10, 2516.8, -2678.09, 14.3501, 89.4654, 0, 0),
(24, 1345, 6, 2210.94, -2511.78, 13.8668, 0.3221, 0, 0),
(25, 1345, 2, 2311.94, -2379.69, 13.8767, 220.476, 0, 0),
(26, 1345, 2, 2275.64, -2374.2, 13.8568, 44.6939, 0, 0),
(0, 1345, 10, 361.059, -2042.09, 8.1659, 89.6852, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `gates`
--

CREATE TABLE `gates` (
  `gateID` int(11) NOT NULL,
  `gateModel` int(11) NOT NULL DEFAULT 980,
  `gateSpeed` float NOT NULL DEFAULT 3,
  `gateRadius` float NOT NULL DEFAULT 5,
  `gateTime` int(11) NOT NULL DEFAULT 0,
  `gateX` float NOT NULL,
  `gateY` float NOT NULL,
  `gateZ` float NOT NULL,
  `gateRX` float NOT NULL,
  `gateRY` float NOT NULL,
  `gateRZ` float NOT NULL,
  `gateInterior` int(11) NOT NULL DEFAULT 0,
  `gateWorld` int(11) NOT NULL DEFAULT 0,
  `gateMoveX` float NOT NULL,
  `gateMoveY` float NOT NULL,
  `gateMoveZ` float NOT NULL,
  `gateMoveRX` float NOT NULL,
  `gateMoveRY` float NOT NULL,
  `gateMoveRZ` float NOT NULL,
  `gateLinkID` int(11) NOT NULL DEFAULT -1,
  `gateFaction` int(11) NOT NULL DEFAULT -1,
  `gatePass` varchar(32) NOT NULL DEFAULT '',
  `gateToll` int(11) NOT NULL DEFAULT 0,
  `gateTollPrice` int(11) NOT NULL DEFAULT 10,
  `gateTollTextOffsetX` float NOT NULL,
  `gateTollTextOffsetY` float NOT NULL,
  `gateTollTextOffsetZ` float NOT NULL,
  `gateTollPickupOffsetX` float NOT NULL,
  `gateTollPickupOffsetY` float NOT NULL,
  `gateTollPickupOffsetZ` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `gates`
--

INSERT INTO `gates` (`gateID`, `gateModel`, `gateSpeed`, `gateRadius`, `gateTime`, `gateX`, `gateY`, `gateZ`, `gateRX`, `gateRY`, `gateRZ`, `gateInterior`, `gateWorld`, `gateMoveX`, `gateMoveY`, `gateMoveZ`, `gateMoveRX`, `gateMoveRY`, `gateMoveRZ`, `gateLinkID`, `gateFaction`, `gatePass`, `gateToll`, `gateTollPrice`, `gateTollTextOffsetX`, `gateTollTextOffsetY`, `gateTollTextOffsetZ`, `gateTollPickupOffsetX`, `gateTollPickupOffsetY`, `gateTollPickupOffsetZ`) VALUES
(8, 968, 0.1, 5, 10000, 42.4194, -1534.97, 5.0215, 0, 89.6999, -89.7697, 0, 0, 42.42, -1535.11, 4.8322, 0, -0.1, -89.7697, -1, -1, '', 1, 50, -8.2513, -2.4061, 1.2163, -8.2513, -2.4061, 0.2162),
(9, 968, 0.1, 5, 10000, 60.6432, -1528.31, 4.795, 0, 88.8999, 90.2488, 0, 0, 60.643, -1528.26, 4.6246, 0, 0.3999, 90.2488, -1, -1, '', 1, 50, 5.7121, 2.6831, 1.0966, 5.7121, 2.6831, 0.0966),
(10, 968, 0.2, 5, 10000, 628.263, -1194.1, 18.0025, 0, 88.7999, 25.6415, 0, 0, 628.421, -1194.02, 17.7461, 0, -0.2, 25.6415, -1, -1, '', 1, 50, 5.1678, -1.65, 1.1068, 5.1678, -1.65, 0.1068),
(11, 968, 0.1, 5, 10000, -49.472, -1395.5, 11.3689, 0, 89.8999, -9.0001, 0, 0, -49.476, -1395.53, 11.1089, 0, 0, -6.2001, -1, -1, '', 1, 50, -0.0965, -3.9714, 1.2034, -0.0965, -3.9714, 0.2034),
(12, 968, 0.1, 5, 10000, -90.0607, -922.365, 18.5381, -3.3999, 85.9999, -57.5368, 0, 0, -89.9784, -922.52, 18.3091, -3.3999, -3.6, -57.5368, -1, -1, '', 1, 50, -0.8686, -9.4069, 2.0335, -0.8686, -9.4069, 1.0335),
(13, 968, 0.1, 5, 10000, -82.977, -892.226, 15.9558, 4.8, -90.2999, 154.786, 0, 0, -82.7897, -892.338, 15.704, 4.8, -0.6999, 154.786, -1, -1, '', 1, 50, 5.743, 3.6109, 0.717, 5.743, 3.6109, -0.2829),
(14, 968, 0.1, 5, 10000, -960.68, -304.519, 36.2206, 0, 88.7, 168.609, 0, 0, -960.839, -304.487, 35.9117, 0, -0.3999, 168.609, -1, -1, '', 1, 50, -1.9956, 5.2537, 1.2057, -1.9956, 5.2537, 0.2057),
(15, 968, 0.1, 5, 10000, -963.492, -325.531, 36.0795, 0, 89.1999, -11.2575, 0, 0, -963.346, -325.561, 35.8185, 0, 0.3999, -11.2575, -1, -1, '', 1, 50, 2.2199, -5.5273, 1.0758, 2.2199, -5.5273, 0.0758),
(16, 968, 0.1, 7, 10000, 2303.87, -2305.2, 13.36, 0, 89.0999, -44.2414, 0, 0, 2303.93, -2305.27, 13.2003, 0, -0.1999, -44.2414, -1, -1, '', 0, 10, 0, 0, 2, 0, 0, 0),
(17, 968, 0.1, 7, 10000, 2348.84, -2298.33, 13.4075, 0, 89.5, 44.7693, 0, 0, 2348.95, -2298.22, 13.1778, 0, -0.1, 44.7693, -1, -1, '', 0, 10, 0, 0, 2, 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `gps`
--

CREATE TABLE `gps` (
  `id` int(11) DEFAULT 0,
  `locationID` int(11) NOT NULL,
  `locationName` varchar(32) DEFAULT NULL,
  `locationX` float DEFAULT 0,
  `locationY` float DEFAULT 0,
  `locationZ` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `graffiti`
--

CREATE TABLE `graffiti` (
  `graffitiID` int(11) NOT NULL,
  `graffitiOwner` int(11) DEFAULT -1,
  `graffitiX` float DEFAULT 0,
  `graffitiY` float DEFAULT 0,
  `graffitiZ` float DEFAULT 0,
  `graffitiRX` float DEFAULT 0,
  `graffitiRY` float DEFAULT 0,
  `graffitiRZ` float DEFAULT 0,
  `graffitiColor` int(11) DEFAULT 0,
  `graffitiText` varchar(64) DEFAULT NULL,
  `graffitiFont` varchar(64) DEFAULT 'Arial',
  `graffitiFontSize` int(11) DEFAULT 24,
  `graffitiBold` int(11) DEFAULT 1,
  `graffitiVirtual` int(11) DEFAULT 0,
  `graffitiInterior` int(11) DEFAULT 0
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `groupperm`
--

CREATE TABLE `groupperm` (
  `commandID` int(11) NOT NULL,
  `commandName` varchar(24) DEFAULT 'none',
  `commandType` int(11) DEFAULT 1,
  `commandTypeGroup` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `groupperm`
--

INSERT INTO `groupperm` (`commandID`, `commandName`, `commandType`, `commandTypeGroup`) VALUES
(1, 'a', 2, 1),
(2, 'abandon', 1, 0),
(3, 'acc', 1, 0),
(4, 'acceptcall', 1, 0),
(5, 'acolorcar', 2, 5),
(6, 'actoranims', 2, 6),
(7, 'actorbar', 1, 0),
(8, 'actorbat', 1, 0),
(9, 'actorblowjob', 1, 0),
(10, 'actorbomb', 1, 0),
(11, 'actorcarry', 1, 0),
(12, 'actorchat', 1, 0),
(13, 'actorcheer', 1, 0),
(14, 'actorcower', 1, 0),
(15, 'actorcpr', 1, 0),
(16, 'actorcrack', 1, 0),
(17, 'actorcrossarms', 1, 0),
(18, 'actorcry', 1, 0),
(19, 'actordancing', 1, 0),
(20, 'actordeal', 1, 0),
(21, 'actordrunk', 1, 0),
(22, 'actoreating', 1, 0),
(23, 'actorfucku', 1, 0),
(24, 'actorgoggles', 1, 0),
(25, 'actorgsign', 1, 0),
(26, 'actorhandsup', 1, 0),
(27, 'actorjump', 1, 0),
(28, 'actorkiss', 1, 0),
(29, 'actorknife', 1, 0),
(30, 'actorlay', 1, 0),
(31, 'actoroffice', 1, 0),
(32, 'actorpoint', 1, 0),
(33, 'actorpuke', 1, 0),
(34, 'actorreload', 1, 0),
(35, 'actorscratch', 1, 0),
(36, 'actorsit', 1, 0),
(37, 'actorslap', 1, 0),
(38, 'actorsleep', 1, 0),
(39, 'actorsmoke', 1, 0),
(40, 'actorspray', 1, 0),
(41, 'actorstrip', 1, 0),
(42, 'actorswipe', 1, 0),
(43, 'actortaichi', 1, 0),
(44, 'actorthrow', 1, 0),
(45, 'actortired', 1, 0),
(46, 'actorwalk', 1, 0),
(47, 'actorwank', 1, 0),
(48, 'actorwash', 1, 0),
(49, 'actorwave', 1, 0),
(50, 'actorworkout', 1, 0),
(51, 'ad', 1, 0),
(52, 'adestroybox', 2, 5),
(53, 'admins', 1, 0),
(54, 'ado', 1, 0),
(55, 'aduty', 2, 1),
(56, 'afire', 2, 5),
(57, 'agive', 2, 8),
(58, 'ah', 2, 1),
(59, 'ahelp', 1, 0),
(60, 'ahide', 1, 0),
(61, 'ainspect', 2, 1),
(62, 'ajail', 2, 1),
(63, 'akillfire', 2, 5),
(64, 'allcmds', 1, 0),
(65, 'ame', 1, 0),
(66, 'ammo', 1, 0),
(67, 'amyov', 2, 5),
(68, 'animalid', 1, 0),
(69, 'animcmds', 1, 0),
(70, 'ans', 2, 1),
(71, 'answer', 1, 0),
(72, 'aojail', 1, 0),
(73, 'apaintjob', 2, 5),
(74, 'approve', 1, 0),
(75, 'ar', 1, 0),
(76, 'aremovecall', 2, 1),
(77, 'arepair', 2, 3),
(78, 'armor', 2, 3),
(79, 'arrest', 1, 0),
(80, 'asellbiz', 2, 7),
(81, 'asellhouse', 2, 7),
(82, 'asellworkshop', 2, 7),
(83, 'asetfaction', 2, 7),
(84, 'asetrank', 2, 7),
(85, 'asetworkshopemployee', 1, 0),
(86, 'asetworkshoprank', 1, 0),
(87, 'ask', 1, 0),
(88, 'asks', 2, 1),
(89, 'aslap', 2, 2),
(90, 'atalk', 2, 2),
(91, 'atm', 1, 0),
(92, 'atog', 1, 0),
(93, 'atracenumber', 2, 3),
(94, 'atune', 2, 5),
(95, 'b', 1, 0),
(96, 'backup', 1, 0),
(97, 'ban', 2, 8),
(98, 'bandage', 1, 0),
(99, 'baninfo', 2, 5),
(100, 'bank', 1, 0),
(101, 'bar', 1, 0),
(102, 'bat', 1, 0),
(103, 'bc', 1, 0),
(104, 'bcargo', 1, 0),
(105, 'beanbag', 1, 0),
(106, 'billboards', 1, 0),
(107, 'binfo', 1, 0),
(108, 'bizcmds', 1, 0),
(109, 'bizmenu', 1, 0),
(110, 'bizstate', 1, 0),
(111, 'blacklist', 2, 8),
(112, 'bleeding', 2, 3),
(113, 'blowjob', 1, 0),
(114, 'bmessage', 1, 0),
(115, 'bname', 1, 0),
(116, 'bomb', 1, 0),
(117, 'boombox', 1, 0),
(118, 'boost', 2, 8),
(119, 'breakcuffs', 1, 0),
(120, 'bring', 2, 1),
(121, 'bringcar', 2, 1),
(122, 'broadcast', 1, 0),
(123, 'bshipment', 1, 0),
(124, 'buy', 1, 0),
(125, 'buychainsaw', 1, 0),
(126, 'buycomponent', 4, 2),
(127, 'buyinsurance', 1, 0),
(128, 'buymaterial', 1, 0),
(129, 'buyseeds', 4, 5),
(130, 'call', 1, 0),
(131, 'callsign', 1, 0),
(132, 'cancelshipment', 1, 0),
(133, 'carry', 1, 0),
(134, 'changepass', 1, 0),
(135, 'channel', 1, 0),
(136, 'charge', 1, 0),
(137, 'chat', 1, 0),
(138, 'checkstats', 2, 1),
(139, 'cheer', 1, 0),
(140, 'claiminsurance', 1, 0),
(141, 'clearactoranim', 1, 0),
(142, 'clearask', 2, 3),
(143, 'clearchat', 1, 0),
(144, 'clearchatall', 2, 2),
(145, 'clearinventory', 2, 7),
(146, 'clearwarns', 2, 6),
(147, 'collecttrash', 1, 0),
(148, 'color', 1, 0),
(149, 'cook', 1, 0),
(150, 'cower', 1, 0),
(151, 'cpr', 1, 0),
(152, 'crack', 1, 0),
(153, 'createactor', 1, 0),
(154, 'createanimal', 1, 0),
(155, 'createarrest', 1, 0),
(156, 'createatm', 1, 0),
(157, 'createbillboard', 1, 0),
(158, 'createbiz', 1, 0),
(159, 'createcar', 1, 0),
(160, 'createcargo', 1, 0),
(161, 'createdetector', 1, 0),
(162, 'createentrance', 1, 0),
(163, 'createfaction', 1, 0),
(164, 'createfarmer', 1, 0),
(165, 'creategarbage', 1, 0),
(166, 'creategate', 1, 0),
(167, 'creategraffiti', 2, 5),
(168, 'creategun', 1, 0),
(169, 'createhouse', 1, 0),
(170, 'createimpound', 1, 0),
(171, 'createinsurance', 1, 0),
(172, 'createjob', 1, 0),
(173, 'createlumber', 1, 0),
(174, 'createmarket', 1, 0),
(175, 'creatematerial', 1, 0),
(176, 'createobject', 2, 5),
(177, 'createpharmacy', 1, 0),
(178, 'createpump', 1, 0),
(179, 'createspeed', 1, 0),
(180, 'createtreatment', 1, 0),
(181, 'createtree', 1, 0),
(182, 'createvendor', 1, 0),
(183, 'createvipdealer', 1, 0),
(184, 'createworkshop', 1, 0),
(185, 'credits', 1, 0),
(186, 'crossarms', 1, 0),
(187, 'cry', 1, 0),
(188, 'cuff', 1, 0),
(189, 'customskin', 1, 0),
(190, 'dance', 1, 0),
(191, 'dancing', 1, 0),
(192, 'deal', 1, 0),
(193, 'delayconfig', 1, 0),
(194, 'delayjob', 1, 0),
(195, 'deleteaccount', 2, 8),
(196, 'deletechar', 2, 8),
(197, 'dept', 1, 0),
(198, 'destroyactor', 1, 0),
(199, 'destroyanimal', 1, 0),
(200, 'destroyarrest', 1, 0),
(201, 'destroyatm', 1, 0),
(202, 'destroybillboard', 1, 0),
(203, 'destroybiz', 1, 0),
(204, 'destroycar', 1, 0),
(205, 'destroycargo', 1, 0),
(206, 'destroydetector', 1, 0),
(207, 'destroyentrance', 1, 0),
(208, 'destroyfaction', 1, 0),
(209, 'destroyfarmer', 1, 0),
(210, 'destroygarbage', 1, 0),
(211, 'destroygate', 1, 0),
(212, 'destroygraffiti', 2, 5),
(213, 'destroyhouse', 1, 0),
(214, 'destroyimpound', 1, 0),
(215, 'destroyinsurance', 1, 0),
(216, 'destroyitem', 2, 7),
(217, 'destroyjob', 1, 0),
(218, 'destroylumber', 1, 0),
(219, 'destroymarket', 1, 0),
(220, 'destroymaterial', 1, 0),
(221, 'destroyobject', 2, 5),
(222, 'destroypharmacy', 1, 0),
(223, 'destroyplant', 1, 0),
(224, 'destroypump', 1, 0),
(225, 'destroyspeed', 1, 0),
(226, 'destroytreatment', 1, 0),
(227, 'destroytree', 1, 0),
(228, 'destroyveh', 2, 3),
(229, 'destroyvendor', 1, 0),
(230, 'destroyvipdealer', 1, 0),
(231, 'destroyworkshop', 1, 0),
(232, 'detain', 1, 0),
(233, 'dh', 2, 1),
(234, 'dice', 1, 0),
(235, 'disablecp', 1, 0),
(236, 'do', 1, 0),
(237, 'doorbell', 1, 0),
(238, 'dr', 1, 0),
(239, 'drag', 1, 0),
(240, 'drink', 1, 0),
(241, 'drivingtest', 1, 0),
(242, 'drop', 1, 0),
(243, 'dropinjured', 1, 0),
(244, 'droppacket', 1, 0),
(245, 'droptrash', 1, 0),
(246, 'drunk', 1, 0),
(247, 'dynamichelp', 2, 7),
(248, 'eating', 1, 0),
(249, 'editactor', 1, 0),
(250, 'editanimal', 1, 0),
(251, 'editbillboard', 1, 0),
(252, 'editbiz', 1, 0),
(253, 'editcar', 1, 0),
(254, 'editentrance', 1, 0),
(255, 'editfaction', 1, 0),
(256, 'editfarmer', 1, 0),
(257, 'editgate', 1, 0),
(258, 'edithouse', 1, 0),
(259, 'editimpound', 1, 0),
(260, 'editinsurance', 1, 0),
(261, 'editjob', 1, 0),
(262, 'editmarket', 1, 0),
(263, 'editmaterial', 1, 0),
(264, 'editobject', 2, 5),
(265, 'editpharmacy', 1, 0),
(266, 'editspeed', 1, 0),
(267, 'edittreatment', 1, 0),
(268, 'edittree', 1, 0),
(269, 'editvipdealer', 1, 0),
(270, 'editworkshop', 1, 0),
(271, 'entercar', 2, 3),
(272, 'entrancelock', 1, 0),
(273, 'fac', 1, 0),
(274, 'factions', 1, 0),
(275, 'faq', 1, 0),
(276, 'farmerid', 1, 0),
(277, 'fill', 1, 0),
(278, 'findanimal', 1, 0),
(279, 'findpacket', 1, 0),
(280, 'findtree', 1, 0),
(281, 'fine', 2, 5),
(282, 'fingerprint', 1, 0),
(283, 'finvite', 1, 0),
(284, 'fish', 1, 0),
(285, 'fishes', 1, 0),
(286, 'flipcar', 2, 3),
(287, 'flist', 1, 0),
(288, 'flocker', 1, 0),
(289, 'fquit', 1, 0),
(290, 'frank', 1, 0),
(291, 'freeze', 2, 1),
(292, 'fremove', 1, 0),
(293, 'frisk', 1, 0),
(294, 'fspawn', 1, 0),
(295, 'fucku', 1, 0),
(296, 'furniture', 1, 0),
(297, 'getcargo', 4, 1),
(298, 'getip', 2, 2),
(299, 'givecar', 2, 7),
(300, 'givecash', 2, 8),
(301, 'giveinsurance', 2, 6),
(302, 'giveitem', 1, 0),
(303, 'giveplate', 1, 0),
(304, 'givesalary', 1, 0),
(305, 'giveup', 1, 0),
(306, 'givewep', 2, 7),
(307, 'goggles', 1, 0),
(308, 'goto', 2, 1),
(309, 'gotocar', 2, 1),
(310, 'gotoco', 1, 0),
(311, 'gps', 1, 0),
(312, 'grabcash', 1, 0),
(313, 'grantbizcert', 1, 0),
(314, 'grantbutcher', 1, 0),
(315, 'grantlumber', 1, 0),
(316, 'grantweapon', 1, 0),
(317, 'gsign', 1, 0),
(318, 'handsup', 1, 0),
(319, 'hangup', 1, 0),
(320, 'harvest', 4, 5),
(321, 'heal', 2, 2),
(322, 'healall', 2, 3),
(323, 'health', 2, 3),
(324, 'help', 1, 0),
(325, 'housecmds', 1, 0),
(326, 'hudstyle', 1, 0),
(327, 'id', 1, 0),
(328, 'impound', 1, 0),
(329, 'infograffiti', 2, 5),
(330, 'infologging', 2, 1),
(331, 'inspect', 1, 0),
(332, 'inventory', 1, 0),
(333, 'inventorydraw', 1, 0),
(334, 'inventoryhide', 1, 0),
(335, 'inviteguest', 1, 0),
(336, 'itemlist', 1, 0),
(337, 'jetpack', 2, 1),
(338, 'jobduty', 1, 0),
(339, 'joblist', 1, 0),
(340, 'jump', 1, 0),
(341, 'kick', 2, 1),
(342, 'kickdoor', 1, 0),
(343, 'kiss', 1, 0),
(344, 'knife', 1, 0),
(345, 'l', 1, 0),
(346, 'lastlogged', 1, 0),
(347, 'lay', 1, 0),
(348, 'listguns', 2, 2),
(349, 'listwarns', 1, 0),
(350, 'loadcrate', 1, 0),
(351, 'loadinjured', 1, 0),
(352, 'lock', 1, 0),
(353, 'm', 3, 1),
(354, 'makeadmin', 2, 8),
(355, 'makequiz', 1, 0),
(356, 'mask', 1, 0),
(357, 'masked', 2, 2),
(358, 'mdc', 1, 0),
(359, 'me', 1, 0),
(360, 'mechanicmenu', 1, 0),
(361, 'medicine', 1, 0),
(362, 'modshop', 1, 0),
(363, 'mute', 2, 1),
(364, 'mybillboard', 1, 0),
(365, 'myov', 1, 0),
(366, 'near', 2, 3),
(367, 'o', 1, 0),
(368, 'objectid', 2, 5),
(369, 'office', 1, 0),
(370, 'ojail', 2, 1),
(371, 'online', 1, 0),
(372, 'open', 1, 0),
(373, 'opencrate', 1, 0),
(374, 'orelease', 2, 1),
(375, 'owarn', 2, 1),
(376, 'paint', 1, 0),
(377, 'panel', 2, 8),
(378, 'passwep', 1, 0),
(379, 'pay', 1, 0),
(380, 'paycheck', 1, 0),
(381, 'phone', 1, 0),
(382, 'piss', 1, 0),
(383, 'plant', 4, 5),
(384, 'playsound', 1, 0),
(385, 'pm', 1, 0),
(386, 'point', 1, 0),
(387, 'pos', 1, 0),
(388, 'pr', 1, 0),
(389, 'products', 1, 0),
(390, 'properties', 1, 0),
(391, 'puke', 1, 0),
(392, 'quitjob', 1, 0),
(393, 'quizans', 1, 0),
(394, 'refill', 2, 7),
(395, 'refreshactor', 2, 6),
(396, 'refreshanimal', 2, 7),
(397, 'refreshtree', 1, 0),
(398, 'refuel', 1, 0),
(399, 'refund', 2, 7),
(400, 'release', 2, 1),
(401, 'releasecar', 1, 0),
(402, 'reload', 1, 0),
(403, 'removeguest', 1, 0),
(404, 'repair', 1, 0),
(405, 'report', 1, 0),
(406, 'reports', 2, 1),
(407, 'resetpacketcp', 1, 0),
(408, 'resetvw', 1, 0),
(409, 'resetweps', 2, 3),
(410, 'respawn', 2, 2),
(411, 'respawncar', 2, 2),
(412, 'respawncars', 2, 2),
(413, 'respawnnear', 2, 2),
(414, 'restart', 2, 8),
(415, 'restock', 4, 1),
(416, 'revive', 2, 2),
(417, 'revokebizcert', 1, 0),
(418, 'revokebutcher', 1, 0),
(419, 'revokelumber', 1, 0),
(420, 'revokeweapon', 1, 0),
(421, 'roadblock', 1, 0),
(422, 's', 1, 0),
(423, 'salary', 1, 0),
(424, 'saveall', 2, 8),
(425, 'savecar', 2, 8),
(426, 'savecars', 2, 8),
(427, 'saveplants', 2, 8),
(428, 'saw', 1, 0),
(429, 'scrapveh', 1, 0),
(430, 'scratch', 1, 0),
(431, 'search', 1, 0),
(432, 'seekhelp', 1, 0),
(433, 'seizebiz', 1, 0),
(434, 'seizepackage', 1, 0),
(435, 'seizeplant', 1, 0),
(436, 'sell', 1, 0),
(437, 'sellfish', 1, 0),
(438, 'selllumber', 1, 0),
(439, 'sellmeat', 1, 0),
(440, 'sellplant', 4, 5),
(441, 'send', 2, 1),
(442, 'sendto', 2, 1),
(443, 'setbodystatus', 2, 6),
(444, 'setcarhp', 2, 5),
(445, 'setdelay', 2, 6),
(446, 'seteconomy', 2, 8),
(447, 'setfuel', 2, 3),
(448, 'setinsurance', 2, 7),
(449, 'setinterior', 2, 1),
(450, 'setinventory', 2, 7),
(451, 'setitem', 2, 7),
(452, 'setleader', 2, 7),
(453, 'setname', 2, 5),
(454, 'setpermission', 2, 8),
(455, 'setplayer', 2, 8),
(456, 'setpump', 1, 0),
(457, 'setquantity', 2, 7),
(458, 'setradio', 1, 0),
(459, 'setvip', 2, 8),
(460, 'setvw', 2, 1),
(461, 'setweather', 2, 4),
(462, 'shakehand', 1, 0),
(463, 'shipments', 1, 0),
(464, 'shooter', 2, 2),
(465, 'showbizcf', 1, 0),
(466, 'showidcard', 1, 0),
(467, 'showlicense', 1, 0),
(468, 'sit', 1, 0),
(469, 'skin', 2, 4),
(470, 'slap', 1, 0),
(471, 'sleep', 1, 0),
(472, 'slicing', 1, 0),
(473, 'smoke', 1, 0),
(474, 'sms', 1, 0),
(475, 'spawn', 2, 2),
(476, 'spawnitem', 2, 7),
(477, 'spawnpoint', 1, 0),
(478, 'spectate', 2, 1),
(479, 'speedlimit', 1, 0),
(480, 'spike', 1, 0),
(481, 'spray', 1, 0),
(482, 'stats', 1, 0),
(483, 'stopanim', 1, 0),
(484, 'stopgrabcash', 1, 0),
(485, 'storage', 1, 0),
(486, 'strip', 1, 0),
(487, 'swipe', 1, 0),
(488, 'switch', 1, 0),
(489, 'tag', 1, 0),
(490, 'taichi', 1, 0),
(491, 'take', 1, 0),
(492, 'takejob', 1, 0),
(493, 'takepacket', 1, 0),
(494, 'tasks', 1, 0),
(495, 'tazer', 1, 0),
(496, 'text', 1, 0),
(497, 'throw', 1, 0),
(498, 'ticket', 1, 0),
(499, 'tickets', 1, 0),
(500, 'time', 1, 0),
(501, 'tired', 1, 0),
(502, 'toggle', 1, 0),
(503, 'togooc', 2, 3),
(504, 'tow', 1, 0),
(505, 'tracenumber', 1, 0),
(506, 'traceplate', 1, 0),
(507, 'treatment', 1, 0),
(508, 'treeid', 1, 0),
(509, 'unban', 2, 2),
(510, 'uncuff', 1, 0),
(511, 'unfreeze', 2, 1),
(512, 'unmute', 2, 1),
(513, 'untow', 1, 0),
(514, 'usedrug', 1, 0),
(515, 'usekit', 1, 0),
(516, 'usemag', 1, 0),
(517, 'usemedicine', 1, 0),
(518, 'username', 2, 1),
(519, 'v', 1, 0),
(520, 'vault', 1, 0),
(521, 'veh', 2, 3),
(522, 'vest', 1, 0),
(523, 'vip', 1, 0),
(524, 'vw', 1, 0),
(525, 'walk', 1, 0),
(526, 'wank', 1, 0),
(527, 'warn', 2, 1),
(528, 'warnings', 1, 0),
(529, 'wash', 1, 0),
(530, 'wave', 1, 0),
(531, 'whisper', 1, 0),
(532, 'workout', 1, 0),
(533, 'workshop', 1, 0),
(534, 'wsinvite', 1, 0),
(535, 'wsquit', 1, 0),
(536, 'wsrank', 1, 0),
(537, 'wsremove', 1, 0),
(538, 'x', 2, 2),
(539, 'y', 2, 2),
(540, 'z', 2, 2),
(541, 'admin', 1, 0),
(542, 'd', 1, 0),
(543, 'f', 1, 0),
(544, 'h', 1, 0),
(545, 'low', 1, 0),
(546, 'megaphone', 1, 0),
(547, 'mm', 1, 0),
(548, 'ooc', 1, 0),
(549, 'or', 1, 0),
(550, 'radio', 1, 0),
(551, 'shout', 1, 0),
(552, 'trace', 1, 0),
(553, 'w', 1, 0),
(554, 'makemeadmin', 1, 0),
(555, 'levels', 1, 0),
(556, 'endquiz', 1, 0),
(557, 'mysalary', 1, 0),
(558, 'no', 1, 0),
(559, 'quiz', 2, 1),
(560, 'quizanswer', 1, 0),
(561, 'vote', 2, 1),
(562, 'yes', 1, 0),
(563, 'createicon', 1, 0),
(564, 'listatm', 1, 0),
(565, 'listgarbage', 1, 0),
(566, 'listicons', 1, 0),
(567, 'reseticons', 1, 0),
(568, 'setcash', 2, 8),
(569, 'listgate', 1, 0),
(570, 'maps', 1, 0),
(571, 'randomfishprice', 2, 8),
(572, 'stopsound', 1, 0),
(573, 'listactor', 1, 0),
(574, 'listbiz', 1, 0),
(575, 'paytoll', 1, 0),
(576, 'tollauto', 1, 0),
(577, 'buyvip', 1, 0),
(578, 'getcar', 1, 0),
(579, 'unwarn', 1, 0),
(580, 'setarmour', 2, 4),
(581, 'sethp', 2, 1),
(582, 'vrespawnobj', 1, 0),
(583, 'vehsave', 1, 0),
(584, 'vehrespawnobj', 1, 0),
(585, 'saveveh', 1, 0),
(586, 'checkfishstock', 1, 0),
(587, 'setfishstock', 1, 0),
(588, 'modifyfishstock', 1, 0),
(589, 'resetfishstock', 1, 0),
(590, 'randomplantprice', 2, 8),
(591, 'claimreward', 1, 0),
(592, 'resetreward', 1, 0),
(593, 'fishstock', 1, 0),
(594, 'restockcargo', 4, 1),
(595, 'kakzah', 1, 0),
(596, 'addcomponent', 1, 0),
(597, 'allstock', 1, 0),
(598, 'remcomponent', 1, 0),
(599, 'addplant', 1, 0),
(600, 'mission', 4, 1),
(601, 'remplant', 1, 0),
(602, 'reply', 1, 0),
(603, 'Signcheck', 1, 0),
(604, 'editatm', 1, 0),
(605, 'editpump', 1, 0),
(606, 'atmcleanup', 1, 0),
(607, 'atmdinfo', 1, 0),
(608, 'atmlist', 1, 0),
(609, 'setatmint', 1, 0),
(610, 'setatmvw', 1, 0),
(611, 'balance', 1, 0),
(612, 'deposit', 1, 0),
(613, 'removevip', 2, 8),
(614, 'sellallbottle', 1, 0),
(615, 'sendtoinsu', 2, 2),
(616, 'transfer', 1, 0),
(617, 'withdraw', 1, 0),
(618, 'endmine', 1, 0),
(619, 'ores', 1, 0),
(620, 'orestatus', 1, 0),
(621, 'respawnores', 1, 0),
(622, 'setwheels', 1, 0),
(623, 'startmine', 1, 0);

-- --------------------------------------------------------

--
-- Table structure for table `gunracks`
--

CREATE TABLE `gunracks` (
  `rackID` int(11) NOT NULL,
  `rackHouse` int(11) DEFAULT 0,
  `rackX` float DEFAULT 0,
  `rackY` float DEFAULT 0,
  `rackZ` float DEFAULT 0,
  `rackA` float DEFAULT 0,
  `rackInterior` int(11) DEFAULT 0,
  `rackWorld` int(11) DEFAULT 0,
  `rackWeapon1` int(11) DEFAULT 0,
  `rackAmmo1` int(11) DEFAULT 0,
  `rackWeapon2` int(11) DEFAULT 0,
  `rackAmmo2` int(11) DEFAULT 0,
  `rackWeapon3` int(11) DEFAULT 0,
  `rackAmmo3` int(11) DEFAULT 0,
  `rackWeapon4` int(11) DEFAULT 0,
  `rackAmmo4` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `houses`
--

CREATE TABLE `houses` (
  `houseID` int(11) NOT NULL,
  `houseOwner` int(11) DEFAULT 0,
  `housePrice` int(11) DEFAULT 0,
  `houseAddress` varchar(32) DEFAULT NULL,
  `housePosX` float DEFAULT 0,
  `housePosY` float DEFAULT 0,
  `housePosZ` float DEFAULT 0,
  `housePosA` float DEFAULT 0,
  `houseIntX` float DEFAULT 0,
  `houseIntY` float DEFAULT 0,
  `houseIntZ` float DEFAULT 0,
  `houseIntA` float DEFAULT 0,
  `houseInterior` int(11) DEFAULT 0,
  `houseExterior` int(11) DEFAULT 0,
  `houseExteriorVW` int(11) DEFAULT 0,
  `houseLocked` int(11) DEFAULT 0,
  `houseWeapon1` int(11) DEFAULT 0,
  `houseAmmo1` int(11) DEFAULT 0,
  `houseWeapon2` int(11) DEFAULT 0,
  `houseAmmo2` int(11) DEFAULT 0,
  `houseWeapon3` int(11) DEFAULT 0,
  `houseAmmo3` int(11) DEFAULT 0,
  `houseWeapon4` int(11) DEFAULT 0,
  `houseAmmo4` int(11) DEFAULT 0,
  `houseWeapon5` int(11) DEFAULT 0,
  `houseAmmo5` int(11) DEFAULT 0,
  `houseWeapon6` int(11) DEFAULT 0,
  `houseAmmo6` int(11) DEFAULT 0,
  `houseWeapon7` int(11) DEFAULT 0,
  `houseAmmo7` int(11) DEFAULT 0,
  `houseWeapon8` int(11) DEFAULT 0,
  `houseAmmo8` int(11) DEFAULT 0,
  `houseWeapon9` int(11) DEFAULT 0,
  `houseAmmo9` int(11) DEFAULT 0,
  `houseWeapon10` int(11) DEFAULT 0,
  `houseAmmo10` int(11) DEFAULT 0,
  `houseMoney` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `houses`
--

INSERT INTO `houses` (`houseID`, `houseOwner`, `housePrice`, `houseAddress`, `housePosX`, `housePosY`, `housePosZ`, `housePosA`, `houseIntX`, `houseIntY`, `houseIntZ`, `houseIntA`, `houseInterior`, `houseExterior`, `houseExteriorVW`, `houseLocked`, `houseWeapon1`, `houseAmmo1`, `houseWeapon2`, `houseAmmo2`, `houseWeapon3`, `houseAmmo3`, `houseWeapon4`, `houseAmmo4`, `houseWeapon5`, `houseAmmo5`, `houseWeapon6`, `houseAmmo6`, `houseWeapon7`, `houseAmmo7`, `houseWeapon8`, `houseAmmo8`, `houseWeapon9`, `houseAmmo9`, `houseWeapon10`, `houseAmmo10`, `houseMoney`) VALUES
(4, 0, 1000000, 'Idlewood st.1', 2067.51, -1731.61, 13.876, 270.594, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(5, 0, 1000000, 'idlewood st.2', 2066.69, -1717.18, 13.8058, 269.66, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(6, 0, 2000000, 'Idlewood st.3', 2015.07, -1732.6, 14.2343, 90.4321, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(7, 0, 1000000, 'Idlewood st.4', 2015.97, -1716.94, 14.0747, 87.5887, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(8, 0, 2000000, 'Idlewood st.5', 2017.81, -1703.28, 14.2343, 91.3488, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(9, 0, 1000000, 'Idlewood st.6', 2013.21, -1656.39, 13.8058, 91.6623, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(10, 0, 2000000, 'Idlewood st.7', 2016.18, -1641.59, 13.7824, 91.0354, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(11, 0, 1000000, 'Idlewood st.8', 2017.69, -1629.88, 13.712, 91.6622, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(12, 0, 2000000, 'Idlewood st.9', 2068.1, -1628.84, 13.8761, 269.614, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(13, 0, 1000000, 'Idlewood st.10', 2067.94, -1643.84, 13.8058, 269.614, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
(14, 0, 1000000, 'Idlewood st.11', 2067.05, -1656.57, 14.0299, 269.301, 2269.88, -1210.32, 1047.56, 90, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `housestorage`
--

CREATE TABLE `housestorage` (
  `id` int(11) DEFAULT 0,
  `itemID` int(11) NOT NULL,
  `itemName` varchar(32) DEFAULT NULL,
  `itemModel` int(11) DEFAULT 0,
  `itemQuantity` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `impoundlots`
--

CREATE TABLE `impoundlots` (
  `impoundID` int(11) NOT NULL,
  `impoundLotX` float DEFAULT 0,
  `impoundLotY` float DEFAULT 0,
  `impoundLotZ` float DEFAULT 0,
  `impoundReleaseX` float DEFAULT 0,
  `impoundReleaseY` float DEFAULT 0,
  `impoundReleaseZ` float DEFAULT 0,
  `impoundReleaseInt` int(11) DEFAULT 0,
  `impoundReleaseWorld` int(11) DEFAULT 0,
  `impoundReleaseA` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `insurancelots`
--

CREATE TABLE `insurancelots` (
  `insuranceID` int(11) NOT NULL,
  `insurancePrice` int(11) DEFAULT 100,
  `insurancePosX` float DEFAULT 0,
  `insurancePosY` float DEFAULT 0,
  `insurancePosZ` float DEFAULT 0,
  `insuranceSpawnX` float DEFAULT 0,
  `insuranceSpawnY` float DEFAULT 0,
  `insuranceSpawnZ` float DEFAULT 0,
  `insuranceSpawnA` float DEFAULT 0,
  `insuranceVirtual` int(11) DEFAULT 0,
  `insuranceInterior` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `insurancelots`
--

INSERT INTO `insurancelots` (`insuranceID`, `insurancePrice`, `insurancePosX`, `insurancePosY`, `insurancePosZ`, `insuranceSpawnX`, `insuranceSpawnY`, `insuranceSpawnZ`, `insuranceSpawnA`, `insuranceVirtual`, `insuranceInterior`) VALUES
(2, 10000, 1668.48, -1409.13, 13.5495, 2411.59, -1426.82, 23.9852, 194.69, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `inventory`
--

CREATE TABLE `inventory` (
  `id` int(11) DEFAULT 0,
  `invID` int(11) NOT NULL,
  `invItem` varchar(32) DEFAULT NULL,
  `invModel` int(11) DEFAULT 0,
  `invQuantity` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `inventory`
--

INSERT INTO `inventory` (`id`, `invID`, `invItem`, `invModel`, `invQuantity`) VALUES
(3, 30, 'Hoe', 2228, 100),
(2, 31, 'Boombox', 2226, 1),
(3, 32, 'Cellphone', 330, 1),
(2, 33, 'Bait', 1605, 6),
(2, 35, 'Soda', 1543, 1),
(2, 36, 'Hoe', 2228, 70),
(2, 37, 'Fishing Rod', 18632, 63),
(2, 39, 'Wheat Seeds', 1575, 40),
(2, 41, 'Empty Bottle', 1484, 6),
(2, 51, 'Wheat (Plant)', 19473, 100),
(2, 57, 'Wheat', 19473, 10),
(10, 58, 'Cellphone', 330, 1),
(10, 59, 'GPS System', 18875, 1),
(10, 60, 'Portable Radio', 18868, 1),
(11, 62, 'GPS System', 18875, 1),
(13, 63, 'Empty Bottle', 1484, 13),
(11, 72, 'Hoe', 2228, 75),
(11, 73, 'Soda', 1543, 71),
(12, 79, 'GPS System', 18875, 1),
(11, 82, 'Cooked Meat', 19882, 19),
(15, 84, 'Empty Bottle', 1484, 7),
(16, 88, 'Fishing Rod', 18632, 97),
(16, 90, 'Empty Bottle', 1484, 5),
(11, 91, 'Corn', 19473, 4),
(11, 92, 'Boombox', 2226, 1),
(15, 93, 'GPS System', 18875, 1),
(16, 94, 'GPS System', 18875, 1),
(11, 100, 'Wheat Seeds', 1575, 2),
(11, 115, 'Onion Seeds', 1575, 1),
(11, 116, 'Carrot Seeds', 1575, 1),
(11, 117, 'Potato Seeds', 1575, 1),
(11, 118, 'Corn Seeds', 1575, 1),
(18, 119, 'Portable Radio', 18868, 1),
(18, 120, 'GPS System', 18875, 1),
(18, 121, 'Cellphone', 330, 1),
(17, 122, 'GPS System', 18875, 1),
(17, 123, 'Fishing Rod', 18632, 100),
(20, 124, 'Fishing Rod', 18632, 96),
(20, 125, 'Bait', 1605, 92),
(15, 128, 'Fishing Rod', 18632, 100),
(15, 129, 'Repair Kit', 920, 1),
(15, 130, 'Water Bottle', 2958, 9),
(13, 131, 'Fishing Rod', 18632, 100),
(13, 132, 'Bait', 1605, 100),
(13, 133, 'Crowbar', 18634, 1),
(11, 134, 'Chair 33', 1703, 1),
(11, 135, 'Table 22', 2110, 1),
(11, 137, 'Cellphone', 330, 1),
(13, 138, 'Cellphone', 330, 1),
(13, 139, 'GPS System', 18875, 1),
(12, 140, 'Cellphone', 330, 1),
(22, 141, 'Empty Bottle', 1484, 39),
(22, 142, 'Fishing Rod', 18632, 40),
(22, 150, 'GPS System', 18875, 1),
(22, 151, 'Cellphone', 330, 1),
(18, 154, 'Water Bottle', 2958, 9),
(24, 156, 'Water Bottle', 2958, 9),
(18, 157, 'Hoe', 2228, 100),
(24, 158, 'Soda', 1543, 5),
(18, 159, 'Soda', 1543, 2),
(24, 163, 'Empty Bottle', 1484, 81),
(12, 165, 'Carrot', 19473, 53),
(18, 170, 'Empty Bottle', 1484, 12),
(25, 182, 'Empty Bottle', 1484, 80),
(14, 183, 'Empty Bottle', 1484, 8),
(14, 185, 'GPS System', 18875, 1),
(25, 187, 'Cellphone', 330, 1),
(25, 188, 'GPS System', 18875, 1),
(25, 189, 'Portable Radio', 18868, 1),
(14, 190, 'Cellphone', 330, 1),
(14, 191, 'Portable Radio', 18868, 1),
(10, 192, 'Empty Bottle', 1484, 1),
(12, 194, 'Hoe', 2228, 87),
(14, 195, 'Boombox', 2226, 1),
(10, 196, 'Bait', 1605, 94),
(10, 198, 'Fishing Rod', 18632, 97),
(10, 199, 'Soda', 1543, 1),
(10, 200, 'Water Bottle', 2958, 1),
(10, 201, 'Crowbar', 18634, 1),
(10, 202, 'Hoe', 2228, 100),
(21, 204, 'GPS System', 18875, 1),
(10, 205, 'Cooked Meat', 19882, 1),
(13, 206, 'Repair Kit', 920, 1),
(12, 214, 'Corn Seeds', 1575, 100),
(26, 217, 'GPS System', 18875, 1),
(26, 218, 'Cellphone', 330, 1),
(26, 219, 'Portable Radio', 18868, 1),
(12, 225, 'Empty Bottle', 1484, 34),
(11, 227, 'Component', 1020, 690),
(12, 229, 'Water Bottle', 2958, 9),
(29, 231, 'Empty Bottle', 1484, 1),
(31, 232, 'Fishing Rod', 18632, 82),
(31, 233, 'Bait', 1605, 64),
(31, 234, 'Empty Bottle', 1484, 41),
(31, 236, 'Soda', 1543, 5),
(22, 237, 'Bait', 1605, 980),
(31, 238, 'GPS System', 18875, 1),
(11, 239, 'Fishing Rod', 18632, 100),
(11, 240, 'Wrench', 365, 97),
(24, 242, 'Hoe', 2228, 36),
(21, 243, 'Empty Bottle', 1484, 2),
(24, 249, 'Cellphone', 330, 1),
(24, 272, 'Repair Kit', 920, 1),
(31, 287, 'Water Bottle', 2958, 47),
(31, 288, 'Hoe', 2228, 950),
(12, 294, 'Fishing Rod', 18632, 70),
(12, 295, 'Bait', 1605, 40),
(24, 296, 'Bait', 1605, 78),
(24, 297, 'Fishing Rod', 18632, 89),
(12, 299, 'Wheat', 19473, 3),
(22, 300, 'Soda', 1543, 2),
(19, 301, 'Empty Bottle', 1484, 2),
(19, 302, 'GPS System', 18875, 1),
(26, 303, 'Empty Bottle', 1484, 1),
(34, 304, 'GPS System', 18875, 1),
(18, 305, 'Fishing Rod', 18632, 86),
(18, 306, 'Bait', 1605, 62),
(16, 307, 'Bait', 1605, 4),
(18, 308, 'Fuel Can', 1650, 1);

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `jobID` int(11) NOT NULL,
  `jobPosX` float DEFAULT 0,
  `jobPosY` float DEFAULT 0,
  `jobPosZ` float DEFAULT 0,
  `jobPointX` float DEFAULT 0,
  `jobPointY` float DEFAULT 0,
  `jobPointZ` float DEFAULT 0,
  `jobDeliverX` float DEFAULT 0,
  `jobDeliverY` float DEFAULT 0,
  `jobDeliverZ` float DEFAULT 0,
  `jobInterior` int(11) DEFAULT 0,
  `jobWorld` int(11) DEFAULT 0,
  `jobType` int(11) DEFAULT 0,
  `jobPointInt` int(11) DEFAULT 0,
  `jobPointWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `jobs`
--

INSERT INTO `jobs` (`jobID`, `jobPosX`, `jobPosY`, `jobPosZ`, `jobPointX`, `jobPointY`, `jobPointZ`, `jobDeliverX`, `jobDeliverY`, `jobDeliverZ`, `jobInterior`, `jobWorld`, `jobType`, `jobPointInt`, `jobPointWorld`) VALUES
(7, -381.797, -1438.91, 25.7265, -372.834, -1428.67, 25.7264, 0, 0, 0, 0, 0, 5, 0, 0),
(11, -77.1443, -1136.56, 1.0781, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0),
(12, 2255.8, -2387.75, 17.4218, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `lumberlots`
--

CREATE TABLE `lumberlots` (
  `lumberID` int(11) NOT NULL,
  `lumberTime` int(11) DEFAULT 0,
  `lumberPosX` float DEFAULT 0,
  `lumberPosY` float DEFAULT 0,
  `lumberPosZ` float DEFAULT 0,
  `lumberPosRX` float DEFAULT 0,
  `lumberPosRY` float DEFAULT 0,
  `lumberPosRZ` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `mapicons`
--

CREATE TABLE `mapicons` (
  `id` int(11) NOT NULL,
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `type` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `mapicons`
--

INSERT INTO `mapicons` (`id`, `x`, `y`, `z`, `type`) VALUES
(0, 1859.36, -1874.36, 13.4904, 37),
(1, 2287.44, -2346.37, 13.5468, 37),
(2, 2739.7, -2390.93, 13.6328, 37),
(3, 2861.09, -1998.32, 10.9342, 9),
(4, 545.633, -1293.47, 17.2421, 55),
(5, 854.615, -604.745, 18.4218, 27),
(6, 2118.05, -1912.39, 13.6328, 37),
(7, 1614.24, -2296.28, 13.5382, 55),
(8, 2257.91, -2007.39, 13.5546, 27),
(9, 2319.55, -2290.14, 13.5468, 27),
(10, 1497.2, -1181.83, 24.0624, 52),
(11, 2008.7, -2065.99, 13.5468, 37);

-- --------------------------------------------------------

--
-- Table structure for table `marketlots`
--

CREATE TABLE `marketlots` (
  `marketID` int(11) NOT NULL,
  `marketInterior` int(11) DEFAULT 0,
  `marketVirtual` int(11) DEFAULT 0,
  `marketPosX` float DEFAULT 0,
  `marketPosY` float DEFAULT 0,
  `marketPosZ` float DEFAULT 0,
  `marketDeliverX` float DEFAULT 0,
  `marketDeliverY` float DEFAULT 0,
  `marketDeliverZ` float DEFAULT 0,
  `marketPrice1` int(11) DEFAULT 0,
  `marketPrice2` int(11) DEFAULT 0,
  `marketPrice3` int(11) DEFAULT 0,
  `marketPrice4` int(11) DEFAULT 0,
  `marketPrice5` int(11) DEFAULT 0,
  `marketPrice6` int(11) DEFAULT 0,
  `marketPrice7` int(11) DEFAULT 0,
  `marketPrice8` int(11) DEFAULT 0,
  `marketPrice9` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `materiallots`
--

CREATE TABLE `materiallots` (
  `materialID` int(11) NOT NULL,
  `materialPrice` int(11) DEFAULT 0,
  `materialProducts` int(11) DEFAULT 0,
  `materialInterior` int(11) DEFAULT 0,
  `materialVirtual` int(11) DEFAULT 0,
  `materialPosX` float DEFAULT 0,
  `materialPosY` float DEFAULT 0,
  `materialPosZ` float DEFAULT 0,
  `materialDeliverX` float DEFAULT 0,
  `materialDeliverY` float DEFAULT 0,
  `materialDeliverZ` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `namechanges`
--

CREATE TABLE `namechanges` (
  `id` int(11) NOT NULL,
  `OldName` varchar(24) DEFAULT NULL,
  `NewName` varchar(24) DEFAULT NULL,
  `Date` varchar(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `namechanges`
--

INSERT INTO `namechanges` (`id`, `OldName`, `NewName`, `Date`) VALUES
(1, 'Jack_Heversont', 'Jhon_Wiliam', '07/04/2025, 20:34'),
(2, 'Jack_Heversont', 'Christian_Bale', '04/07/2025, 01:18'),
(3, 'Jack_Heversont', 'Dennis_Wesovicy', '04/07/2025, 01:27'),
(4, 'Dennis_Wesovicy', 'Jack_Heversont', '04/07/2025, 01:27'),
(5, 'Christian_Bale', 'Jack_Heversont', '04/07/2025, 01:28'),
(6, 'Yuki_Haruto', 'Yuki_Kato', '04/07/2025, 02:37'),
(7, 'Yuki_Kato', 'Yuki_Haruto', '04/07/2025, 02:39');

-- --------------------------------------------------------

--
-- Table structure for table `object`
--

CREATE TABLE `object` (
  `objectID` int(11) NOT NULL,
  `objectModel` int(11) DEFAULT 0,
  `objectPosX` float DEFAULT 0,
  `objectPosY` float DEFAULT 0,
  `objectPosZ` float DEFAULT 0,
  `objectPosRX` float DEFAULT 0,
  `objectPosRY` float DEFAULT 0,
  `objectPosRZ` float DEFAULT 0,
  `objectVirtual` int(11) DEFAULT 0,
  `objectInterior` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plants`
--

CREATE TABLE `plants` (
  `plantID` int(11) NOT NULL,
  `plantType` int(11) DEFAULT 0,
  `plantDrugs` int(11) DEFAULT 0,
  `plantX` float DEFAULT 0,
  `plantY` float DEFAULT 0,
  `plantZ` float DEFAULT 0,
  `plantA` float DEFAULT 0,
  `plantInterior` int(11) DEFAULT 0,
  `plantWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plant_storage`
--

CREATE TABLE `plant_storage` (
  `id` int(11) NOT NULL,
  `stock` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plant_storage`
--

INSERT INTO `plant_storage` (`id`, `stock`) VALUES
(1, 4146);

-- --------------------------------------------------------

--
-- Table structure for table `pumps`
--

CREATE TABLE `pumps` (
  `id` int(11) DEFAULT 0,
  `pumpID` int(11) NOT NULL,
  `pumpPosX` float DEFAULT 0,
  `pumpPosY` float DEFAULT 0,
  `pumpPosZ` float DEFAULT 0,
  `pumpPosA` float DEFAULT 0,
  `pumpFuel` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `pumps`
--

INSERT INTO `pumps` (`id`, `pumpID`, `pumpPosX`, `pumpPosY`, `pumpPosZ`, `pumpPosA`, `pumpFuel`) VALUES
(6, 7, 1941.72, -1776.35, 13.9426, -178.685, 1432),
(6, 8, 1941.87, -1769.29, 13.9327, 179.035, 1388),
(10, 9, -92.3052, -1162.13, 2.7256, -23.7794, 1934),
(10, 13, -96.8811, -1173.5, 2.7427, -23.6845, 1875);

-- --------------------------------------------------------

--
-- Table structure for table `salary`
--

CREATE TABLE `salary` (
  `id` int(11) DEFAULT 0,
  `salaryID` int(11) NOT NULL,
  `salaryName` varchar(32) DEFAULT 'NoSalary',
  `salaryAmmount` int(11) DEFAULT 0,
  `salaryTime` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `salary`
--

INSERT INTO `salary` (`id`, `salaryID`, `salaryName`, `salaryAmmount`, `salaryTime`) VALUES
(5, 5, 'Sweeper Sidejob rute (A)', 5000, 1744027571),
(5, 6, 'Sweeper Sidejob rute (B)', 6500, 1744027973),
(3, 12, 'Sweeper Sidejob rute (A)', 5000, 1744033756),
(3, 14, 'Sweeper Sidejob rute (A)', 5000, 1744036387),
(6, 27, 'Sweeper Sidejob rute (B)', 6500, 1744894391),
(6, 28, 'Forklift Sidejob', 7500, 1744895105),
(6, 29, 'Trashmaster Sidejob: (20 Trash)', 12000, 1744895590),
(3, 30, 'Forklift Sidejob', 7500, 1745086918),
(2, 32, 'Restock 1 Cargo (Fish)', 1500, 1751501780),
(2, 33, 'Restock 1 Cargo (Fish)', 1500, 1751501918),
(2, 34, 'Restock 1 Cargo (Fish)', 1500, 1751506007),
(2, 35, 'Restock 1 Cargo (Fish)', 1500, 1751506018),
(15, 46, 'Forklift Sidejob', 15000, 1751570156),
(17, 49, 'Sweeper Sidejob rute (B)', 6500, 1751598573),
(20, 50, 'Sweeper Sidejob rute (A)', 5500, 1751599545),
(19, 51, 'Bus Sidejob Rute (B)', 40000, 1751600025),
(19, 53, 'Sweeper Sidejob rute (B)', 6500, 1751600363),
(19, 56, 'Restock 1 Cargo (Component)', 1500, 1751601390),
(19, 57, 'Restock 1 Cargo (Component)', 1500, 1751601402),
(19, 58, 'Restock 1 Cargo (Component)', 1500, 1751601413),
(19, 59, 'Restock 1 Cargo (Component)', 1500, 1751601423),
(19, 61, 'Restock 1 Cargo (Component)', 1500, 1751601440),
(20, 63, 'Sweeper Sidejob rute (A)', 5500, 1751612035),
(15, 65, 'Sweeper Sidejob rute (B)', 6500, 1751620912),
(16, 115, 'Trashmaster Sidejob: (20 Trash)', 12000, 1751675766),
(25, 155, 'Forklift Sidejob', 15000, 1751736768),
(10, 160, 'Restock 1 Cargo (Plant)', 1500, 1751738783),
(26, 174, 'Bus Sidejob Rute (B)', 40000, 1751781335),
(29, 223, 'Sweeper Sidejob rute (A)', 5500, 1751910559),
(30, 224, 'Sweeper Sidejob rute (B)', 6500, 1751910852),
(28, 225, 'Sweeper Sidejob rute (A)', 5500, 1751911786),
(12, 257, 'Trashmaster Sidejob: (20 Trash)', 12000, 1751959404),
(12, 266, 'Sweeper Sidejob rute (B)', 6500, 1751961308),
(31, 269, 'Sweeper Sidejob rute (B)', 6500, 1751962572),
(12, 271, 'Sweeper Sidejob rute (B)', 6500, 1751963361),
(12, 272, 'Bus Sidejob Rute (B)', 40000, 1751964327),
(12, 275, 'Trashmaster Sidejob: (20 Trash)', 12000, 1751964475),
(12, 277, 'Forklift Sidejob', 15000, 1751965251),
(22, 280, 'Trashmaster Sidejob: (20 Trash)', 12000, 1751990217),
(22, 282, 'Sweeper Sidejob rute (B)', 6500, 1751991091),
(22, 283, 'Forklift Sidejob', 15000, 1751991855),
(22, 284, 'Bus Sidejob Rute (B)', 40000, 1751992881),
(22, 285, 'Sweeper Sidejob rute (B)', 6500, 1751993458),
(22, 288, 'Forklift Sidejob', 15000, 1751994547),
(12, 289, 'Trashmaster Sidejob: (20 Trash)', 12000, 1751997085),
(22, 290, 'Trashmaster Sidejob: (20 Trash)', 12000, 1751997241),
(12, 291, 'Restock 1 Cargo (Plant)', 1500, 1751998361),
(24, 292, 'Restock 1 Cargo (Plant)', 1500, 1751998371),
(12, 293, 'Restock 1 Cargo (Plant)', 1500, 1751998371),
(12, 294, 'Restock 1 Cargo (Plant)', 1500, 1751998381),
(24, 295, 'Restock 1 Cargo (Plant)', 1500, 1751998388),
(12, 296, 'Restock 1 Cargo (Plant)', 1500, 1751998391),
(24, 297, 'Restock 1 Cargo (Plant)', 1500, 1751998402),
(12, 298, 'Restock 1 Cargo (Plant)', 1500, 1751998406),
(24, 299, 'Restock 1 Cargo (Plant)', 1500, 1751998414),
(12, 300, 'Restock 1 Cargo (Plant)', 1500, 1751998416),
(24, 301, 'Restock 1 Cargo (Plant)', 1500, 1751998425),
(12, 302, 'Restock 1 Cargo (Plant)', 1500, 1751998428),
(24, 303, 'Restock 1 Cargo (Plant)', 1500, 1751998436),
(12, 304, 'Restock 1 Cargo (Plant)', 1500, 1751998438),
(24, 305, 'Restock 1 Cargo (Plant)', 1500, 1751998445),
(12, 306, 'Restock 1 Cargo (Plant)', 1500, 1751998450),
(24, 307, 'Restock 1 Cargo (Plant)', 1500, 1751998457),
(12, 308, 'Restock 1 Cargo (Plant)', 1500, 1751998459),
(24, 309, 'Restock 1 Cargo (Plant)', 1500, 1751998467),
(24, 310, 'Restock 1 Cargo (Plant)', 1500, 1751998477),
(22, 311, 'Sweeper Sidejob rute (B)', 6500, 1751998683),
(24, 312, 'Forklift Sidejob', 15000, 1751999097),
(12, 313, 'Forklift Sidejob', 15000, 1751999209),
(24, 314, 'Trashmaster Sidejob: (20 Trash)', 12000, 1751999244),
(24, 315, 'Sweeper Sidejob rute (B)', 6500, 1752000018),
(22, 316, 'Sweeper Sidejob rute (B)', 6500, 1752000843),
(24, 317, 'Bus Sidejob Rute (B)', 40000, 1752001051),
(12, 318, 'Bus Sidejob Rute (B)', 40000, 1752001171),
(12, 319, 'Sweeper Sidejob rute (B)', 6500, 1752001437),
(24, 320, 'Trashmaster Sidejob: (20 Trash)', 12000, 1752002541),
(12, 321, 'Trashmaster Sidejob: (20 Trash)', 12000, 1752002898),
(24, 322, 'Forklift Sidejob', 15000, 1752003124),
(12, 323, 'Forklift Sidejob', 15000, 1752003450),
(24, 324, 'Sweeper Sidejob rute (B)', 6500, 1752003467),
(24, 325, 'Trashmaster Sidejob: (20 Trash)', 12000, 1752005617),
(19, 326, 'Restock 1 Cargo (Component)', 1500, 1752035817),
(19, 327, 'Restock 1 Cargo (Component)', 1500, 1752035832),
(19, 328, 'Restock 1 Cargo (Component)', 1500, 1752035849),
(19, 329, 'Restock 1 Cargo (Component)', 1500, 1752035861),
(19, 330, 'Restock 1 Cargo (Component)', 1500, 1752035872),
(19, 331, 'Restock 1 Cargo (Component)', 1500, 1752036246),
(19, 332, 'Restock 1 Cargo (Component)', 1500, 1752036255),
(19, 333, 'Restock 1 Cargo (Component)', 1500, 1752036265),
(19, 334, 'Restock 1 Cargo (Component)', 1500, 1752036275),
(19, 335, 'Restock 1 Cargo (Component)', 1500, 1752036287),
(26, 336, 'Sweeper Sidejob rute (B)', 6500, 1752036368),
(12, 337, 'Bus Sidejob Rute (B)', 40000, 1752038445),
(26, 338, 'Trashmaster Sidejob: (20 Trash)', 12000, 1752067402),
(26, 339, 'Forklift Sidejob', 15000, 1752068067),
(18, 349, 'Restock 1 Cargo (Component)', 1500, 1752139118),
(18, 350, 'Restock 1 Cargo (Component)', 1500, 1752139133),
(12, 351, 'Trashmaster Sidejob: (20 Trash)', 12000, 1752139149),
(18, 352, 'Restock 1 Cargo (Component)', 1500, 1752139158),
(18, 353, 'Restock 1 Cargo (Component)', 1500, 1752139169),
(18, 354, 'Restock 1 Cargo (Component)', 1500, 1752139179),
(18, 355, 'Restock 1 Cargo (Component)', 1500, 1752139191),
(18, 356, 'Restock 1 Cargo (Component)', 1500, 1752139202),
(18, 357, 'Restock 1 Cargo (Component)', 1500, 1752139216),
(18, 358, 'Restock 1 Cargo (Component)', 1500, 1752139233),
(18, 359, 'Restock 1 Cargo (Component)', 1500, 1752139297),
(12, 360, 'Bus Sidejob Rute (B)', 40000, 1752140443),
(18, 361, 'Trashmaster Sidejob: (20 Trash)', 12000, 1752140674),
(12, 362, 'Sweeper Sidejob rute (B)', 6500, 1752140887),
(18, 363, 'Restock 1 Cargo (Fish)', 1500, 1752141228),
(18, 364, 'Restock 1 Cargo (Fish)', 1500, 1752141239),
(18, 365, 'Restock 1 Cargo (Fish)', 1500, 1752141248),
(18, 366, 'Restock 1 Cargo (Fish)', 1500, 1752141259),
(18, 367, 'Restock 1 Cargo (Fish)', 1500, 1752141271),
(18, 368, 'Restock 1 Cargo (Fish)', 1500, 1752141281),
(18, 369, 'Restock 1 Cargo (Fish)', 1500, 1752141291),
(18, 370, 'Restock 1 Cargo (Fish)', 1500, 1752141302),
(18, 371, 'Restock 1 Cargo (Fish)', 1500, 1752141312),
(18, 372, 'Restock 1 Cargo (Fish)', 1500, 1752141325),
(18, 373, 'Restock 1 Cargo (Plant)', 1500, 1752142021),
(18, 374, 'Restock 1 Cargo (Plant)', 1500, 1752142031),
(18, 375, 'Restock 1 Cargo (Plant)', 1500, 1752142043),
(18, 376, 'Restock 1 Cargo (Plant)', 1500, 1752142051),
(18, 377, 'Restock 1 Cargo (Plant)', 1500, 1752142087),
(18, 378, 'Restock 1 Cargo (Plant)', 1500, 1752142097),
(18, 379, 'Restock 1 Cargo (Plant)', 1500, 1752142106),
(18, 380, 'Restock 1 Cargo (Plant)', 1500, 1752142118),
(18, 381, 'Restock 1 Cargo (Plant)', 1500, 1752142128),
(18, 382, 'Restock 1 Cargo (Plant)', 1500, 1752142138);

-- --------------------------------------------------------

--
-- Table structure for table `speedcameras`
--

CREATE TABLE `speedcameras` (
  `speedID` int(11) NOT NULL,
  `speedRange` float DEFAULT 0,
  `speedLimit` float DEFAULT 0,
  `speedX` float DEFAULT 0,
  `speedY` float DEFAULT 0,
  `speedZ` float DEFAULT 0,
  `speedAngle` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tickets`
--

CREATE TABLE `tickets` (
  `id` int(11) DEFAULT 0,
  `ticketID` int(11) NOT NULL,
  `ticketFee` int(11) DEFAULT 0,
  `ticketBy` varchar(24) DEFAULT NULL,
  `ticketDate` varchar(36) DEFAULT NULL,
  `ticketReason` varchar(32) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `treatmentlots`
--

CREATE TABLE `treatmentlots` (
  `treatmentID` int(11) NOT NULL,
  `treatmentPrice` int(11) DEFAULT 100,
  `treatmentPosX` float DEFAULT 0,
  `treatmentPosY` float DEFAULT 0,
  `treatmentPosZ` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `trees`
--

CREATE TABLE `trees` (
  `treeID` int(11) NOT NULL,
  `treeX` float DEFAULT 0,
  `treeY` float DEFAULT 0,
  `treeZ` float DEFAULT 0,
  `treeA` float DEFAULT 0,
  `treeInterior` int(11) DEFAULT 0,
  `treeWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `treestorage`
--

CREATE TABLE `treestorage` (
  `id` int(11) DEFAULT 0,
  `carTreeID` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `vehicles_toy`
--

CREATE TABLE `vehicles_toy` (
  `id` int(11) DEFAULT 0,
  `accID` int(11) NOT NULL,
  `accModel` int(11) DEFAULT -1,
  `accColor` int(11) DEFAULT 0,
  `accPosX` float DEFAULT 0,
  `accPosY` float DEFAULT 0,
  `accPosZ` float DEFAULT 0,
  `accPosRX` float DEFAULT 0,
  `accPosRY` float DEFAULT 0,
  `accPosRZ` float DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `vendors`
--

CREATE TABLE `vendors` (
  `vendorID` int(11) NOT NULL,
  `vendorType` int(11) DEFAULT 0,
  `vendorX` float DEFAULT 0,
  `vendorY` float DEFAULT 0,
  `vendorZ` float DEFAULT 0,
  `vendorA` float DEFAULT 0,
  `vendorInterior` int(11) DEFAULT 0,
  `vendorWorld` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `warrants`
--

CREATE TABLE `warrants` (
  `id` int(11) NOT NULL,
  `Suspect` varchar(24) DEFAULT NULL,
  `username` varchar(24) DEFAULT NULL,
  `Date` varchar(36) DEFAULT NULL,
  `Description` varchar(128) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `workshop`
--

CREATE TABLE `workshop` (
  `workshopID` int(11) NOT NULL,
  `workshopOwner` int(11) DEFAULT 0,
  `workshopPrice` int(11) DEFAULT 0,
  `workshopRanks` int(11) DEFAULT 0,
  `workshopPosX` float DEFAULT 0,
  `workshopPosY` float DEFAULT 0,
  `workshopPosZ` float DEFAULT 0,
  `workshopWorld` int(11) DEFAULT 0,
  `workshopInterior` int(11) DEFAULT 0,
  `workshopMoney` int(11) DEFAULT 0,
  `workshopRank1` varchar(32) DEFAULT NULL,
  `workshopRank2` varchar(32) DEFAULT NULL,
  `workshopRank3` varchar(32) DEFAULT NULL,
  `workshopRank4` varchar(32) DEFAULT NULL,
  `workshopRank5` varchar(32) DEFAULT NULL,
  `workshopRank6` varchar(32) DEFAULT NULL,
  `workshopRank7` varchar(32) DEFAULT NULL,
  `workshopRank8` varchar(32) DEFAULT NULL,
  `workshopRank9` varchar(32) DEFAULT NULL,
  `workshopRank10` varchar(32) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `acc`
--
ALTER TABLE `acc`
  ADD PRIMARY KEY (`accID`);

--
-- Indexes for table `accounts`
--
ALTER TABLE `accounts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `actor`
--
ALTER TABLE `actor`
  ADD PRIMARY KEY (`actorID`);

--
-- Indexes for table `animallots`
--
ALTER TABLE `animallots`
  ADD PRIMARY KEY (`animalID`);

--
-- Indexes for table `arrestpoints`
--
ALTER TABLE `arrestpoints`
  ADD PRIMARY KEY (`arrestID`);

--
-- Indexes for table `atm_machines`
--
ALTER TABLE `atm_machines`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `backpackitems`
--
ALTER TABLE `backpackitems`
  ADD PRIMARY KEY (`itemID`);

--
-- Indexes for table `backpacks`
--
ALTER TABLE `backpacks`
  ADD PRIMARY KEY (`backpackID`);

--
-- Indexes for table `billboards`
--
ALTER TABLE `billboards`
  ADD PRIMARY KEY (`bbID`);

--
-- Indexes for table `bizvipdealer`
--
ALTER TABLE `bizvipdealer`
  ADD PRIMARY KEY (`vipID`);

--
-- Indexes for table `businesses`
--
ALTER TABLE `businesses`
  ADD PRIMARY KEY (`bizID`);

--
-- Indexes for table `caracc`
--
ALTER TABLE `caracc`
  ADD PRIMARY KEY (`accID`);

--
-- Indexes for table `cars`
--
ALTER TABLE `cars`
  ADD PRIMARY KEY (`carID`);

--
-- Indexes for table `carsticker`
--
ALTER TABLE `carsticker`
  ADD PRIMARY KEY (`stickerID`);

--
-- Indexes for table `carstorage`
--
ALTER TABLE `carstorage`
  ADD PRIMARY KEY (`itemID`);

--
-- Indexes for table `characters`
--
ALTER TABLE `characters`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `component_storage`
--
ALTER TABLE `component_storage`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `contacts`
--
ALTER TABLE `contacts`
  ADD PRIMARY KEY (`contactID`);

--
-- Indexes for table `crates`
--
ALTER TABLE `crates`
  ADD PRIMARY KEY (`crateID`);

--
-- Indexes for table `cratestorage`
--
ALTER TABLE `cratestorage`
  ADD PRIMARY KEY (`cargoID`);

--
-- Indexes for table `dealervehicles`
--
ALTER TABLE `dealervehicles`
  ADD PRIMARY KEY (`vehID`);

--
-- Indexes for table `dealervipvehicles`
--
ALTER TABLE `dealervipvehicles`
  ADD PRIMARY KEY (`vehID`);

--
-- Indexes for table `detectors`
--
ALTER TABLE `detectors`
  ADD PRIMARY KEY (`detectorID`);

--
-- Indexes for table `dropped`
--
ALTER TABLE `dropped`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `entrances`
--
ALTER TABLE `entrances`
  ADD PRIMARY KEY (`entranceID`);

--
-- Indexes for table `factions`
--
ALTER TABLE `factions`
  ADD PRIMARY KEY (`factionID`);

--
-- Indexes for table `farmasilots`
--
ALTER TABLE `farmasilots`
  ADD PRIMARY KEY (`farmasiID`);

--
-- Indexes for table `farmer`
--
ALTER TABLE `farmer`
  ADD PRIMARY KEY (`farmerID`);

--
-- Indexes for table `fish_warehouse`
--
ALTER TABLE `fish_warehouse`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `furniture`
--
ALTER TABLE `furniture`
  ADD PRIMARY KEY (`furnitureID`);

--
-- Indexes for table `gates`
--
ALTER TABLE `gates`
  ADD PRIMARY KEY (`gateID`);

--
-- Indexes for table `gps`
--
ALTER TABLE `gps`
  ADD PRIMARY KEY (`locationID`);

--
-- Indexes for table `graffiti`
--
ALTER TABLE `graffiti`
  ADD PRIMARY KEY (`graffitiID`);

--
-- Indexes for table `groupperm`
--
ALTER TABLE `groupperm`
  ADD PRIMARY KEY (`commandID`);

--
-- Indexes for table `gunracks`
--
ALTER TABLE `gunracks`
  ADD PRIMARY KEY (`rackID`);

--
-- Indexes for table `houses`
--
ALTER TABLE `houses`
  ADD PRIMARY KEY (`houseID`);

--
-- Indexes for table `housestorage`
--
ALTER TABLE `housestorage`
  ADD PRIMARY KEY (`itemID`);

--
-- Indexes for table `impoundlots`
--
ALTER TABLE `impoundlots`
  ADD PRIMARY KEY (`impoundID`);

--
-- Indexes for table `insurancelots`
--
ALTER TABLE `insurancelots`
  ADD PRIMARY KEY (`insuranceID`);

--
-- Indexes for table `inventory`
--
ALTER TABLE `inventory`
  ADD PRIMARY KEY (`invID`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`jobID`);

--
-- Indexes for table `lumberlots`
--
ALTER TABLE `lumberlots`
  ADD PRIMARY KEY (`lumberID`);

--
-- Indexes for table `mapicons`
--
ALTER TABLE `mapicons`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `marketlots`
--
ALTER TABLE `marketlots`
  ADD PRIMARY KEY (`marketID`);

--
-- Indexes for table `materiallots`
--
ALTER TABLE `materiallots`
  ADD PRIMARY KEY (`materialID`);

--
-- Indexes for table `namechanges`
--
ALTER TABLE `namechanges`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `object`
--
ALTER TABLE `object`
  ADD PRIMARY KEY (`objectID`);

--
-- Indexes for table `plants`
--
ALTER TABLE `plants`
  ADD PRIMARY KEY (`plantID`);

--
-- Indexes for table `plant_storage`
--
ALTER TABLE `plant_storage`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `pumps`
--
ALTER TABLE `pumps`
  ADD PRIMARY KEY (`pumpID`);

--
-- Indexes for table `salary`
--
ALTER TABLE `salary`
  ADD PRIMARY KEY (`salaryID`);

--
-- Indexes for table `speedcameras`
--
ALTER TABLE `speedcameras`
  ADD PRIMARY KEY (`speedID`);

--
-- Indexes for table `tickets`
--
ALTER TABLE `tickets`
  ADD PRIMARY KEY (`ticketID`);

--
-- Indexes for table `treatmentlots`
--
ALTER TABLE `treatmentlots`
  ADD PRIMARY KEY (`treatmentID`);

--
-- Indexes for table `trees`
--
ALTER TABLE `trees`
  ADD PRIMARY KEY (`treeID`);

--
-- Indexes for table `treestorage`
--
ALTER TABLE `treestorage`
  ADD PRIMARY KEY (`carTreeID`);

--
-- Indexes for table `vehicles_toy`
--
ALTER TABLE `vehicles_toy`
  ADD PRIMARY KEY (`accID`);

--
-- Indexes for table `vendors`
--
ALTER TABLE `vendors`
  ADD PRIMARY KEY (`vendorID`);

--
-- Indexes for table `warrants`
--
ALTER TABLE `warrants`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `workshop`
--
ALTER TABLE `workshop`
  ADD PRIMARY KEY (`workshopID`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `acc`
--
ALTER TABLE `acc`
  MODIFY `accID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `accounts`
--
ALTER TABLE `accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=49;

--
-- AUTO_INCREMENT for table `actor`
--
ALTER TABLE `actor`
  MODIFY `actorID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `animallots`
--
ALTER TABLE `animallots`
  MODIFY `animalID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `arrestpoints`
--
ALTER TABLE `arrestpoints`
  MODIFY `arrestID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `atm_machines`
--
ALTER TABLE `atm_machines`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `backpackitems`
--
ALTER TABLE `backpackitems`
  MODIFY `itemID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `backpacks`
--
ALTER TABLE `backpacks`
  MODIFY `backpackID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `billboards`
--
ALTER TABLE `billboards`
  MODIFY `bbID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bizvipdealer`
--
ALTER TABLE `bizvipdealer`
  MODIFY `vipID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `businesses`
--
ALTER TABLE `businesses`
  MODIFY `bizID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `caracc`
--
ALTER TABLE `caracc`
  MODIFY `accID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `cars`
--
ALTER TABLE `cars`
  MODIFY `carID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=75;

--
-- AUTO_INCREMENT for table `carsticker`
--
ALTER TABLE `carsticker`
  MODIFY `stickerID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `carstorage`
--
ALTER TABLE `carstorage`
  MODIFY `itemID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `characters`
--
ALTER TABLE `characters`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

--
-- AUTO_INCREMENT for table `component_storage`
--
ALTER TABLE `component_storage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `contacts`
--
ALTER TABLE `contacts`
  MODIFY `contactID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `crates`
--
ALTER TABLE `crates`
  MODIFY `crateID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=314;

--
-- AUTO_INCREMENT for table `cratestorage`
--
ALTER TABLE `cratestorage`
  MODIFY `cargoID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=159;

--
-- AUTO_INCREMENT for table `dealervehicles`
--
ALTER TABLE `dealervehicles`
  MODIFY `vehID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `dealervipvehicles`
--
ALTER TABLE `dealervipvehicles`
  MODIFY `vehID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `detectors`
--
ALTER TABLE `detectors`
  MODIFY `detectorID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `dropped`
--
ALTER TABLE `dropped`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `entrances`
--
ALTER TABLE `entrances`
  MODIFY `entranceID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `factions`
--
ALTER TABLE `factions`
  MODIFY `factionID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `farmasilots`
--
ALTER TABLE `farmasilots`
  MODIFY `farmasiID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `farmer`
--
ALTER TABLE `farmer`
  MODIFY `farmerID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1202;

--
-- AUTO_INCREMENT for table `furniture`
--
ALTER TABLE `furniture`
  MODIFY `furnitureID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `gates`
--
ALTER TABLE `gates`
  MODIFY `gateID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `gps`
--
ALTER TABLE `gps`
  MODIFY `locationID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `graffiti`
--
ALTER TABLE `graffiti`
  MODIFY `graffitiID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `groupperm`
--
ALTER TABLE `groupperm`
  MODIFY `commandID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=624;

--
-- AUTO_INCREMENT for table `gunracks`
--
ALTER TABLE `gunracks`
  MODIFY `rackID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `houses`
--
ALTER TABLE `houses`
  MODIFY `houseID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `housestorage`
--
ALTER TABLE `housestorage`
  MODIFY `itemID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `impoundlots`
--
ALTER TABLE `impoundlots`
  MODIFY `impoundID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `insurancelots`
--
ALTER TABLE `insurancelots`
  MODIFY `insuranceID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `inventory`
--
ALTER TABLE `inventory`
  MODIFY `invID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=309;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `jobID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `lumberlots`
--
ALTER TABLE `lumberlots`
  MODIFY `lumberID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `marketlots`
--
ALTER TABLE `marketlots`
  MODIFY `marketID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `materiallots`
--
ALTER TABLE `materiallots`
  MODIFY `materialID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `namechanges`
--
ALTER TABLE `namechanges`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `object`
--
ALTER TABLE `object`
  MODIFY `objectID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `plants`
--
ALTER TABLE `plants`
  MODIFY `plantID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pumps`
--
ALTER TABLE `pumps`
  MODIFY `pumpID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `salary`
--
ALTER TABLE `salary`
  MODIFY `salaryID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=383;

--
-- AUTO_INCREMENT for table `speedcameras`
--
ALTER TABLE `speedcameras`
  MODIFY `speedID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tickets`
--
ALTER TABLE `tickets`
  MODIFY `ticketID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `treatmentlots`
--
ALTER TABLE `treatmentlots`
  MODIFY `treatmentID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `trees`
--
ALTER TABLE `trees`
  MODIFY `treeID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `treestorage`
--
ALTER TABLE `treestorage`
  MODIFY `carTreeID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `vehicles_toy`
--
ALTER TABLE `vehicles_toy`
  MODIFY `accID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `vendors`
--
ALTER TABLE `vendors`
  MODIFY `vendorID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `warrants`
--
ALTER TABLE `warrants`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `workshop`
--
ALTER TABLE `workshop`
  MODIFY `workshopID` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
