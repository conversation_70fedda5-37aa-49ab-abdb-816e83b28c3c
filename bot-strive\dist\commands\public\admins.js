"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const CharacterRepository_1 = require("../../database/repositories/CharacterRepository");
const discord_1 = require("../../types/discord");
const embeds_1 = require("../../utils/embeds");
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('admins')
        .setDescription('Show online administrators in the server')
        .addBooleanOption(option => option.setName('show_hidden')
        .setDescription('Show hidden admins (admin only)')
        .setRequired(false)),
    async execute(interaction, client) {
        await interaction.deferReply();
        try {
            const botClient = client || interaction.client;
            const characterRepo = new CharacterRepository_1.CharacterRepository(botClient.db, botClient.logger);
            const showHidden = interaction.options.getBoolean('show_hidden') || false;
            const isAdmin = interaction.memberPermissions?.has('Administrator') || false;
            if (showHidden && !isAdmin) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Permission Denied', '❌ You need Administrator permission to view hidden admins.', false);
                return;
            }
            const adminsResult = await characterRepo.getOnlineAdmins();
            if (!adminsResult.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Database Error', 'Failed to retrieve admin information. Please try again later.', false);
                return;
            }
            const onlineAdmins = adminsResult.data || [];
            const visibleAdmins = showHidden || isAdmin
                ? onlineAdmins
                : onlineAdmins.filter((admin) => !admin.isHidden);
            if (visibleAdmins.length === 0) {
                const embed = new discord_js_1.EmbedBuilder()
                    .setTitle('👮 Online Administrators')
                    .setDescription('❌ **No administrators are currently online.**')
                    .setColor(discord_1.EmbedColors.WARNING)
                    .addFields([
                    {
                        name: '📞 Need Help?',
                        value: [
                            // '• Use `/ticket` to create a support ticket',
                            '• Check our Discord for announcements',
                            '• Visit our website for more information'
                        ].join('\n'),
                        inline: false
                    }
                ])
                    .setTimestamp()
                    .setFooter({ text: 'Strive Roleplay - Admin Status' });
                await interaction.editReply({ embeds: [embed] });
                return;
            }
            const adminsByLevel = {};
            visibleAdmins.forEach((admin) => {
                if (!adminsByLevel[admin.adminLevel]) {
                    adminsByLevel[admin.adminLevel] = [];
                }
                adminsByLevel[admin.adminLevel].push(admin);
            });
            const embed = new discord_js_1.EmbedBuilder()
                .setTitle('👮 Online Administrators')
                .setDescription(`**${visibleAdmins.length}** administrator${visibleAdmins.length !== 1 ? 's' : ''} currently online`)
                .setColor(discord_1.EmbedColors.SUCCESS)
                .setTimestamp()
                .setFooter({ text: 'Strive Roleplay - Admin Status' });
            const sortedLevels = Object.keys(adminsByLevel)
                .map(Number)
                .sort((a, b) => b - a);
            for (const level of sortedLevels) {
                const admins = adminsByLevel[level];
                if (!admins || admins.length === 0)
                    continue;
                const levelName = characterRepo.getAdminLevelName(level);
                const adminList = admins.map((admin) => {
                    const lastLoginTime = characterRepo.formatTimestamp(admin.lastLogin);
                    const hiddenIndicator = admin.isHidden ? ' 🔒' : '';
                    return `**${admin.character}** (${admin.username})${hiddenIndicator}\n` +
                        `└ Last seen: ${lastLoginTime}`;
                }).join('\n\n');
                embed.addFields([{
                        name: `${levelName} (${admins.length})`,
                        value: adminList,
                        inline: false
                    }]);
            }
            if (!showHidden && !isAdmin && onlineAdmins.some((admin) => admin.isHidden)) {
                embed.addFields([{
                        name: '🔒 Hidden Admins',
                        value: 'Some administrators may be hidden from this list.',
                        inline: false
                    }]);
            }
            embed.addFields([{
                    name: '📞 Need Admin Assistance?',
                    value: [
                        // '• Use `/ticket` to create a support ticket',
                        '• Send a private message to any online admin',
                        '• Use in-game `/report` command for urgent issues'
                    ].join('\n'),
                    inline: false
                }]);
            await interaction.editReply({ embeds: [embed] });
            botClient.logger.info('Admins command executed', {
                userId: interaction.user.id,
                username: interaction.user.username,
                guildId: interaction.guildId,
                showHidden,
                isAdmin,
                totalAdmins: onlineAdmins.length,
                visibleAdmins: visibleAdmins.length
            });
        }
        catch (error) {
            const botClient = client || interaction.client;
            botClient.logger.error('Error in admins command', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
            await embeds_1.InteractionUtils.replyError(interaction, 'Command Error', 'An unexpected error occurred while retrieving admin information.', false);
        }
    },
};
//# sourceMappingURL=admins.js.map