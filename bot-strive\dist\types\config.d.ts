import { z } from 'zod';
/**
 * Environment configuration schema using Z<PERSON> for validation
 */
export declare const ConfigSchema: z.ZodObject<{
    DISCORD_TOKEN: z.ZodString;
    DISCORD_CLIENT_ID: z.ZodString;
    DISCORD_GUILD_ID: z.ZodString;
    BOT_PREFIX: z.Zod<PERSON>efault<z.ZodString>;
    NODE_ENV: z.ZodDefault<z.ZodEnum<["development", "production", "test"]>>;
    LOG_LEVEL: z.ZodDefault<z.ZodEnum<["error", "warn", "info", "debug"]>>;
    DB_HOST: z.ZodDefault<z.ZodString>;
    DB_PORT: z.ZodDefault<z.ZodNumber>;
    DB_USER: z.ZodString;
    DB_PASSWORD: z.ZodDefault<z.ZodString>;
    DB_NAME: z.ZodString;
    SAMP_SERVER_IP: z.ZodDefault<z.ZodString>;
    SAMP_SERVER_PORT: z.<PERSON>odDefault<z.ZodNumber>;
    ERROR_WEBHOOK_ID: z.ZodOptional<z.ZodString>;
    ERROR_WEBHOOK_TOKEN: z.ZodOptional<z.ZodString>;
    ROLE_UCP: z.ZodOptional<z.ZodString>;
    ROLE_UNVERIFIED: z.ZodOptional<z.ZodString>;
    WELCOME_ENABLED: z.ZodDefault<z.ZodBoolean>;
    WELCOME_CHANNEL_ID: z.ZodOptional<z.ZodString>;
    WELCOME_MESSAGE: z.ZodOptional<z.ZodString>;
    WELCOME_BANNER_URL: z.ZodOptional<z.ZodString>;
    LEAVE_ENABLED: z.ZodDefault<z.ZodBoolean>;
    CHANGELOG_CHANNEL_ID: z.ZodOptional<z.ZodString>;
    ICON_URL: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    DISCORD_TOKEN: string;
    DISCORD_CLIENT_ID: string;
    DISCORD_GUILD_ID: string;
    BOT_PREFIX: string;
    NODE_ENV: "development" | "production" | "test";
    LOG_LEVEL: "error" | "warn" | "info" | "debug";
    DB_HOST: string;
    DB_PORT: number;
    DB_USER: string;
    DB_PASSWORD: string;
    DB_NAME: string;
    SAMP_SERVER_IP: string;
    SAMP_SERVER_PORT: number;
    WELCOME_ENABLED: boolean;
    LEAVE_ENABLED: boolean;
    ERROR_WEBHOOK_ID?: string | undefined;
    ERROR_WEBHOOK_TOKEN?: string | undefined;
    ROLE_UCP?: string | undefined;
    ROLE_UNVERIFIED?: string | undefined;
    WELCOME_CHANNEL_ID?: string | undefined;
    WELCOME_MESSAGE?: string | undefined;
    WELCOME_BANNER_URL?: string | undefined;
    CHANGELOG_CHANNEL_ID?: string | undefined;
    ICON_URL?: string | undefined;
}, {
    DISCORD_TOKEN: string;
    DISCORD_CLIENT_ID: string;
    DISCORD_GUILD_ID: string;
    DB_USER: string;
    DB_NAME: string;
    BOT_PREFIX?: string | undefined;
    NODE_ENV?: "development" | "production" | "test" | undefined;
    LOG_LEVEL?: "error" | "warn" | "info" | "debug" | undefined;
    DB_HOST?: string | undefined;
    DB_PORT?: number | undefined;
    DB_PASSWORD?: string | undefined;
    SAMP_SERVER_IP?: string | undefined;
    SAMP_SERVER_PORT?: number | undefined;
    ERROR_WEBHOOK_ID?: string | undefined;
    ERROR_WEBHOOK_TOKEN?: string | undefined;
    ROLE_UCP?: string | undefined;
    ROLE_UNVERIFIED?: string | undefined;
    WELCOME_ENABLED?: boolean | undefined;
    WELCOME_CHANNEL_ID?: string | undefined;
    WELCOME_MESSAGE?: string | undefined;
    WELCOME_BANNER_URL?: string | undefined;
    LEAVE_ENABLED?: boolean | undefined;
    CHANGELOG_CHANNEL_ID?: string | undefined;
    ICON_URL?: string | undefined;
}>;
/**
 * Inferred type from the configuration schema
 */
export type Config = z.infer<typeof ConfigSchema>;
/**
 * Database connection configuration
 */
export interface DatabaseConfig {
    host: string;
    port: number;
    user: string;
    password: string;
    database: string;
    connectionLimit: number;
    acquireTimeout?: number;
    timeout?: number;
    reconnect?: boolean;
}
/**
 * SAMP server configuration
 */
export interface SampServerConfig {
    ip: string;
    port: number;
}
//# sourceMappingURL=config.d.ts.map