import { Logger } from '../types/common';
import { WebhookConfig } from '../types/discord';
/**
 * Centralized error handling utility
 */
export declare class ErrorHandler {
    private webhook;
    private logger;
    private errorCount;
    private lastErrorTime;
    private readonly ERROR_RATE_LIMIT;
    private readonly ERROR_RATE_WINDOW;
    constructor(logger: Logger, webhookConfig?: WebhookConfig);
    /**
     * <PERSON>le and log an error
     */
    handleError(error: Error, context?: Record<string, unknown>): Promise<void>;
    /**
     * <PERSON>le unhandled promise rejections
     */
    handleUnhandledRejection(reason: unknown, promise: Promise<unknown>): Promise<void>;
    /**
     * Handle uncaught exceptions
     */
    handleUncaughtException(error: Error): Promise<void>;
    /**
     * Send error information to Discord webhook
     */
    private sendErrorToWebhook;
    /**
     * Create a safe error message for users (without sensitive information)
     */
    static createUserFriendlyError(error: Error): string;
    /**
     * Wrap an async function with error handling
     */
    static wrapAsync<T extends unknown[], R>(fn: (...args: T) => Promise<R>, errorHandler: (error: Error) => void | Promise<void>): (...args: T) => Promise<R | undefined>;
    /**
     * Create a retry wrapper for functions that might fail
     */
    static createRetryWrapper<T extends unknown[], R>(fn: (...args: T) => Promise<R>, maxRetries?: number, delay?: number): (...args: T) => Promise<R>;
    /**
     * Destroy the webhook client
     */
    destroy(): void;
}
//# sourceMappingURL=errorHandler.d.ts.map