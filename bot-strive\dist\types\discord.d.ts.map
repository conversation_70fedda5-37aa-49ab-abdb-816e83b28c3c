{"version": 3, "file": "discord.d.ts", "sourceRoot": "", "sources": ["../../src/types/discord.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,MAAM,EACN,UAAU,EACV,mBAAmB,EACnB,2BAA2B,EAC3B,iBAAiB,EACjB,sBAAsB,EACtB,yBAAyB,EACzB,iCAAiC,EACjC,oCAAoC,EACrC,MAAM,YAAY,CAAC;AAEpB;;GAEG;AACH,MAAM,WAAW,cAAe,SAAQ,MAAM;IAC5C,QAAQ,EAAE,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC3C,OAAO,EAAE,UAAU,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAC3C,MAAM,EAAE,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACzC,MAAM,EAAE,OAAO,UAAU,EAAE,MAAM,CAAC;IAClC,OAAO,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;CACjC;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,mBAAmB,GAAG,yBAAyB,CAAC;IACtD,OAAO,EAAE,CAAC,WAAW,EAAE,2BAA2B,EAAE,MAAM,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACnF,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,SAAS,CAAC,EAAE,OAAO,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC5B,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,CAAC,WAAW,EAAE,iBAAiB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAC3D,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,CAAC,WAAW,EAAE,sBAAsB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAChE,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;CACxB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,OAAO,CAAC;IACd,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;CAC5C;AAED;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,sBAAsB,GAAG,yBAAyB,CAAC;AAEpF,MAAM,WAAW,sBAAsB;IACrC,IAAI,EAAE,yBAAyB,CAAC;IAChC,OAAO,EAAE,CAAC,WAAW,EAAE,iCAAiC,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAC3E,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,SAAS,CAAC,EAAE,OAAO,CAAC;CACrB;AAED,MAAM,WAAW,yBAAyB;IACxC,IAAI,EAAE,yBAAyB,CAAC;IAChC,OAAO,EAAE,CAAC,WAAW,EAAE,oCAAoC,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9E,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,SAAS,CAAC,EAAE,OAAO,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,KAAK,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,WAAW;IACrB,OAAO,QAAW;IAClB,KAAK,WAAW;IAChB,OAAO,WAAW;IAClB,IAAI,QAAW;IACf,OAAO,UAAW;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,SAAS;IACxB,QAAQ,EAAE,OAAO,CAAC;IAClB,cAAc,EAAE,MAAM,CAAC;IACvB,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC5B,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAClC,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,KAAK,CAAC;QACb,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,MAAM,CAAC,EAAE,KAAK,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;YACd,MAAM,CAAC,EAAE,OAAO,CAAC;SAClB,CAAC,CAAC;KACJ,CAAC,CAAC;CACJ"}