/**
 * Database model interfaces and types
 */
/**
 * User account model from the database (SAMP gamemode compatible)
 */
export interface Account {
    id: number;
    username: string;
    password?: string;
    email?: string;
    createdAt?: string;
    LoginDate?: string;
    IP?: string;
    Admin?: number;
    AdminHide?: number;
    VIP?: number;
    VIPTime?: number;
    VIPCoin?: number;
    isVerified?: number;
    refresh_token?: string;
    updatedAt?: string;
    discord_id: string;
    discord_code: number;
}
/**
 * Input type for creating a new account
 */
export interface CreateAccountInput {
    username: string;
    discord_id: string;
    discord_code: number;
    password?: string;
}
/**
 * Input type for updating an account
 */
export interface UpdateAccountInput {
    username?: string;
    discord_code?: number;
    password?: string;
}
/**
 * Query result wrapper for database operations
 */
export interface QueryResult<T = unknown> {
    success: boolean;
    data?: T;
    error?: string;
    affectedRows?: number;
    insertId?: number;
}
/**
 * Database connection pool interface
 */
export interface DatabasePool {
    query<T = unknown>(sql: string, values?: unknown[]): Promise<QueryResult<T>>;
    execute<T = unknown>(sql: string, values?: unknown[]): Promise<QueryResult<T>>;
    getConnection(): Promise<DatabaseConnection>;
    escape(value: unknown): string;
    end(): Promise<void>;
}
/**
 * Database connection interface
 */
export interface DatabaseConnection {
    query<T = unknown>(sql: string, values?: unknown[]): Promise<QueryResult<T>>;
    execute<T = unknown>(sql: string, values?: unknown[]): Promise<QueryResult<T>>;
    beginTransaction(): Promise<void>;
    commit(): Promise<void>;
    rollback(): Promise<void>;
    release(): void;
}
/**
 * Database error types
 */
export declare enum DatabaseErrorType {
    CONNECTION_ERROR = "CONNECTION_ERROR",
    QUERY_ERROR = "QUERY_ERROR",
    VALIDATION_ERROR = "VALIDATION_ERROR",
    DUPLICATE_ENTRY = "DUPLICATE_ENTRY",
    NOT_FOUND = "NOT_FOUND"
}
/**
 * Custom database error class
 */
export declare class DatabaseError extends Error {
    type: DatabaseErrorType;
    originalError?: Error | undefined;
    constructor(message: string, type: DatabaseErrorType, originalError?: Error | undefined);
}
//# sourceMappingURL=database.d.ts.map