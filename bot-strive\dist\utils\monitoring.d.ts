import { Logger } from '../types/common';
import { BotClient } from '../client/BotClient';
/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
    uptime: number;
    memoryUsage: NodeJS.MemoryUsage;
    commandsExecuted: number;
    interactionsHandled: number;
    databaseQueries: number;
    errors: number;
    lastError?: string;
    serverQuerySuccess: number;
    serverQueryFailed: number;
}
/**
 * Monitoring and analytics system
 */
export declare class MonitoringSystem {
    private logger;
    private metrics;
    private startTime;
    private monitoringInterval?;
    private isRunning;
    constructor(_client: BotClient, logger: Logger);
    /**
     * Start monitoring system
     */
    start(): void;
    /**
     * Stop monitoring system
     */
    stop(): void;
    /**
     * Update performance metrics
     */
    private updateMetrics;
    /**
     * Log current metrics
     */
    private logMetrics;
    /**
     * Record command execution
     */
    recordCommand(): void;
    /**
     * Record interaction handling
     */
    recordInteraction(): void;
    /**
     * Record database query
     */
    recordDatabaseQuery(): void;
    /**
     * Record error
     */
    recordError(error: string): void;
    /**
     * Record server query success
     */
    recordServerQuerySuccess(): void;
    /**
     * Record server query failure
     */
    recordServerQueryFailed(): void;
    /**
     * Get server query success rate
     */
    getServerQuerySuccessRate(): string;
    /**
     * Get server query success rate (private method for internal use)
     */
    private getServerQuerySuccessRateInternal;
    /**
     * Get current metrics
     */
    getMetrics(): PerformanceMetrics;
    /**
     * Generate health report
     */
    generateHealthReport(): {
        status: 'healthy' | 'warning' | 'critical';
        issues: string[];
        metrics: PerformanceMetrics;
    };
    /**
     * Log health report
     */
    logHealthReport(): void;
}
//# sourceMappingURL=monitoring.d.ts.map