"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const discord_js_1 = require("discord.js");
const discord_1 = require("../types/discord");
const roleManager_1 = require("../utils/roleManager");
/**
 * Guild member add event handler
 * Automatically assigns unverified role and sends welcome message to new members
 */
function default_1(client) {
    client.on('guildMemberAdd', async (member) => {
        try {
            if (member.user.bot) {
                client.logger.debug('Skipping welcome message and role assignment for bot', {
                    userId: member.user.id,
                    username: member.user.username
                });
                return;
            }
            if (client.config.ROLE_UNVERIFIED) {
                client.logger.info('Attempting to assign unverified role', {
                    userId: member.user.id,
                    username: member.user.username,
                    guildId: member.guild.id,
                    roleId: client.config.ROLE_UNVERIFIED
                });
                const roleManager = new roleManager_1.RoleManager(client.logger);
                const roleResult = await roleManager.assignRole(member.guild, member.user.id, client.config.ROLE_UNVERIFIED, 'member_join');
                if (roleResult.success && roleResult.roleAssigned) {
                    client.logger.info('Unverified role assigned to new member', {
                        userId: member.user.id,
                        username: member.user.username,
                        guildId: member.guild.id,
                        roleId: client.config.ROLE_UNVERIFIED,
                        roleName: roleResult.roleName
                    });
                }
                else if (roleResult.error) {
                    client.logger.warn('Failed to assign unverified role to new member', {
                        userId: member.user.id,
                        username: member.user.username,
                        guildId: member.guild.id,
                        roleId: client.config.ROLE_UNVERIFIED,
                        error: roleResult.error
                    });
                }
            }
            else {
                client.logger.info('Auto unverified role skipped - ROLE_UNVERIFIED not configured', {
                    userId: member.user.id,
                    username: member.user.username,
                    guildId: member.guild.id
                });
            }
            await sendWelcomeMessage(member, client);
        }
        catch (error) {
            client.logger.error('Error in guildMemberAdd event', {
                error: error.message,
                stack: error.stack,
                userId: member.user.id,
                username: member.user.username,
                guildId: member.guild.id
            });
        }
    });
}
/**
 * Send welcome message to new member
 */
async function sendWelcomeMessage(member, client) {
    try {
        if (!client.config.WELCOME_ENABLED) {
            client.logger.debug('Welcome message skipped - WELCOME_ENABLED is false', {
                userId: member.user.id,
                guildId: member.guild.id
            });
            return;
        }
        if (!client.config.WELCOME_CHANNEL_ID) {
            client.logger.warn('Welcome message skipped - WELCOME_CHANNEL_ID not configured', {
                userId: member.user.id,
                guildId: member.guild.id
            });
            return;
        }
        const welcomeChannel = await member.guild.channels.fetch(client.config.WELCOME_CHANNEL_ID);
        if (!welcomeChannel || !welcomeChannel.isTextBased()) {
            client.logger.warn('Welcome channel not found or not text-based', {
                channelId: client.config.WELCOME_CHANNEL_ID,
                userId: member.user.id,
                guildId: member.guild.id
            });
            return;
        }
        const welcomeEmbed = new discord_js_1.EmbedBuilder()
            .setTitle(`🎉 Welcome to ${member.guild.name}!`)
            .setDescription(client.config.WELCOME_MESSAGE ||
            `Welcome ${member.user.username}! We're glad to have you here.\n\n` +
                `Please check out our rules and feel free to introduce yourself!\n` +
                `Use the account panel below to register for our SAMP server.`)
            .setColor(discord_1.EmbedColors.SUCCESS)
            .addFields([
            {
                name: '👤 New Member',
                value: [
                    `**User:** ${member.user.username}`,
                    `**Tag:** ${member.user.tag}`,
                    `**ID:** \`${member.user.id}\``,
                    `**Joined:** <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`
                ].join('\n'),
                inline: true
            },
            {
                name: '📊 Server Stats',
                value: [
                    `**Total Members:** ${member.guild.memberCount}`,
                    `**You are member:** #${member.guild.memberCount}`,
                    `**Server Created:** <t:${Math.floor(member.guild.createdTimestamp / 1000)}:D>`
                ].join('\n'),
                inline: true
            }
        ])
            .setThumbnail(member.user.displayAvatarURL())
            .setTimestamp()
            .setTimestamp();
        const guildIcon = member.guild.iconURL();
        if (guildIcon) {
            welcomeEmbed.setFooter({
                text: `Welcome to ${member.guild.name}`,
                iconURL: guildIcon
            });
        }
        else {
            welcomeEmbed.setFooter({
                text: `Welcome to ${member.guild.name}`
            });
        }
        if (client.config.WELCOME_BANNER_URL) {
            welcomeEmbed.setImage(client.config.WELCOME_BANNER_URL);
        }
        await welcomeChannel.send({
            content: `${member.user} welcome to the server! 🎉`,
            embeds: [welcomeEmbed]
        });
        client.logger.info('Welcome message sent successfully', {
            userId: member.user.id,
            username: member.user.username,
            guildId: member.guild.id,
            channelId: client.config.WELCOME_CHANNEL_ID
        });
    }
    catch (error) {
        client.logger.error('Error sending welcome message', {
            error: error.message,
            stack: error.stack,
            userId: member.user.id,
            guildId: member.guild.id,
            channelId: client.config.WELCOME_CHANNEL_ID
        });
    }
}
//# sourceMappingURL=guildMemberAdd.js.map