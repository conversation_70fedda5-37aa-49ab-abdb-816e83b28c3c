"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationUtils = void 0;
/**
 * Validation utilities for user input
 */
class ValidationUtils {
    /**
     * Validate username according to UCP rules (for account registration)
     */
    static validateUsername(username) {
        const errors = [];
        if (username.length < 3) {
            errors.push('Username must be at least 3 characters long');
        }
        if (username.length > 24) {
            errors.push('Username must be no more than 24 characters long');
        }
        if (username.includes('_')) {
            errors.push('Username cannot contain underscores (this is for UCP account, not character name)');
        }
        if (username.includes(' ')) {
            errors.push('Username cannot contain spaces');
        }
        if (!/^[a-zA-Z0-9]+$/.test(username)) {
            errors.push('Username can only contain letters and numbers');
        }
        if (/^\d+$/.test(username)) {
            errors.push('Username cannot be only numbers');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Validate Discord ID format
     */
    static validateDiscordId(discordId) {
        const errors = [];
        if (!/^\d{17,19}$/.test(discordId)) {
            errors.push('Invalid Discord ID format');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Validate verification code
     */
    static validateVerificationCode(code) {
        const errors = [];
        if (code < 1 || code > 999999) {
            errors.push('Verification code must be between 1 and 999999');
        }
        if (!Number.isInteger(code)) {
            errors.push('Verification code must be a whole number');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Sanitize user input to prevent injection attacks
     */
    static sanitizeInput(input) {
        return input
            .trim()
            .replace(/[<>]/g, '') // Remove potential HTML tags
            .replace(/['"]/g, '') // Remove quotes
            .substring(0, 100); // Limit length
    }
    /**
     * Check if a string is empty or only whitespace
     */
    static isEmpty(value) {
        return !value || value.trim().length === 0;
    }
    /**
     * Validate email format (if needed for future features)
     */
    static validateEmail(email) {
        const errors = [];
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            errors.push('Invalid email format');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Validate URL format
     */
    static validateUrl(url) {
        const errors = [];
        try {
            new URL(url);
        }
        catch {
            errors.push('Invalid URL format');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Combine multiple validation results
     */
    static combineValidationResults(...results) {
        const allErrors = results.flatMap(result => result.errors);
        return {
            isValid: allErrors.length === 0,
            errors: allErrors,
        };
    }
    /**
     * Create a validation result from a boolean and error message
     */
    static createResult(isValid, errorMessage) {
        return {
            isValid,
            errors: isValid ? [] : [errorMessage || 'Validation failed'],
        };
    }
}
exports.ValidationUtils = ValidationUtils;
//# sourceMappingURL=validation.js.map