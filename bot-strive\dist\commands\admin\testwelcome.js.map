{"version": 3, "file": "testwelcome.js", "sourceRoot": "", "sources": ["../../../src/commands/admin/testwelcome.ts"], "names": [], "mappings": ";;;AAAA,2CAAmG;AAEnG,+CAAsD;AAOzC,QAAA,OAAO,GAAY;IAC9B,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC5B,OAAO,CAAC,aAAa,CAAC;SACtB,cAAc,CAAC,gDAAgD,CAAC;SAChE,eAAe,CAAC,MAAM,CAAC,EAAE,CACxB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;SACnB,cAAc,CAAC,qBAAqB,CAAC;SACrC,WAAW,CAAC,IAAI,CAAC;SACjB,UAAU,CACT,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAE,EAC7C,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,EACzC,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,CAC1C,CACJ;SACA,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IAEjE,KAAK,CAAC,OAAO,CAAC,WAAwC,EAAE,MAAkB;QACxE,MAAM,WAAW,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAK,WAAW,CAAC,MAAc,CAAC;YACxD,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAE7D,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC3E,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,mBAAmB,EACnB,0DAA0D,EAC1D,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC1B,MAAM,UAAU,GAAG;oBACjB,wBAAwB,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE;oBAC1D,2BAA2B,SAAS,CAAC,MAAM,CAAC,kBAAkB,IAAI,SAAS,EAAE;oBAC7E,wBAAwB,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE;oBAC9E,2BAA2B,SAAS,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE;oBACpF,sBAAsB,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE;oBACtD,wBAAwB,SAAS,CAAC,MAAM,CAAC,eAAe,IAAI,SAAS,EAAE;iBACxE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEb,MAAM,yBAAgB,CAAC,SAAS,CAC9B,WAAW,EACX,qBAAqB,EACrB,kDAAkD,UAAU,EAAE,EAC9D,IAAI,CACL,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;oBACvB,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,aAAa,EACb,oCAAoC,EACpC,IAAI,CACL,CAAC;oBACF,OAAO;gBACT,CAAC;gBAED,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;oBAC/C,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;oBAC3B,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE;iBAC9B,CAAC,CAAC;gBAEH,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAE/E,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;oBAE9C,MAAM,yBAAgB,CAAC,YAAY,CACjC,WAAW,EACX,cAAc,EACd,uEAAuE,EACvE,IAAI,CACL,CAAC;oBAEF,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;wBACtD,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;wBAC3B,QAAQ,EAAE,SAAS;qBACpB,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,aAAa,EACb,kCAAmC,KAAe,CAAC,OAAO,EAAE,EAC5D,IAAI,CACL,CAAC;gBACJ,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;oBACvB,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,aAAa,EACb,oCAAoC,EACpC,IAAI,CACL,CAAC;oBACF,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAE/E,SAAS,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;oBAEjD,MAAM,yBAAgB,CAAC,YAAY,CACjC,WAAW,EACX,YAAY,EACZ,qEAAqE,EACrE,IAAI,CACL,CAAC;oBAEF,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;wBACpD,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;wBAC3B,QAAQ,EAAE,OAAO;qBAClB,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,aAAa,EACb,gCAAiC,KAAe,CAAC,OAAO,EAAE,EAC1D,IAAI,CACL,CAAC;gBACJ,CAAC;gBACD,OAAO;YACT,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,MAAM,IAAK,WAAW,CAAC,MAAc,CAAC;YACxD,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBACrD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,KAAK,EAAG,KAAe,CAAC,KAAK;gBAC7B,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,yBAAgB,CAAC,UAAU,CAC/B,WAAW,EACX,eAAe,EACf,gEAAgE,EAChE,IAAI,CACL,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC"}