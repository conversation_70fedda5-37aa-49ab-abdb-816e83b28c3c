"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const AccountRepository_1 = require("../../database/repositories/AccountRepository");
const embeds_1 = require("../../utils/embeds");
/**
 * Reset password button handler - allows users to reset their own password
 */
const resetPasswordButton = {
    customId: 'reset_password',
    async execute(interaction) {
        const client = interaction.client;
        const accountRepo = new AccountRepository_1.AccountRepository(client.db, client.logger);
        try {
            const existingAccount = await accountRepo.findByDiscordId(interaction.user.id);
            if (!existingAccount.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Database Error', 'There was an error checking your account. Please try again later.', true);
                return;
            }
            if (!existingAccount.data) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Account Not Found', '❌ You don\'t have a registered account yet. Please register first using the **Register** button.', true);
                return;
            }
            const account = existingAccount.data;
            const modal = new discord_js_1.ModalBuilder()
                .setCustomId('reset_password_modal')
                .setTitle('🔐 Reset Your Password');
            const currentPasswordInput = new discord_js_1.TextInputBuilder()
                .setCustomId('current_password')
                .setLabel('Current Password')
                .setPlaceholder('Enter your current password')
                .setStyle(discord_js_1.TextInputStyle.Short)
                .setRequired(true)
                .setMinLength(6)
                .setMaxLength(50);
            const newPasswordInput = new discord_js_1.TextInputBuilder()
                .setCustomId('new_password')
                .setLabel('New Password')
                .setPlaceholder('Enter your new password (min 6 characters)')
                .setStyle(discord_js_1.TextInputStyle.Short)
                .setRequired(true)
                .setMinLength(6)
                .setMaxLength(50);
            const confirmPasswordInput = new discord_js_1.TextInputBuilder()
                .setCustomId('confirm_password')
                .setLabel('Confirm New Password')
                .setPlaceholder('Confirm your new password')
                .setStyle(discord_js_1.TextInputStyle.Short)
                .setRequired(true)
                .setMinLength(6)
                .setMaxLength(50);
            const currentPasswordRow = new discord_js_1.ActionRowBuilder().addComponents(currentPasswordInput);
            const newPasswordRow = new discord_js_1.ActionRowBuilder().addComponents(newPasswordInput);
            const confirmPasswordRow = new discord_js_1.ActionRowBuilder().addComponents(confirmPasswordInput);
            modal.addComponents(currentPasswordRow, newPasswordRow, confirmPasswordRow);
            await interaction.showModal(modal);
            client.logger.info('Password reset modal shown', {
                userId: interaction.user.id,
                username: interaction.user.username,
                accountId: account.id,
                accountUsername: account.username
            });
        }
        catch (error) {
            client.logger.error('Error in reset password button', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
            await embeds_1.InteractionUtils.replyError(interaction, 'Button Error', 'An unexpected error occurred. Please try again later.', true);
        }
    },
};
exports.default = resetPasswordButton;
//# sourceMappingURL=resetPasswordButton.js.map