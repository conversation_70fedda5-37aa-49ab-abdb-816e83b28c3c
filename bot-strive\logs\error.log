{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1393091788788600914","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-10 21:49:28","userId":"826820357121638420"}
{"context":{"interactionId":"1393091788788600914","interactionType":5,"source":"interaction_handler","userId":"826820357121638420"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"Discord<PERSON>IError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-10 21:49:28"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-10 21:49:29"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1393502606009172029","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-12 01:01:55","userId":"282234884343463966"}
{"context":{"interactionId":"1393502606009172029","interactionType":5,"source":"interaction_handler","userId":"282234884343463966"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-12 01:01:55"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-12 01:01:56"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1394387358857695253","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-14 11:37:38","userId":"959324529962917900"}
{"context":{"interactionId":"1394387358857695253","interactionType":5,"source":"interaction_handler","userId":"959324529962917900"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-14 11:37:38"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-14 11:37:39"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1394421873822924802","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-14 13:54:46","userId":"822442329074958426"}
{"context":{"interactionId":"1394421873822924802","interactionType":5,"source":"interaction_handler","userId":"822442329074958426"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-14 13:54:46"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-14 13:54:46"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1394523545773670440","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-14 20:38:47","userId":"1315856720362209351"}
{"context":{"interactionId":"1394523545773670440","interactionType":5,"source":"interaction_handler","userId":"1315856720362209351"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-14 20:38:47"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-14 20:38:48"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1394567729318727751","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-14 23:34:21","userId":"360703220374568961"}
{"context":{"interactionId":"1394567729318727751","interactionType":5,"source":"interaction_handler","userId":"360703220374568961"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-14 23:34:21"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-14 23:34:21"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1394625538873757797","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-15 03:24:05","userId":"818398603864178728"}
{"context":{"interactionId":"1394625538873757797","interactionType":5,"source":"interaction_handler","userId":"818398603864178728"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-15 03:24:05"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-15 03:24:06"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1394699151190790284","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-15 08:16:34","userId":"1392135872161382503"}
{"context":{"interactionId":"1394699151190790284","interactionType":5,"source":"interaction_handler","userId":"1392135872161382503"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-15 08:16:34"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-15 08:16:34"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1394714191448899707","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-15 09:16:20","userId":"1219447450469929051"}
{"context":{"interactionId":"1394714191448899707","interactionType":5,"source":"interaction_handler","userId":"1219447450469929051"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-15 09:16:20"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-15 09:16:22"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1394744365523665006","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-15 11:16:16","userId":"1002921948675846155"}
{"context":{"interactionId":"1394744365523665006","interactionType":5,"source":"interaction_handler","userId":"1002921948675846155"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-15 11:16:16"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-15 11:16:17"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1395054752882102302","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-16 21:49:37","userId":"827787681143717899"}
{"context":{"interactionId":"1395054752882102302","interactionType":5,"source":"interaction_handler","userId":"827787681143717899"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-16 21:49:37"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-16 21:49:38"}
{"channelId":"1357671360435851416","error":"The fetched channel does not belong to this manager's guild.","guildId":"1143812400668233728","level":"error","message":"Error sending leave message","stack":"Error [GuildChannelUnowned]: The fetched channel does not belong to this manager's guild.\n    at GuildChannelManager.fetch (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\managers\\GuildChannelManager.js:401:50)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async sendLeaveMessage (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\guildMemberRemove.js:78:30)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\guildMemberRemove.js:39:13)","timestamp":"2025-07-17 16:42:59","userId":"826820357121638420"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1395748680358953080","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-18 19:47:00","userId":"704944275967705088"}
{"context":{"interactionId":"1395748680358953080","interactionType":5,"source":"interaction_handler","userId":"704944275967705088"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-18 19:47:00"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-18 19:47:01"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1395855710880927884","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-19 02:52:20","userId":"1318939528022593627"}
{"context":{"interactionId":"1395855710880927884","interactionType":5,"source":"interaction_handler","userId":"1318939528022593627"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-19 02:52:20"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-19 02:52:20"}
{"error":"Unknown interaction","guildId":"1352256146957664337","interactionId":"1396086590777987072","interactionType":5,"level":"error","message":"Error handling interaction","timestamp":"2025-07-19 18:09:45","userId":"1321804597198590063"}
{"context":{"interactionId":"1396086590777987072","interactionType":5,"source":"interaction_handler","userId":"1321804597198590063"},"errorCount":1,"level":"error","message":"Unhandled error occurred Unknown interaction","name":"DiscordAPIError[10062]","stack":"DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ModalSubmitInteraction.reply (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:200:22)\n    at async InteractionUtils.replyError (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\utils\\embeds.js:87:13)\n    at async Object.execute (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\interactions\\modals\\registerModal.js:156:13)\n    at async handleModalSubmission (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:146:5)\n    at async BotClient.<anonymous> (C:\\Users\\<USER>\\Documents\\StriveGM\\bot-strive\\dist\\events\\interactionCreate.js:24:17)","timestamp":"2025-07-19 18:09:45"}
{"error":"Unknown interaction","level":"error","message":"Failed to send error response to user","timestamp":"2025-07-19 18:09:46"}
