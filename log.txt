Starting open.mp server (1.4.0.2783) from commit 601de2e1c8da86ff7979821a82bce20568d547dc
Loading component Actors.dll
	Successfully loaded component Actors (1.4.0.2783) with UID c81ca021eae2ad5c
Loading component Checkpoints.dll
	Successfully loaded component Checkpoints (1.4.0.2783) with UID 44a937350d611dde
Loading component Classes.dll
	Successfully loaded component Classes (1.4.0.2783) with UID 8cfb3183976da208
Loading component Console.dll
	Successfully loaded component Console (1.4.0.2783) with UID bfa24e49d0c95ee4
Loading component CustomModels.dll
	Successfully loaded component CustomModels (1.4.0.2783) with UID 15e3cb1e7c77ffff
Loading component Databases.dll
	Successfully loaded component Databases (1.4.0.2783) with UID 80092e7eb5821a96
Loading component Dialogs.dll
	Successfully loaded component Dialogs (1.4.0.2783) with UID 44a111350d611dde
Loading component GangZones.dll
	Successfully loaded component GangZones (1.4.0.2783) with UID b3351d11ee8d8056
Loading component LegacyConfig.dll
	Successfully loaded component LegacyConfig (1.4.0.2783) with UID 24ef6216838f9ffc
Loading component LegacyNetwork.dll
	Successfully loaded component RakNetLegacyNetwork (1.4.0.2783) with UID ea9799fd79cf8442
Loading component Menus.dll
	Successfully loaded component Menus (1.4.0.2783) with UID 621e219eb97ee0b2
Loading component Objects.dll
	Successfully loaded component Objects (1.4.0.2783) with UID 59f8415f72da6160
Loading component Pawn.dll
	Successfully loaded component Pawn (1.4.0.2783) with UID 78906cd9f19c36a6
Loading component pawnraknet.dll
	Successfully loaded component Pawn.RakNet (0.1.6.0) with UID 4a8b15c16d23e42f
Loading component Pickups.dll
	Successfully loaded component Pickups (1.4.0.2783) with UID cf304faa363dd971
Loading component Recordings.dll
	Successfully loaded component Recordings (1.4.0.2783) with UID 871144d399f5f613
Loading component sampvoice.dll
	Successfully loaded component sampvoice open.mp port (0.0.0.1) with UID 6f7d8cbde58c9ce9
Loading component sscanf.dll
	Successfully loaded component sscanf (2.13.8.0) with UID a1e7c01e55caa1f2
Loading component TextDraws.dll
	Successfully loaded component TextDraws (1.4.0.2783) with UID 9b5dc2b1d15c992a
Loading component TextLabels.dll
	Successfully loaded component TextLabels (1.4.0.2783) with UID a0c57ea80a009742
Loading component Timers.dll
	Successfully loaded component Timers (1.4.0.2783) with UID 2ad8124c5ea257a3
Loading component Variables.dll
	Successfully loaded component Variables (1.4.0.2783) with UID 75e121848bc01fa2
Loading component Vehicles.dll
	Successfully loaded component Vehicles (1.4.0.2783) with UID 3f1f62ee9e22ab19
Loaded 23 component(s) from C:\Users\<USER>\Documents\StriveGM\components
Parsing unknown legacy option long_call_time
[2025-07-25T08:33:16+0700] [Info] 
[2025-07-25T08:33:16+0700] [Info]  ===============================
[2025-07-25T08:33:16+0700] [Info] 
[2025-07-25T08:33:16+0700] [Info]      sscanf component loaded.   
[2025-07-25T08:33:16+0700] [Info] 
[2025-07-25T08:33:16+0700] [Info]          Version: 2.13.8
[2025-07-25T08:33:16+0700] [Info] 
[2025-07-25T08:33:16+0700] [Info]    (c) 2022 Alex "Y_Less" Cole  
[2025-07-25T08:33:16+0700] [Info] 
[2025-07-25T08:33:16+0700] [Info]  ===============================
[2025-07-25T08:33:16+0700] [Info] 
[2025-07-25T08:33:16+0700] [Info] [Pawn.RakNet] plugin v1.6.0 loading...
[2025-07-25T08:33:16+0700] [Info] [Pawn.RakNet] 

    | Pawn.RakNet 1.6.0 | open.mp | 2016 - 2023
    |--------------------------------------------
    | Author and maintainer: katursis


    | Compiled: Feb 12 2023 at 16:51:14
    |--------------------------------------------------------------
    | Repository: https://github.com/katursis/Pawn.RakNet/tree/omp
    |--------------------------------------------------------------
    | Wiki: https://github.com/katursis/Pawn.RakNet/wiki

[2025-07-25T08:33:16+0700] [Info] [sv:dbg:network:init] : module initializing...
[2025-07-25T08:33:16+0700] [Info] [dbg:raknet:init] : module initializing...
[2025-07-25T08:33:16+0700] [Info] [dbg:raknet:init] : module initialized
[2025-07-25T08:33:16+0700] [Info] [sv:dbg:network:init] : module initialized
[2025-07-25T08:33:16+0700] [Info] [sv:dbg:main:Load] : creating 1 work threads...
[2025-07-25T08:33:16+0700] [Info]  -------------------------------------------    
[2025-07-25T08:33:16+0700] [Info]    ___                __   __    _              
[2025-07-25T08:33:16+0700] [Info]   / __| __ _ _ __  _ _\ \ / /__ (_) __ ___    
[2025-07-25T08:33:16+0700] [Info]   \__ \/ _` | '  \| '_ \   / _ \| |/ _/ -_)
[2025-07-25T08:33:16+0700] [Info]   |___/\__,_|_|_|_| .__/\_/\___/|_|\__\___|
[2025-07-25T08:33:16+0700] [Info]                   |_|                           
[2025-07-25T08:33:16+0700] [Info]  -------------------------------------------    
[2025-07-25T08:33:16+0700] [Info]                 SampVoice by MOR                
[2025-07-25T08:33:16+0700] [Info]     Ported to open.mp by AmyrAhmady (iAmir)     
[2025-07-25T08:33:16+0700] [Info]  -------------------------------------------    
[2025-07-25T08:33:16+0700] [Info] Loading plugin: crashdetect.dll
[2025-07-25T08:33:16+0700] [Info]   CrashDetect plugin 4.22-1-gcd608b6
[2025-07-25T08:33:16+0700] [Info] Loading plugin: streamer.dll
[2025-07-25T08:33:16+0700] [Info] 

*** Streamer Plugin v2.9.6 by Incognito loaded ***

[2025-07-25T08:33:16+0700] [Info] Loading plugin: mysql.dll
[2025-07-25T08:33:16+0700] [Info]  >> plugin.mysql: R41-4 successfully loaded.
[2025-07-25T08:33:16+0700] [Info] Loading plugin: samp_bcrypt.dll
[2025-07-25T08:33:16+0700] [Info] [SampBcrypt] [info]: Version: 0.4.1
[2025-07-25T08:33:16+0700] [Info] Loading plugin: textdraw-streamer.dll
[2025-07-25T08:33:16+0700] [Info] 
[2025-07-25T08:33:16+0700] [Info]  =================================
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |    textdraw-streamer v2.0.3   |
[2025-07-25T08:33:16+0700] [Info]  |            Loaded             |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |  Coding:                      |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |  Burak (Nexor)                |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |  Compiled:                    |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |  29.09.2023, 16:49:07         |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |  Github:                      |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |  github.com/nexquery          |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |  Discord:                     |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  |  benburakya - Nexor#4730      |
[2025-07-25T08:33:16+0700] [Info]  |                               |
[2025-07-25T08:33:16+0700] [Info]  =================================
[2025-07-25T08:33:16+0700] [Info] 
[2025-07-25T08:33:16+0700] [Info] Loading plugin: PawnPlus.dll
[2025-07-25T08:33:16+0700] [Info]  PawnPlus v1.5.1 loaded
[2025-07-25T08:33:16+0700] [Info]  Created by IS4
[2025-07-25T08:33:17+0700] [Info] [sv:dbg:network:bind] : voice server running on port 7778
[2025-07-25T08:33:17+0700] [Info]  
[2025-07-25T08:33:17+0700] [Info]  
[2025-07-25T08:33:17+0700] [Info]          ==============================================================
[2025-07-25T08:33:17+0700] [Info]          |                                                            |
[2025-07-25T08:33:17+0700] [Info]          |                                                            |
[2025-07-25T08:33:17+0700] [Info]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[2025-07-25T08:33:17+0700] [Info]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[2025-07-25T08:33:17+0700] [Info]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[2025-07-25T08:33:17+0700] [Info]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[2025-07-25T08:33:17+0700] [Info]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[2025-07-25T08:33:17+0700] [Info]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[2025-07-25T08:33:17+0700] [Info]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[2025-07-25T08:33:17+0700] [Info]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[2025-07-25T08:33:17+0700] [Info]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[2025-07-25T08:33:17+0700] [Info]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[2025-07-25T08:33:17+0700] [Info]          |           Y:::::Y                    S:::::S   I::::I      |
[2025-07-25T08:33:17+0700] [Info]          |           Y:::::Y                    S:::::S   I::::I      |
[2025-07-25T08:33:17+0700] [Info]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[2025-07-25T08:33:17+0700] [Info]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[2025-07-25T08:33:17+0700] [Info]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[2025-07-25T08:33:17+0700] [Info]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[2025-07-25T08:33:17+0700] [Info]          |                                                            |
[2025-07-25T08:33:17+0700] [Info]          |                                                            |
[2025-07-25T08:33:17+0700] [Info]          |                      (c) 2021 MPL v1.1                     |
[2025-07-25T08:33:17+0700] [Info]          |            Alex "Y_Less" Cole and contributors.            |
[2025-07-25T08:33:17+0700] [Info]          |                                                            |
[2025-07-25T08:33:17+0700] [Info]          |                                                            |
[2025-07-25T08:33:17+0700] [Info]          ==============================================================
[2025-07-25T08:33:17+0700] [Info]  
[2025-07-25T08:33:17+0700] [Info]  
[2025-07-25T08:33:17+0700] [Info]  ========================================== 
[2025-07-25T08:33:17+0700] [Info]  |                                        | 
[2025-07-25T08:33:17+0700] [Info]  |   Generating code, this may take a     | 
[2025-07-25T08:33:17+0700] [Info]  |  little bit of time.  Note that this   | 
[2025-07-25T08:33:17+0700] [Info]  |  code generation works best with the   | 
[2025-07-25T08:33:17+0700] [Info]  |     JIT plugin, which you are not      | 
[2025-07-25T08:33:17+0700] [Info]  |     currently using.  Get it here:     | 
[2025-07-25T08:33:17+0700] [Info]  |                                        | 
[2025-07-25T08:33:17+0700] [Info]  |       https://git.io/jit-plugin        | 
[2025-07-25T08:33:17+0700] [Info]  |                                        | 
[2025-07-25T08:33:17+0700] [Info]  |             Please wait...             | 
[2025-07-25T08:33:18+0700] [Info]  |                                        | 
[2025-07-25T08:33:18+0700] [Info]  |             Done in 900ms!             | 
[2025-07-25T08:33:18+0700] [Info]  |                                        | 
[2025-07-25T08:33:18+0700] [Info]  ========================================== 
[2025-07-25T08:33:18+0700] [Info] *** YSI Info: Script ID: 1
[2025-07-25T08:33:18+0700] [Info] *** YSI Error: Commands must be in lower-case in your source code.
[2025-07-25T08:33:18+0700] [Info]  |                                        | 
[2025-07-25T08:33:18+0700] [Info]  | Server:      open.mp 1.4.0.2783 (W)    | 
[2025-07-25T08:33:18+0700] [Info]  | Started:     25 Jul 2025 - 08:33:17    | 
[2025-07-25T08:33:18+0700] [Info]  |                                        | 
[2025-07-25T08:33:18+0700] [Info]  | Compiler:    3.10.11 (Windows)         | 
[2025-07-25T08:33:18+0700] [Info]  | Includes:    open.mp v037030           | 
[2025-07-25T08:33:18+0700] [Info]  | Codepage:    <none>                    | 
[2025-07-25T08:33:18+0700] [Info]  | Built:       22 Jul 2025 - 15:52:04    | 
[2025-07-25T08:33:18+0700] [Info]  |                                        | 
[2025-07-25T08:33:18+0700] [Info]  | YSI:         v05.10.0006               | 
[2025-07-25T08:33:18+0700] [Info]  | Master:      1                         | 
[2025-07-25T08:33:18+0700] [Info]  |                                        | 
[2025-07-25T08:33:18+0700] [Info]  | JIT:         <none>                    | 
[2025-07-25T08:33:18+0700] [Info]  | Crashdetect: <found>                   | 
[2025-07-25T08:33:18+0700] [Info]  |                                        | 
[2025-07-25T08:33:18+0700] [Info]  ========================================== 
[2025-07-25T08:33:18+0700] [Info]  
[2025-07-25T08:33:18+0700] [Info]  
[2025-07-25T08:33:18+0700] [Info] [SQL] Connection to database passed!
[2025-07-25T08:33:18+0700] [Info] [VOICE] Voice features initialized
[2025-07-25T08:33:18+0700] [Info] [Loaded] Driving Test DMV with Car ID: 1
[2025-07-25T08:33:18+0700] [Info] [Loaded] Anti-AFK System Loaded!
[2025-07-25T08:33:18+0700] [Info] [Loaded] Complete Event System loaded successfully!
[2025-07-25T08:33:18+0700] [Info] [FACTION] Faction vehicle system initialized
[2025-07-25T08:33:18+0700] [Info] [Loaded] Successfully load created 38/38 ore objects!
[2025-07-25T08:33:18+0700] [Info] [ADM] Info: Load EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[2025-07-25T08:33:18+0700] [Info] Legacy Network started on port 7777
[2025-07-25T08:33:18+0700] [Info] [Loaded] 1 Dropped Items
[2025-07-25T08:33:18+0700] [Info] [Loaded] 116 Houses
[2025-07-25T08:33:18+0700] [Info] [Loaded] 11 Businesses
[2025-07-25T08:33:18+0700] [Info] [Loaded] 5 Entrance Doors
[2025-07-25T08:33:18+0700] [Info] [Loaded] 4 Jobs
[2025-07-25T08:33:18+0700] [Info] [Loaded] 1 Factions
[2025-07-25T08:33:18+0700] [Info] [Loaded] 7 Actors
[2025-07-25T08:33:18+0700] [Info] [Loaded] 2 Insurance Lots
[2025-07-25T08:33:18+0700] [Info] [Loaded] 10 Gates
[2025-07-25T08:33:18+0700] [Info] [Loaded] Loaded 20 lumber trees.
[2025-07-25T08:33:18+0700] [Info] [Loaded] 1 Trees
[2025-07-25T08:33:18+0700] [Info] [Loaded] 1 VIP Dealerships
[2025-07-25T08:33:18+0700] [Info] [Loaded] 18 Garbage
[2025-07-25T08:33:18+0700] [Info] [Loaded] 16 Map Icons
[2025-07-25T08:33:18+0700] [Info] [FACTION] FactionVehicle_Load called with 1 rows
[2025-07-25T08:33:18+0700] [Info] [FACTION] Loading 1 faction vehicles from database...
[2025-07-25T08:33:18+0700] [Info] [FACTION] Loaded data: Array 0, DB 2, Model 596, Faction 1
[2025-07-25T08:33:18+0700] [Info] [FACTION] Data loading completed: 1 faction vehicles
[2025-07-25T08:33:18+0700] [Info] [FACTION] Successfully spawned: Array 0, Vehicle 13, Model 596, Faction 1
[2025-07-25T08:33:18+0700] [Info] [FACTION] Spawned: Array 0, Vehicle 13, Model 596, Faction 1
[2025-07-25T08:33:18+0700] [Info] [FACTION] Spawning completed: 1 faction vehicles spawned
[2025-07-25T08:33:18+0700] [Info] [Loaded] 70 Fish Stock
[2025-07-25T08:33:18+0700] [Info] [Loaded] 1200 Component Stock
[2025-07-25T08:33:18+0700] [Info] [Loaded] 12657 Plant Stock
[2025-07-25T08:33:18+0700] [Info] [Loaded] 10 Atm
[2025-07-25T09:53:02+0700] [Info] [connection] incoming connection: 114.5.218.200:33861 id: 0
[2025-07-25T09:53:02+0700] [Info] [join] Djaa has joined the server (0:114.5.218.200)
[2025-07-25T09:53:48+0700] [Info] [part] Daniel_Valhein has left the server (0:0)
[2025-07-25T09:56:01+0700] [Info] [connection] incoming connection: 114.5.218.200:33863 id: 0
[2025-07-25T09:56:01+0700] [Info] [join] Djaa has joined the server (0:114.5.218.200)
[2025-07-25T10:06:43+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T10:08:37+0700] [Info] [part] Daniel_Valhein has left the server (0:1)
[2025-07-25T11:13:02+0700] [Info] [connection] incoming connection: 114.5.218.200:33902 id: 0
[2025-07-25T11:13:02+0700] [Info] [join] Djaa has joined the server (0:114.5.218.200)
[2025-07-25T11:13:34+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T11:13:39+0700] [Info] [CMD]: Daniel_Valhein used command /en
[2025-07-25T11:14:25+0700] [Info] [CMD]: Daniel_Valhein used command /charge
[2025-07-25T11:14:27+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T11:14:27+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T11:14:41+0700] [Info] [CMD]: Daniel_Valhein used command /time
[2025-07-25T11:14:45+0700] [Info] [CMD]: Daniel_Valhein used command /quitjob
[2025-07-25T11:14:56+0700] [Info] [CMD]: Daniel_Valhein used command /refuel
[2025-07-25T11:15:51+0700] [Info] [CMD]: Daniel_Valhein used command /stopanim
[2025-07-25T11:16:07+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T11:16:08+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T11:16:11+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T11:16:56+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T11:16:56+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T11:18:26+0700] [Info] [CMD]: Daniel_Valhein used command /gps
[2025-07-25T11:18:50+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-25T11:18:50+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (6e428a6ef19bb348)
[2025-07-25T11:18:50+0700] [Info] [connection] incoming connection: **************:63742 id: 1
[2025-07-25T11:18:50+0700] [Info] [join] bili has joined the server (1:**************)
[2025-07-25T11:18:53+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:63743)
[2025-07-25T11:19:01+0700] [Info] [CMD]: Daniel_Valhein used command /scrapveh
[2025-07-25T11:19:02+0700] [Info] [CMD]: Daniel_Valhein used command /scrapveh
[2025-07-25T11:19:06+0700] [Info] [CMD]: Daniel_Valhein used command /scrapveh confirm
[2025-07-25T11:19:12+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T11:19:18+0700] [Info] [CMD]: Daniel_Valhein used command /stopanim
[2025-07-25T11:19:23+0700] [Info] [CMD]: Daniel_Valhein used command /stopanim
[2025-07-25T11:21:01+0700] [Info] [CMD]: Daniel_Valhein used command /collecttrash
[2025-07-25T11:21:04+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T11:21:22+0700] [Info] [CMD]: Daniel_Valhein used command /collecttrash
[2025-07-25T11:21:34+0700] [Info] [CMD]: Daniel_Valhein used command /collecttrash
[2025-07-25T11:21:37+0700] [Info] [CMD]: Daniel_Valhein used command /collecttrash
[2025-07-25T11:21:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-25T11:21:45+0700] [Info] [CMD]: Daniel_Valhein used command /droptrash
[2025-07-25T11:21:55+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-25T11:21:55+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (6e428a6efeba532d)
[2025-07-25T11:21:55+0700] [Info] [connection] incoming connection: **************:50630 id: 2
[2025-07-25T11:21:55+0700] [Info] [join] gibran has joined the server (2:**************)
[2025-07-25T11:22:00+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:51504)
[2025-07-25T11:22:20+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T11:22:41+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T11:23:59+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-25T11:23:59+0700] [Info] [part] Wiliam_Hurly has left the server (2:1)
[2025-07-25T11:24:02+0700] [Info] [CMD]: Luiz_Bearch used command /pell
[2025-07-25T11:24:24+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T11:24:38+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T11:25:00+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-25T11:25:01+0700] [Info] [part] Luiz_Bearch has left the server (1:1)
[2025-07-25T11:29:37+0700] [Info] [part] Daniel_Valhein has left the server (0:1)
[2025-07-25T11:42:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-25T12:03:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-25T12:24:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-25T12:28:47+0700] [Info] [connection] incoming connection: 114.10.146.21:51653 id: 0
[2025-07-25T12:28:47+0700] [Info] [join] Djaa has joined the server (0:114.10.146.21)
[2025-07-25T12:35:15+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T12:35:17+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T12:35:37+0700] [Info] [CMD]: Daniel_Valhein used command /items
[2025-07-25T12:35:56+0700] [Info] [CMD]: Daniel_Valhein used command /drink water
[2025-07-25T12:35:57+0700] [Info] [CMD]: Daniel_Valhein used command /items
[2025-07-25T12:36:00+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T12:36:01+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T12:36:27+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T12:36:27+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T12:36:44+0700] [Info] [CMD]: Daniel_Valhein used command /quitjob
[2025-07-25T12:38:55+0700] [Info] [CMD]: Daniel_Valhein used command /refuel
[2025-07-25T12:38:58+0700] [Info] [CMD]: Daniel_Valhein used command /refuel
[2025-07-25T12:39:51+0700] [Info] [CMD]: Daniel_Valhein used command /quitjob
[2025-07-25T12:40:36+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-25T12:40:36+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (6e428a6e6637a3d7)
[2025-07-25T12:40:36+0700] [Info] [connection] incoming connection: **************:65425 id: 1
[2025-07-25T12:40:36+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-25T12:40:41+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:50454)
[2025-07-25T12:40:59+0700] [Info] [chat] [Daniel_Valhein]: Wasu
[2025-07-25T12:41:01+0700] [Info] [CMD]: Daniel_Valhein used command /delayjob
[2025-07-25T12:41:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /o bang
[2025-07-25T12:41:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T12:41:29+0700] [Info] [CMD]: Daniel_Valhein used command /o Oit
[2025-07-25T12:41:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /o bagi laporan SAPD nak ap
[2025-07-25T12:41:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-25T12:41:55+0700] [Info] [CMD]: Daniel_Valhein used command /o Laporan?
[2025-07-25T12:42:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /o ya
[2025-07-25T12:42:08+0700] [Info] [CMD]: Daniel_Valhein used command /o Maksudnya gmna
[2025-07-25T12:42:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /o nanti mau Duty
[2025-07-25T12:42:21+0700] [Info] [CMD]: Daniel_Valhein used command /o Oh bantuin bikin ss rp?
[2025-07-25T12:42:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /o ini lo kalok mau Duty kan harus ada laporan dari Radio nya kan
[2025-07-25T12:43:19+0700] [Info] [CMD]: Daniel_Valhein used command /o Haah, gimana
[2025-07-25T12:43:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /o uda uda engga usa uda ingit
[2025-07-25T12:43:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /o uda uda engga usa uda ingat
[2025-07-25T12:44:02+0700] [Info] [CMD]: Daniel_Valhein used command /o Duty code? 10-8/10-7?
[2025-07-25T12:44:26+0700] [Info] [chat] [Daniel_Valhein]: Anj
[2025-07-25T12:44:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /r lapor Sergent | Duty for SAPD
[2025-07-25T12:44:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /r report Sergent | Duty for SAPD
[2025-07-25T12:45:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:45:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:45:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:45:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:45:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:45:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:45:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-25T12:45:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:45:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:46:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-25T12:46:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /animcmds
[2025-07-25T12:46:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-25T12:46:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /faq
[2025-07-25T12:46:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-25T12:46:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /editacc
[2025-07-25T12:46:58+0700] [Info] [CMD]: Daniel_Valhein used command /stopanim
[2025-07-25T12:47:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /hidegun
[2025-07-25T12:47:27+0700] [Info] [CMD]: Daniel_Valhein used command /restock
[2025-07-25T12:47:31+0700] [Info] [CMD]: Daniel_Valhein used command /quitjob
[2025-07-25T12:47:36+0700] [Info] [CMD]: Daniel_Valhein used command /stats
[2025-07-25T12:48:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /o jalan nya yang bener uda ada PD
[2025-07-25T12:48:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T12:48:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T12:48:18+0700] [Info] [CMD]: Daniel_Valhein used command /o awkkwk iya iya
[2025-07-25T12:48:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T12:48:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T12:48:49+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T12:48:50+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T12:48:52+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T12:49:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T12:49:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T12:49:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T12:49:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T12:49:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T12:50:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T12:50:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-25T12:51:22+0700] [Info] [CMD]: Daniel_Valhein used command /charger
[2025-07-25T12:51:24+0700] [Info] [CMD]: Daniel_Valhein used command /charge
[2025-07-25T12:52:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:52:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:52:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:52:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T12:53:07+0700] [Info] [CMD]: Daniel_Valhein used command /o Loh udahan kah bng duty nya?
[2025-07-25T12:53:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /O uda nanti habis sholat jum at lanjut lagi
[2025-07-25T12:53:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff
[2025-07-25T12:53:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:54:15+0700] [Info] [CMD]: Daniel_Valhein used command /startmine
[2025-07-25T12:54:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /o kamu di mana
[2025-07-25T12:54:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T12:54:24+0700] [Info] [CMD]: Daniel_Valhein used command /o Miner bng
[2025-07-25T12:54:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /o entar gw duty lagi
[2025-07-25T12:54:44+0700] [Info] [CMD]: Daniel_Valhein used command /o ok
[2025-07-25T12:54:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-25T12:54:58+0700] [Info] [CMD]: Daniel_Valhein used command /stopanim
[2025-07-25T12:55:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /fremove
[2025-07-25T12:55:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /finvite
[2025-07-25T12:55:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /m o
[2025-07-25T12:56:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:56:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:56:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:56:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-25T12:56:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:56:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:56:46+0700] [Info] [CMD]: Daniel_Valhein used command /piss
[2025-07-25T12:56:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:56:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:56:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:56:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:57:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-25T12:57:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /retockcargo
[2025-07-25T12:57:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:36+0700] [Info] [CMD]: Daniel_Valhein used command /drink water
[2025-07-25T12:57:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:40+0700] [Info] [CMD]: Daniel_Valhein used command /drink water
[2025-07-25T12:57:41+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-25T12:57:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-25T12:58:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /o uda ya abang mau sholat dulu
[2025-07-25T12:58:19+0700] [Info] [CMD]: Daniel_Valhein used command /o Oke cuy
[2025-07-25T12:58:40+0700] [Info] [CMD]: Daniel_Valhein used command /endmine
[2025-07-25T12:58:44+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T12:58:46+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:02:58+0700] [Info] [CMD]: Daniel_Valhein used command /paycheck
[2025-07-25T13:03:00+0700] [Info] [CMD]: Daniel_Valhein used command /signcheck
[2025-07-25T13:03:04+0700] [Info] [CMD]: Daniel_Valhein used command /paycheck
[2025-07-25T13:03:05+0700] [Info] [CMD]: Daniel_Valhein used command /atm
[2025-07-25T13:03:08+0700] [Info] [CMD]: Daniel_Valhein used command /faq
[2025-07-25T13:03:21+0700] [Info] [CMD]: Daniel_Valhein used command /quitjob
[2025-07-25T13:03:26+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:03:37+0700] [Info] [AFK] Player Kenzo_Wazoski (ID: 1) masuk mode AFK
[2025-07-25T13:04:30+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:05:56+0700] [Info] [CMD]: Daniel_Valhein used command /engine
[2025-07-25T13:05:57+0700] [Info] [CMD]: Daniel_Valhein used command /engine
[2025-07-25T13:06:02+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:06:05+0700] [Info] [CMD]: Daniel_Valhein used command /lo
[2025-07-25T13:06:06+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:06:12+0700] [Info] [CMD]: Daniel_Valhein used command /v trunk
[2025-07-25T13:06:19+0700] [Info] [CMD]: Daniel_Valhein used command /restockcargo
[2025-07-25T13:06:21+0700] [Info] [CMD]: Daniel_Valhein used command /restockcargo
[2025-07-25T13:07:15+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T13:07:15+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:07:15+0700] [Info] [CMD]: Daniel_Valhein used command /v trunk
[2025-07-25T13:08:38+0700] [Info] [CMD]: Daniel_Valhein used command /v trunk
[2025-07-25T13:08:58+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:09:04+0700] [Info] [CMD]: Daniel_Valhein used command /takejob
[2025-07-25T13:09:10+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:09:11+0700] [Info] [CMD]: Daniel_Valhein used command /v trunk
[2025-07-25T13:09:20+0700] [Info] [CMD]: Daniel_Valhein used command /findtree
[2025-07-25T13:09:25+0700] [Info] [CMD]: Daniel_Valhein used command /engine
[2025-07-25T13:13:02+0700] [Info] [CMD]: Daniel_Valhein used command /v trunk
[2025-07-25T13:14:43+0700] [Info] [CMD]: Daniel_Valhein used command /selllumber
[2025-07-25T13:15:16+0700] [Info] [CMD]: Daniel_Valhein used command /map
[2025-07-25T13:15:18+0700] [Info] [CMD]: Daniel_Valhein used command /maps
[2025-07-25T13:15:24+0700] [Info] [CMD]: Daniel_Valhein used command /gps
[2025-07-25T13:15:39+0700] [Info] [CMD]: Daniel_Valhein used command /gps
[2025-07-25T13:15:41+0700] [Info] [CMD]: Daniel_Valhein used command /gps
[2025-07-25T13:15:53+0700] [Info] [CMD]: Daniel_Valhein used command /help
[2025-07-25T13:15:55+0700] [Info] [CMD]: Daniel_Valhein used command /joblist
[2025-07-25T13:16:07+0700] [Info] [CMD]: Daniel_Valhein used command /joblist
[2025-07-25T13:16:10+0700] [Info] [CMD]: Daniel_Valhein used command /findanimal
[2025-07-25T13:16:57+0700] [Info] [CMD]: Daniel_Valhein used command /delayjob
[2025-07-25T13:17:04+0700] [Info] [CMD]: Daniel_Valhein used command /disablecp
[2025-07-25T13:17:16+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T13:17:16+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:17:24+0700] [Info] [part] Daniel_Valhein has left the server (0:1)
[2025-07-25T13:30:55+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*************) ...
[2025-07-25T13:30:55+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (fa2a0a72f5a3a228)
[2025-07-25T13:30:55+0700] [Info] [connection] incoming connection: *************:50505 id: 0
[2025-07-25T13:30:55+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-25T13:30:59+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:27695)
[2025-07-25T13:33:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /afk JL6HE8
[2025-07-25T13:33:03+0700] [Info] [AFK] Player Kenzo_Wazoski (ID: 1) keluar dari mode AFK
[2025-07-25T13:33:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /O BANG
[2025-07-25T13:33:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 0 hallo
[2025-07-25T13:33:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /o engga kerja abang
[2025-07-25T13:33:45+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-25T13:33:45+0700] [Info] [part] Kenzo_Wazoski has left the server (1:1)
[2025-07-25T13:33:58+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-25T13:33:58+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (6e428a6e27b21050)
[2025-07-25T13:33:58+0700] [Info] [connection] incoming connection: **************:58148 id: 1
[2025-07-25T13:33:58+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-25T13:34:02+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:59930)
[2025-07-25T13:34:17+0700] [Info] [connection] incoming connection: *************:8047 id: 2
[2025-07-25T13:34:17+0700] [Info] [join] Djaa has joined the server (2:*************)
[2025-07-25T13:34:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 0 bang ngebug bang
[2025-07-25T13:34:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 0 nyangkut di bawa kendaraan
[2025-07-25T13:35:28+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T13:35:28+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:35:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /desspawn
[2025-07-25T13:35:46+0700] [Info] [CMD]: Daniel_Valhein used command /refuel
[2025-07-25T13:35:49+0700] [Info] [chat] [Daniel_Valhein]: Jir
[2025-07-25T13:35:49+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T13:35:51+0700] [Info] [CMD]: Daniel_Valhein used command /v windows
[2025-07-25T13:36:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /o engga ada admin ya
[2025-07-25T13:36:08+0700] [Info] [CMD]: Daniel_Valhein used command /o Adaa
[2025-07-25T13:36:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /o siapa
[2025-07-25T13:36:28+0700] [Info] [CMD]: Daniel_Valhein used command /o Ituu Aaron
[2025-07-25T13:36:29+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T13:36:29+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:36:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /o ini nyangkut di dalam kendaraan
[2025-07-25T13:36:48+0700] [Info] [CMD]: Daniel_Valhein used command /o Truck y
[2025-07-25T13:36:49+0700] [Info] [CMD]: Daniel_Valhein used command /o Truck ya
[2025-07-25T13:36:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /o ya
[2025-07-25T13:37:01+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) masuk mode AFK
[2025-07-25T13:37:08+0700] [Info] [CMD]: Daniel_Valhein used command /o Coba ku bantu dorong, dmna emng
[2025-07-25T13:37:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /o lu tau tempat antar Crate Plant
[2025-07-25T13:37:38+0700] [Info] [CMD]: Daniel_Valhein used command /o Tau
[2025-07-25T13:37:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /o di situ
[2025-07-25T13:37:46+0700] [Info] [CMD]: Daniel_Valhein used command /o Oke
[2025-07-25T13:39:10+0700] [Info] [CMD]: Daniel_Valhein used command /b masih nyangkut ya
[2025-07-25T13:39:36+0700] [Info] [CMD]: Daniel_Valhein used command /o Mashi nyangkut kah
[2025-07-25T13:39:41+0700] [Info] [CMD]: Daniel_Valhein used command /drag
[2025-07-25T13:39:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /b iya
[2025-07-25T13:39:46+0700] [Info] [CMD]: Daniel_Valhein used command /drag Kenzo
[2025-07-25T13:40:00+0700] [Info] [chat] [Kenzo_Wazoski]: ok
[2025-07-25T13:40:01+0700] [Info] [CMD]: Daniel_Valhein used command /b Nah
[2025-07-25T13:40:01+0700] [Info] [chat] [Kenzo_Wazoski]: oook
[2025-07-25T13:40:05+0700] [Info] [chat] [Daniel_Valhein]: Oke lanjut cuy
[2025-07-25T13:40:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /b makasi bro
[2025-07-25T13:40:10+0700] [Info] [CMD]: Daniel_Valhein used command /b sama sama
[2025-07-25T13:40:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /mission
[2025-07-25T13:40:28+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:40:33+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:40:42+0700] [Info] [CMD]: Daniel_Valhein used command /use
[2025-07-25T13:40:48+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:40:48+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:40:49+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:40:49+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:40:52+0700] [Info] [CMD]: Aaron_Fang used command /afk qq3p6q
[2025-07-25T13:40:52+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) keluar dari mode AFK
[2025-07-25T13:41:04+0700] [Info] [CMD]: Aaron_Fang used command /o aku cuma lantau dikit dikit ya
[2025-07-25T13:41:12+0700] [Info] [CMD]: Daniel_Valhein used command /o Aman bng
[2025-07-25T13:41:17+0700] [Info] [CMD]: Aaron_Fang used command /o soalnya mash kerja
[2025-07-25T13:41:30+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-25T13:41:46+0700] [Info] [CMD]: Aaron_Fang used command /o udah?
[2025-07-25T13:41:52+0700] [Info] [CMD]: Daniel_Valhein used command /o Udah aman ming
[2025-07-25T13:41:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /b udaa
[2025-07-25T13:42:03+0700] [Info] [CMD]: Aaron_Fang used command /b oke kenzo
[2025-07-25T13:42:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /b aman aj bang kerja aja abang dulu
[2025-07-25T13:42:17+0700] [Info] [CMD]: Aaron_Fang used command /o aku afk ya
[2025-07-25T13:42:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /b iya
[2025-07-25T13:42:22+0700] [Info] [CMD]: Daniel_Valhein used command /o Shap min
[2025-07-25T13:42:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-25T13:42:58+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T13:42:58+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:42:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-25T13:43:23+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-25T13:43:23+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-25T13:43:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T13:43:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T13:44:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T13:44:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T13:44:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-25T13:44:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T13:44:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-25T13:45:03+0700] [Info] [chat] [Daniel_Valhein]: Anj
[2025-07-25T13:45:26+0700] [Info] [part] Daniel_Valhein has left the server (2:0)
[2025-07-25T13:45:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T13:45:35+0700] [Info] [connection] incoming connection: 114.5.218.251:37612 id: 2
[2025-07-25T13:45:35+0700] [Info] [join] Daniel_Valhein has joined the server (2:114.5.218.251)
[2025-07-25T13:45:54+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:46:00+0700] [Info] [CMD]: Daniel_Valhein used command /engine
[2025-07-25T13:46:01+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:46:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /o kerja bareng bareng yok
[2025-07-25T13:46:24+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:46:26+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:46:30+0700] [Info] [CMD]: Daniel_Valhein used command /o Bjir kerja bakti kah
[2025-07-25T13:46:32+0700] [Info] [CMD]: Daniel_Valhein used command /startmine
[2025-07-25T13:46:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /o engga barengan
[2025-07-25T13:47:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /o kamu job trucker
[2025-07-25T13:47:10+0700] [Info] [CMD]: Daniel_Valhein used command /o tak, aku lumber
[2025-07-25T13:47:24+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) masuk mode AFK
[2025-07-25T13:47:29+0700] [Info] [CMD]: Daniel_Valhein used command /o Kenapa ga duty pd aja
[2025-07-25T13:48:04+0700] [Info] [CMD]: Daniel_Valhein used command /drink water
[2025-07-25T13:48:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /o nanti aj
[2025-07-25T13:51:00+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-25T13:51:06+0700] [Info] [CMD]: Daniel_Valhein used command /endmine
[2025-07-25T13:51:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /o kamu faction apa 
[2025-07-25T13:51:32+0700] [Info] [CMD]: Daniel_Valhein used command /o Ga ada faction bro
[2025-07-25T13:51:42+0700] [Info] [CMD]: Daniel_Valhein used command /o Freelance
[2025-07-25T13:51:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /o daftar PD la bro biar duty bareng
[2025-07-25T13:52:27+0700] [Info] [CMD]: Daniel_Valhein used command /o Nanti ku coba
[2025-07-25T13:52:32+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-25T13:52:32+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) disconnect saat AFK - data telah di-restore
[2025-07-25T13:52:32+0700] [Info] [part] Aaron_Fang has left the server (0:0)
[2025-07-25T13:52:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /o ehh btw kenalin aku Officer Kenzo_Wazoski PANGGIL aja Kenzo
[2025-07-25T13:52:44+0700] [Info] [part] Daniel_Valhein has left the server (2:1)
[2025-07-25T13:54:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-25T13:58:22+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-25T13:58:22+0700] [Info] [part] Kenzo_Wazoski has left the server (1:0)
[2025-07-25T13:59:06+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-25T13:59:06+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (6e428a6e2041ef94)
[2025-07-25T13:59:06+0700] [Info] [connection] incoming connection: **************:59985 id: 0
[2025-07-25T13:59:06+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-25T13:59:11+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:52832)
[2025-07-25T13:59:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /delays
[2025-07-25T13:59:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-25T14:01:02+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-25T14:01:02+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (6e428a6e11062365)
[2025-07-25T14:01:02+0700] [Info] [connection] incoming connection: **************:56204 id: 1
[2025-07-25T14:01:02+0700] [Info] [join] FanID has joined the server (1:**************)
[2025-07-25T14:01:06+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:49356)
[2025-07-25T14:01:27+0700] [Info] [CMD]: Yuki_Haruto used command /goto bus
[2025-07-25T14:01:35+0700] [Info] [CMD]: Yuki_Haruto used command /bring 0
[2025-07-25T14:01:46+0700] [Info] [CMD]: Yuki_Haruto used command /oduty
[2025-07-25T14:01:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 1
[2025-07-25T14:01:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuff
[2025-07-25T14:01:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuff 1
[2025-07-25T14:02:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /b jangan
[2025-07-25T14:02:06+0700] [Info] [CMD]: Yuki_Haruto used command /ajail
[2025-07-25T14:02:24+0700] [Info] [CMD]: Yuki_Haruto used command /ajail 0 10 bandel
[2025-07-25T14:02:24+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has jailed %s for %d minutes, reason: %s." < 6456495
[2025-07-25T14:02:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T14:02:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /o kocak
[2025-07-25T14:02:39+0700] [Info] [CMD]: Yuki_Haruto used command /ahelp
[2025-07-25T14:02:58+0700] [Info] [CMD]: Yuki_Haruto used command /bring 0
[2025-07-25T14:03:08+0700] [Info] [chat] [Kenzo_Wazoski]: makasi
[2025-07-25T14:03:15+0700] [Info] [chat] [Kenzo_Wazoski]: teazer
[2025-07-25T14:03:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /b teazer
[2025-07-25T14:03:20+0700] [Info] [CMD]: Yuki_Haruto used command /ojail
[2025-07-25T14:03:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /teazer
[2025-07-25T14:03:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /teazer
[2025-07-25T14:03:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-25T14:03:40+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-25T14:03:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /b apa la
[2025-07-25T14:03:59+0700] [Info] [CMD]: Yuki_Haruto used command /kiss 2
[2025-07-25T14:04:02+0700] [Info] [CMD]: Yuki_Haruto used command /kiss 2
[2025-07-25T14:04:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-25T14:04:05+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-25T14:04:05+0700] [Info] [part] Yuki_Haruto has left the server (1:1)
[2025-07-25T14:04:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-25T14:04:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-25T14:04:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-25T14:05:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-25T14:14:13+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-25T14:14:13+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-25T19:23:37+0700] [Info] [connection] incoming connection: **************:18780 id: 0
[2025-07-25T19:23:37+0700] [Info] [join] Jenarrey has joined the server (0:**************)
[2025-07-25T19:24:02+0700] [Info] [CMD]: Albertus_Ragenmar used command /online
[2025-07-25T19:24:27+0700] [Info] [CMD]: Albertus_Ragenmar used command /r a
[2025-07-25T19:24:43+0700] [Info] [CMD]: Albertus_Ragenmar used command /stats
[2025-07-25T19:26:25+0700] [Info] [part] Albertus_Ragenmar has left the server (0:1)
[2025-07-25T19:31:31+0700] [Info] [connection] incoming connection: **************:21114 id: 0
[2025-07-25T19:31:31+0700] [Info] [join] Jenarrey has joined the server (0:**************)
[2025-07-25T19:32:37+0700] [Warning] Insufficient specifiers given to `format`: "Update Character {00FFEE}%s" < 2
[2025-07-25T19:32:43+0700] [Warning] Insufficient specifiers given to `format`: "Update Character {00FFEE}%s" < 2
[2025-07-25T19:32:50+0700] [Warning] Insufficient specifiers given to `format`: "Update Character {00FFEE}%s" < 2
[2025-07-25T19:32:59+0700] [Info] [CMD]: Derryl_Winston used command /claimrewards
[2025-07-25T19:33:01+0700] [Info] [CMD]: Derryl_Winston used command /claimreward
[2025-07-25T19:33:01+0700] [Info] [REWARD] Derryl_Winston (ID: 0) has claimed their character reward: $1,000.00 + Shancez vehicle (ID: 14).
[2025-07-25T19:33:09+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (*************) ...
[2025-07-25T19:33:09+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (1e23ba67d960ad26)
[2025-07-25T19:33:09+0700] [Info] [connection] incoming connection: *************:47253 id: 1
[2025-07-25T19:33:09+0700] [Info] [join] dflx has joined the server (1:*************)
[2025-07-25T19:33:13+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:42905)
[2025-07-25T19:33:49+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:33:55+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:33:56+0700] [Info] [CMD]: Aaron_Fang used command /o halo
[2025-07-25T19:34:01+0700] [Info] [CMD]: Derryl_Winston used command /o ini gue nil wkwkwk
[2025-07-25T19:34:08+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:34:14+0700] [Info] [CMD]: Aaron_Fang used command /o pajar?
[2025-07-25T19:34:16+0700] [Info] [CMD]: Derryl_Winston used command /o hooh
[2025-07-25T19:34:19+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:34:25+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:34:38+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:34:42+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:34:47+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:34:51+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:34:56+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:35:03+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:35:09+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:35:14+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:35:20+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:35:24+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:35:36+0700] [Info] [CMD]: Derryl_Winston used command /redeem
[2025-07-25T19:36:23+0700] [Info] [CMD]: Derryl_Winston used command /guide
[2025-07-25T19:36:26+0700] [Info] [CMD]: Derryl_Winston used command /tutorial
[2025-07-25T19:37:46+0700] [Info] [connection] incoming connection: **************:49791 id: 2
[2025-07-25T19:37:46+0700] [Info] [join] bili has joined the server (2:**************)
[2025-07-25T19:38:12+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-25T19:38:12+0700] [Info] [part] Aaron_Fang has left the server (1:0)
[2025-07-25T19:38:15+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-25T19:38:15+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (6e428a6ed62a33fb)
[2025-07-25T19:38:15+0700] [Info] [connection] incoming connection: **************:58764 id: 1
[2025-07-25T19:38:15+0700] [Info] [join] tata has joined the server (1:**************)
[2025-07-25T19:38:19+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:58765)
[2025-07-25T19:38:27+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T19:38:28+0700] [Info] [sv:dbg:network:connect] : connecting player (3) with address (*************) ...
[2025-07-25T19:38:28+0700] [Info] [sv:dbg:network:connect] : player (3) assigned key (1e23ba67992438d5)
[2025-07-25T19:38:28+0700] [Info] [connection] incoming connection: *************:55095 id: 3
[2025-07-25T19:38:28+0700] [Info] [join] dflx has joined the server (3:*************)
[2025-07-25T19:38:34+0700] [Info] [chat] [Tata_Wazoski]: lock
[2025-07-25T19:38:37+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:38:41+0700] [Info] [sv:dbg:network:receive] : player (3) identified (port:63523)
[2025-07-25T19:38:52+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-25T19:38:55+0700] [Info] [chat] [Luiz_Bearch]: `
[2025-07-25T19:38:55+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T19:38:58+0700] [Info] [sv:dbg:network:connect] : connecting player (4) with address (**************) ...
[2025-07-25T19:38:58+0700] [Info] [sv:dbg:network:connect] : player (4) assigned key (6e428a6eea3df278)
[2025-07-25T19:38:58+0700] [Info] [connection] incoming connection: **************:54838 id: 4
[2025-07-25T19:38:58+0700] [Info] [join] FanID has joined the server (4:**************)
[2025-07-25T19:39:02+0700] [Info] [sv:dbg:network:receive] : player (4) identified (port:54839)
[2025-07-25T19:39:03+0700] [Info] [CMD]: Tata_Wazoski used command /pm 3 min]
[2025-07-25T19:39:08+0700] [Info] [CMD]: Aaron_Fang used command /b ngapa
[2025-07-25T19:39:16+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-25T19:39:20+0700] [Info] [CMD]: Tata_Wazoski used command /pm 3 sadler ku masuk laut barusan spawn dari asuransi
[2025-07-25T19:39:35+0700] [Info] [CMD]: Tata_Wazoski used command /pm 3 tolong tp kan ke daratan min
[2025-07-25T19:39:35+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-25T19:39:37+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T19:39:45+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-25T19:39:46+0700] [Info] [sv:dbg:network:connect] : disconnecting player (3) ...
[2025-07-25T19:39:46+0700] [Info] [part] Aaron_Fang has left the server (3:0)
[2025-07-25T19:39:57+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-25T19:40:22+0700] [Info] [CMD]: Yuki_Haruto used command /goto insurance
[2025-07-25T19:40:27+0700] [Info] [sv:dbg:network:connect] : connecting player (3) with address (*************) ...
[2025-07-25T19:40:27+0700] [Info] [sv:dbg:network:connect] : player (3) assigned key (1e23ba6718a9d40c)
[2025-07-25T19:40:27+0700] [Info] [connection] incoming connection: *************:49271 id: 3
[2025-07-25T19:40:27+0700] [Info] [join] dflx has joined the server (3:*************)
[2025-07-25T19:40:32+0700] [Info] [sv:dbg:network:receive] : player (3) identified (port:60158)
[2025-07-25T19:40:44+0700] [Info] [CMD]: Aaron_Fang used command /pm 1 tunggu asuransi aja
[2025-07-25T19:40:56+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-25T19:41:07+0700] [Info] [CMD]: Aaron_Fang used command /refill 3
[2025-07-25T19:41:13+0700] [Info] [CMD]: Aaron_Fang used command /goto 2
[2025-07-25T19:41:20+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-25T19:41:25+0700] [Info] [CMD]: Aaron_Fang used command /pm 1 sini
[2025-07-25T19:41:27+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-25T19:41:30+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-25T19:41:33+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:41:35+0700] [Info] [chat] [Tata_Wazoski]: udah min g usah
[2025-07-25T19:41:37+0700] [Info] [chat] [Aaron_Fang]: Lu berdua
[2025-07-25T19:41:37+0700] [Info] [CMD]: Yuki_Haruto used command /b alow
[2025-07-25T19:41:41+0700] [Info] [chat] [Tata_Wazoski]: makasih
[2025-07-25T19:41:44+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-25T19:41:45+0700] [Info] [CMD]: Aaron_Fang used command /b sini
[2025-07-25T19:41:48+0700] [Info] [chat] [Yuki_Haruto]: kenapa tuh?
[2025-07-25T19:41:48+0700] [Info] [CMD]: Aaron_Fang used command /b sini
[2025-07-25T19:41:55+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:42:01+0700] [Info] [chat] [Aaron_Fang]: Lu mau gak jadi robber?
[2025-07-25T19:42:10+0700] [Info] [chat] [Yuki_Haruto]: jir
[2025-07-25T19:42:20+0700] [Info] [chat] [Aaron_Fang]: Ikut gua yok jadi robber
[2025-07-25T19:42:35+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:42:36+0700] [Info] [chat] [Yuki_Haruto]: emang mau ngerobber siapa?
[2025-07-25T19:42:45+0700] [Info] [chat] [Aaron_Fang]: Kita lawan polisi aja
[2025-07-25T19:42:53+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:42:53+0700] [Info] [CMD]: Aaron_Fang used command /sendto 1 3
[2025-07-25T19:42:56+0700] [Info] [CMD]: Aaron_Fang used command /b sini tata
[2025-07-25T19:42:59+0700] [Info] [chat] [Yuki_Haruto]: ga ada senjata aku 
[2025-07-25T19:43:04+0700] [Info] [chat] [Aaron_Fang]: Gua kasih
[2025-07-25T19:43:11+0700] [Info] [CMD]: Aaron_Fang used command /b rpan jadi robber ya
[2025-07-25T19:43:12+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash\
[2025-07-25T19:43:21+0700] [Info] [chat] [Aaron_Fang]: Shotgun sama deagle
[2025-07-25T19:43:28+0700] [Info] [CMD]: Yuki_Haruto used command /b kurang tau aku 
[2025-07-25T19:43:31+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash\
[2025-07-25T19:43:34+0700] [Info] [chat] [Tata_Wazoski]: shotgun sini min
[2025-07-25T19:43:36+0700] [Info] [CMD]: Aaron_Fang used command /givewep
[2025-07-25T19:43:36+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:43:38+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:43:41+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:43:49+0700] [Info] [CMD]: Tata_Wazoski used command /pay 3 36
[2025-07-25T19:43:49+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-25T19:43:50+0700] [Info] [CMD]: Aaron_Fang used command /givewep 1 24 200
[2025-07-25T19:43:57+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:43:57+0700] [Info] [CMD]: Aaron_Fang used command /givewep 1 4 200
[2025-07-25T19:44:07+0700] [Info] [CMD]: Tata_Wazoski used command /locjk
[2025-07-25T19:44:09+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:44:12+0700] [Info] [CMD]: Aaron_Fang used command /givewep 4 24 200
[2025-07-25T19:44:19+0700] [Info] [CMD]: Aaron_Fang used command /givewep 4 4 200
[2025-07-25T19:44:21+0700] [Info] [CMD]: Aaron_Fang used command /b sini dulu
[2025-07-25T19:44:22+0700] [Info] [chat] [Yuki_Haruto]: jir
[2025-07-25T19:44:26+0700] [Info] [CMD]: Aaron_Fang used command /b shotgunnya belom
[2025-07-25T19:44:26+0700] [Info] [CMD]: Luiz_Bearch used command /collecttrash
[2025-07-25T19:44:35+0700] [Info] [chat] [Tata_Wazoski]: kapan?
[2025-07-25T19:44:56+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-25T19:45:05+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T19:45:06+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:45:10+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:45:12+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:45:14+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T19:45:17+0700] [Info] [CMD]: Aaron_Fang used command /givewep 4 25 200
[2025-07-25T19:45:25+0700] [Info] [CMD]: Aaron_Fang used command /givewep 1 25 200
[2025-07-25T19:45:26+0700] [Info] [CMD]: Tata_Wazoski used command /v list
[2025-07-25T19:45:30+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T19:45:36+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 16
[2025-07-25T19:46:19+0700] [Info] [CMD]: Aaron_Fang used command /b coba lu lawan lawan pd nanti
[2025-07-25T19:46:20+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:46:30+0700] [Info] [CMD]: Aaron_Fang used command /pm 4 discord sini
[2025-07-25T19:46:45+0700] [Info] [CMD]: Derryl_Winston used command /open
[2025-07-25T19:46:47+0700] [Info] [CMD]: Yuki_Haruto used command /pm 3 lagi main di biling 
[2025-07-25T19:46:50+0700] [Info] [CMD]: Aaron_Fang used command /o ramaikan malam ini
[2025-07-25T19:46:54+0700] [Info] [CMD]: Yuki_Haruto used command /pm 3 ga pakai headset
[2025-07-25T19:46:54+0700] [Info] [chat] [Tata_Wazoski]: gue bagian ngelawannya aja
[2025-07-25T19:46:55+0700] [Info] [CMD]: Aaron_Fang used command /pm 4 oke
[2025-07-25T19:46:57+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-25T19:46:57+0700] [Info] [part] Tata_Wazoski has left the server (1:1)
[2025-07-25T19:47:31+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-25T19:47:31+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (6e428a6e4c1fdcd5)
[2025-07-25T19:47:31+0700] [Info] [connection] incoming connection: **************:52406 id: 1
[2025-07-25T19:47:31+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-25T19:47:36+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:52407)
[2025-07-25T19:47:55+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T19:48:02+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-25T19:48:06+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T19:48:09+0700] [Info] [part] Luiz_Bearch has left the server (2:1)
[2025-07-25T19:48:10+0700] [Info] [connection] incoming connection: ************:59234 id: 2
[2025-07-25T19:48:10+0700] [Info] [join] Kurangtau has joined the server (2:************)
[2025-07-25T19:48:14+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T19:48:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /b bagi senjata gw
[2025-07-25T19:48:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T19:48:27+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-25T19:48:38+0700] [Info] [sv:dbg:network:connect] : connecting player (5) with address (**************) ...
[2025-07-25T19:48:38+0700] [Info] [sv:dbg:network:connect] : player (5) assigned key (6e428a6ea3d0bec0)
[2025-07-25T19:48:38+0700] [Info] [connection] incoming connection: **************:51528 id: 5
[2025-07-25T19:48:38+0700] [Info] [join] tata has joined the server (5:**************)
[2025-07-25T19:48:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 4
[2025-07-25T19:48:43+0700] [Info] [sv:dbg:network:receive] : player (5) identified (port:59665)
[2025-07-25T19:48:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /fuff 3
[2025-07-25T19:48:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 3
[2025-07-25T19:48:53+0700] [Info] [CMD]: Alfred_Williams used command /id yuk
[2025-07-25T19:48:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuff
[2025-07-25T19:48:59+0700] [Info] [chat] [Alfred_Williams]: a
[2025-07-25T19:49:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuff 4
[2025-07-25T19:49:01+0700] [Info] [CMD]: Alfred_Williams used command /stats
[2025-07-25T19:49:04+0700] [Info] [CMD]: Alfred_Williams used command /stats
[2025-07-25T19:49:04+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-25T19:49:09+0700] [Info] [CMD]: Alfred_Williams used command /id alf
[2025-07-25T19:49:12+0700] [Info] [CMD]: Alfred_Williams used command /id alf\
[2025-07-25T19:49:15+0700] [Info] [CMD]: Alfred_Williams used command /id alf
[2025-07-25T19:49:18+0700] [Info] [part] Alfred_Williams has left the server (2:1)
[2025-07-25T19:49:29+0700] [Info] [connection] incoming connection: **************:53206 id: 2
[2025-07-25T19:49:29+0700] [Info] [join] Aldo has joined the server (2:**************)
[2025-07-25T19:49:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /handsup
[2025-07-25T19:49:43+0700] [Info] [CMD]: Mark_Olrando used command /open
[2025-07-25T19:49:44+0700] [Info] [CMD]: Mark_Olrando used command /open
[2025-07-25T19:49:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /teze
[2025-07-25T19:49:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /tezer
[2025-07-25T19:49:49+0700] [Info] [CMD]: Mark_Olrando used command /open
[2025-07-25T19:49:50+0700] [Info] [CMD]: Mark_Olrando used command /open
[2025-07-25T19:50:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /o asekk gw PD sendirian
[2025-07-25T19:50:35+0700] [Info] [CMD]: Mark_Olrando used command /piss
[2025-07-25T19:50:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-25T19:50:47+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-25T19:50:59+0700] [Info] [CMD]: Mark_Olrando used command /b aloo
[2025-07-25T19:51:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 2
[2025-07-25T19:51:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuff 2
[2025-07-25T19:51:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /b arron
[2025-07-25T19:51:51+0700] [Info] [AFK] Player Derryl_Winston (ID: 0) masuk mode AFK
[2025-07-25T19:51:52+0700] [Info] [CMD]: Yuki_Haruto used command /lay 3
[2025-07-25T19:51:53+0700] [Info] [CMD]: Mark_Olrando used command /b bg
[2025-07-25T19:51:54+0700] [Info] [CMD]: Yuki_Haruto used command /lay 3
[2025-07-25T19:52:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-25T19:52:04+0700] [Info] [AFK] Player Aaron_Fang (ID: 3) masuk mode AFK
[2025-07-25T19:52:19+0700] [Info] [CMD]: Yuki_Haruto used command /b /lay 3
[2025-07-25T19:52:23+0700] [Info] [CMD]: Tata_Wazoski used command /lay 3
[2025-07-25T19:52:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-25T19:52:42+0700] [Info] [CMD]: Mark_Olrando used command /lya
[2025-07-25T19:52:47+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-25T19:52:57+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:53:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /lay 3
[2025-07-25T19:53:18+0700] [Info] [CMD]: Tata_Wazoski used command /lay 3
[2025-07-25T19:53:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T19:54:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T19:54:11+0700] [Info] [connection] incoming connection: **************:62523 id: 6
[2025-07-25T19:54:11+0700] [Info] [join] bili has joined the server (6:**************)
[2025-07-25T19:54:11+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-25T19:54:15+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 2 50
[2025-07-25T19:54:20+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-25T19:54:21+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 2 100
[2025-07-25T19:54:24+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-25T19:54:48+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T19:54:56+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:55:06+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T19:55:12+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:55:29+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T19:55:36+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic
[2025-07-25T19:55:37+0700] [Info] [part] Luiz_Bearch has left the server (6:1)
[2025-07-25T19:55:41+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:55:42+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:55:50+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:55:51+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:56:27+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:56:37+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:56:38+0700] [Info] [connection] incoming connection: **************:51080 id: 6
[2025-07-25T19:56:38+0700] [Info] [join] bili has joined the server (6:**************)
[2025-07-25T19:56:43+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:56:47+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T19:56:47+0700] [Info] [kill] Tata_Wazoski killed Kenzo_Wazoski Desert Eagle
[2025-07-25T19:56:59+0700] [Info] [CMD]: Luiz_Bearch used command /lock
[2025-07-25T19:57:00+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T19:57:01+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-25T19:57:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /o wkwkwkw
[2025-07-25T19:57:05+0700] [Info] [CMD]: Tata_Wazoski used command //buy
[2025-07-25T19:57:06+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:57:07+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-25T19:57:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T19:57:11+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:57:15+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T19:57:16+0700] [Info] [CMD]: Luiz_Bearch used command /open
[2025-07-25T19:57:29+0700] [Info] [CMD]: Derryl_Winston used command /afk roh1os
[2025-07-25T19:57:29+0700] [Info] [AFK] Player Derryl_Winston (ID: 0) keluar dari mode AFK
[2025-07-25T19:57:29+0700] [Info] [CMD]: Mark_Olrando used command /b aloo
[2025-07-25T19:57:34+0700] [Info] [CMD]: Luiz_Bearch used command /handsub
[2025-07-25T19:57:38+0700] [Info] [CMD]: Luiz_Bearch used command /handsub
[2025-07-25T19:57:49+0700] [Info] [CMD]: Derryl_Winston used command /quiz
[2025-07-25T19:57:58+0700] [Info] [chat] [Kenzo_Wazoski]: Mark
[2025-07-25T19:58:03+0700] [Info] [chat] [Kenzo_Wazoski]: antar gw mark
[2025-07-25T19:58:24+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:58:53+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T19:59:22+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (reason %d)." < 6456495
[2025-07-25T19:59:26+0700] [Info] [CMD]: Luiz_Bearch used command /atm
[2025-07-25T19:59:27+0700] [Info] [part] Derryl_Winston has left the server (0:1)
[2025-07-25T19:59:38+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 5 100
[2025-07-25T19:59:40+0700] [Info] [CMD]: Luiz_Bearch used command /atm
[2025-07-25T19:59:53+0700] [Info] [connection] incoming connection: **************:23242 id: 0
[2025-07-25T19:59:53+0700] [Info] [join] Jenarrey has joined the server (0:**************)
[2025-07-25T20:00:18+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:00:34+0700] [Info] [CMD]: Luiz_Bearch used command /pay 5 750
[2025-07-25T20:00:34+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-25T20:00:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /o bro perang sama anak anak warnet
[2025-07-25T20:00:38+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-25T20:00:52+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:00:58+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-25T20:01:09+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:01:10+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:01:10+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:01:10+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:01:10+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:01:10+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:01:20+0700] [Info] [CMD]: Luiz_Bearch used command /buy
[2025-07-25T20:01:25+0700] [Info] [CMD]: Tata_Wazoski used command /my salary
[2025-07-25T20:01:25+0700] [Info] [CMD]: Luiz_Bearch used command /buy
[2025-07-25T20:01:29+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-25T20:01:30+0700] [Info] [CMD]: Luiz_Bearch used command /buy
[2025-07-25T20:01:53+0700] [Info] [CMD]: Luiz_Bearch used command /piss
[2025-07-25T20:02:10+0700] [Info] [CMD]: Albertus_Ragenmar used command /flocker
[2025-07-25T20:02:14+0700] [Info] [CMD]: Albertus_Ragenmar used command /flocker
[2025-07-25T20:02:16+0700] [Info] [CMD]: Tata_Wazoski used command /atm
[2025-07-25T20:02:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:02:26+0700] [Info] [kill] Mark_Olrando killed Luiz_Bearch Fist
[2025-07-25T20:02:26+0700] [Info] [CMD]: Albertus_Ragenmar used command /createarrest
[2025-07-25T20:02:26+0700] [Info] [death] Luiz_Bearch died 255
[2025-07-25T20:02:30+0700] [Info] [CMD]: Albertus_Ragenmar used command /destroyarrest
[2025-07-25T20:02:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:02:32+0700] [Info] [CMD]: Tata_Wazoski used command /deposit 3100
[2025-07-25T20:02:32+0700] [Info] [CMD]: Albertus_Ragenmar used command /destroyarrest 0
[2025-07-25T20:02:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:02:52+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 6 100
[2025-07-25T20:03:03+0700] [Info] [CMD]: Albertus_Ragenmar used command /flocker
[2025-07-25T20:03:20+0700] [Info] [CMD]: Mark_Olrando used command /b bang
[2025-07-25T20:03:36+0700] [Info] [chat] [Kenzo_Wazoski]: asekk
[2025-07-25T20:03:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /b makai baju hitam aja
[2025-07-25T20:04:06+0700] [Info] [CMD]: Yuki_Haruto used command /wave 1
[2025-07-25T20:04:09+0700] [Info] [CMD]: Yuki_Haruto used command /wave 2
[2025-07-25T20:04:11+0700] [Info] [CMD]: Yuki_Haruto used command /wave 2
[2025-07-25T20:04:13+0700] [Info] [CMD]: Yuki_Haruto used command /wave 3
[2025-07-25T20:04:15+0700] [Info] [CMD]: Yuki_Haruto used command /wave 3
[2025-07-25T20:04:17+0700] [Info] [CMD]: Yuki_Haruto used command /wave 4
[2025-07-25T20:04:20+0700] [Info] [CMD]: Yuki_Haruto used command /wave
[2025-07-25T20:04:34+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:04:39+0700] [Info] [CMD]: Albertus_Ragenmar used command /r Sergeant I Ragenmar reporting for on duty.
[2025-07-25T20:04:44+0700] [Info] [CMD]: Albertus_Ragenmar used command /online
[2025-07-25T20:04:57+0700] [Info] [CMD]: Yuki_Haruto used command /goto mehcanic
[2025-07-25T20:04:59+0700] [Info] [CMD]: Albertus_Ragenmar used command /b Officer Kenzo reporting for on duty
[2025-07-25T20:05:01+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic
[2025-07-25T20:05:07+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-25T20:05:12+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:05:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /r Officer Kenzo Reporting for on duty
[2025-07-25T20:05:30+0700] [Info] [CMD]: Mark_Olrando used command /open
[2025-07-25T20:05:30+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-25T20:05:34+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-25T20:05:37+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-25T20:05:40+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-25T20:05:42+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-25T20:05:45+0700] [Info] [CMD]: Mark_Olrando used command /open
[2025-07-25T20:05:47+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-25T20:05:53+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-25T20:05:59+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-25T20:06:00+0700] [Info] [CMD]: Yuki_Haruto used command /goto 6
[2025-07-25T20:06:03+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-25T20:06:08+0700] [Info] [CMD]: Albertus_Ragenmar used command /me grabs his GLOCK.18 from holster, flicks the safety [OFF], and aims the target.
[2025-07-25T20:06:10+0700] [Info] [CMD]: Albertus_Ragenmar used command /me puts his GLOCK.18 to holster and flicks the safety [ON].
[2025-07-25T20:06:23+0700] [Info] [CMD]: Albertus_Ragenmar used command /mdc
[2025-07-25T20:06:23+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-25T20:06:32+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:33+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:33+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:33+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:06:34+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:34+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:36+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:37+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:38+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:38+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:39+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:06:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-25T20:06:55+0700] [Info] [part] Mark_Olrando has left the server (2:0)
[2025-07-25T20:07:01+0700] [Info] [part] Luiz_Bearch has left the server (6:1)
[2025-07-25T20:07:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /me grabs his GLOCK.18 FROM Holster,Flicks the safety [OFF],AND aims the target
[2025-07-25T20:08:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /me grabs his GLOCK.18 FROM Holster,Flicks the safety [OFF],AND aims the target
[2025-07-25T20:08:26+0700] [Info] [CMD]: Albertus_Ragenmar used command /elm
[2025-07-25T20:08:28+0700] [Info] [CMD]: Albertus_Ragenmar used command /ems
[2025-07-25T20:08:31+0700] [Info] [CMD]: Albertus_Ragenmar used command /elms
[2025-07-25T20:08:32+0700] [Info] [CMD]: Albertus_Ragenmar used command /lms
[2025-07-25T20:08:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /me grabs his GLOCK.18 FROM Holster,Flicks the safety [OFF],AND aims the target
[2025-07-25T20:08:36+0700] [Info] [CMD]: Albertus_Ragenmar used command /lms
[2025-07-25T20:09:21+0700] [Info] [CMD]: Albertus_Ragenmar used command /callsign
[2025-07-25T20:09:29+0700] [Info] [chat] [Albertus_Ragenmar]: Kenzo
[2025-07-25T20:09:29+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:09:30+0700] [Info] [chat] [Albertus_Ragenmar]: Tunggu
[2025-07-25T20:09:30+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:09:34+0700] [Info] [chat] [Albertus_Ragenmar]: Turun bentar
[2025-07-25T20:09:34+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:09:34+0700] [Info] [chat] [Kenzo_Wazoski]: IYA ADA AP
[2025-07-25T20:09:34+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:09:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:09:38+0700] [Info] [chat] [Kenzo_Wazoski]: nak ap
[2025-07-25T20:09:38+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:09:41+0700] [Info] [chat] [Albertus_Ragenmar]: Sini saya bawa sebentar
[2025-07-25T20:09:51+0700] [Info] [CMD]: Albertus_Ragenmar used command /callsign 1-ADAM-1
[2025-07-25T20:09:59+0700] [Info] [CMD]: Albertus_Ragenmar used command /elm
[2025-07-25T20:10:00+0700] [Info] [CMD]: Albertus_Ragenmar used command /lms
[2025-07-25T20:10:04+0700] [Info] [CMD]: Albertus_Ragenmar used command /lms
[2025-07-25T20:10:06+0700] [Info] [CMD]: Albertus_Ragenmar used command /lms
[2025-07-25T20:10:13+0700] [Info] [chat] [Kenzo_Wazoski]: ini saya mau makan dulu>
[2025-07-25T20:10:13+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:10:15+0700] [Info] [chat] [Albertus_Ragenmar]: kita makan dulu
[2025-07-25T20:10:15+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:10:28+0700] [Info] [CMD]: Albertus_Ragenmar used command /buy
[2025-07-25T20:10:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T20:10:33+0700] [Info] [CMD]: Albertus_Ragenmar used command /buy
[2025-07-25T20:10:36+0700] [Info] [CMD]: Albertus_Ragenmar used command /buy]
[2025-07-25T20:10:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T20:10:37+0700] [Info] [CMD]: Albertus_Ragenmar used command /buy
[2025-07-25T20:10:39+0700] [Info] [CMD]: Albertus_Ragenmar used command /buy
[2025-07-25T20:10:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T20:10:44+0700] [Info] [CMD]: Albertus_Ragenmar used command /buy
[2025-07-25T20:10:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T20:10:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-25T20:10:52+0700] [Info] [CMD]: Yuki_Haruto used command /goto 3
[2025-07-25T20:10:58+0700] [Info] [CMD]: Albertus_Ragenmar used command /piss
[2025-07-25T20:11:03+0700] [Info] [FACTION] FanID ejected from faction vehicle 13 (Faction: 1)
[2025-07-25T20:11:08+0700] [Info] [FACTION] FanID ejected from faction vehicle 13 (Faction: 1)
[2025-07-25T20:11:26+0700] [Info] [chat] [Kenzo_Wazoski]: yuki>
[2025-07-25T20:11:42+0700] [Info] [chat] [Kenzo_Wazoski]: apa kamu ni
[2025-07-25T20:11:42+0700] [Info] [chat] [Albertus_Ragenmar]: Lets move
[2025-07-25T20:11:42+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:11:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 4
[2025-07-25T20:11:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuff 4
[2025-07-25T20:12:06+0700] [Info] [FACTION] FanID ejected from faction vehicle 13 (Faction: 1)
[2025-07-25T20:12:06+0700] [Info] [DEBUG] Continuous check: Ejecting FanID from faction vehicle 13
[2025-07-25T20:12:06+0700] [Info] [FACTION] FanID ejected from faction vehicle 13 (Faction: 1)
[2025-07-25T20:12:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:12:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:12:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:12:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:12:25+0700] [Info] [CMD]: Albertus_Ragenmar used command /r Officer Kenzo takes status 4 under 1-ADAM-1
[2025-07-25T20:13:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:13:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:13:28+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:28+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:29+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:29+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:30+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:32+0700] [Info] [CMD]: Albertus_Ragenmar used command /lms
[2025-07-25T20:13:32+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:33+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:33+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:34+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:38+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:38+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:39+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:40+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:41+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:41+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:42+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:42+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:43+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:43+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:44+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-25T20:13:44+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:46+0700] [Info] [CMD]: Albertus_Ragenmar used command /open
[2025-07-25T20:13:50+0700] [Info] [chat] [Kenzo_Wazoski]: ada nya
[2025-07-25T20:13:51+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:14:36+0700] [Info] [chat] [Albertus_Ragenmar]: 1-ADAM-1
[2025-07-25T20:14:36+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:14:38+0700] [Info] [chat] [Albertus_Ragenmar]: 1-ADAM-2
[2025-07-25T20:14:38+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:14:39+0700] [Info] [chat] [Albertus_Ragenmar]: 1-ADAM-3
[2025-07-25T20:14:39+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:15:35+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:15:43+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-25T20:15:43+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (e7a31c684dccf1cd)
[2025-07-25T20:15:43+0700] [Info] [connection] incoming connection: **************:30816 id: 2
[2025-07-25T20:15:43+0700] [Info] [join] Weziza has joined the server (2:**************)
[2025-07-25T20:15:48+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:31536)
[2025-07-25T20:16:07+0700] [Info] [CMD]: Blake_Foster used command /goto
[2025-07-25T20:16:17+0700] [Info] [CMD]: Blake_Foster used command /goto cityhall
[2025-07-25T20:16:21+0700] [Info] [CMD]: Blake_Foster used command /sethbe
[2025-07-25T20:16:25+0700] [Info] [CMD]: Tata_Wazoski used command //piss
[2025-07-25T20:16:54+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T20:17:04+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T20:17:08+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T20:17:11+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T20:17:15+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T20:17:54+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-25T20:17:54+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:18:24+0700] [Info] [CMD]: Blake_Foster used command /id blake
[2025-07-25T20:19:00+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-25T20:19:00+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:19:00+0700] [Info] [CMD]: Blake_Foster used command /lock
[2025-07-25T20:19:12+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 3
[2025-07-25T20:19:14+0700] [Info] [CMD]: Blake_Foster used command /afix
[2025-07-25T20:19:15+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-25T20:19:17+0700] [Info] [CMD]: Aaron_Fang used command /uncuffn 
[2025-07-25T20:19:19+0700] [Info] [CMD]: Aaron_Fang used command /uncuffn
[2025-07-25T20:19:20+0700] [Info] [CMD]: Aaron_Fang used command /uncuff
[2025-07-25T20:19:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:19:23+0700] [Info] [CMD]: Aaron_Fang used command /uncuff 3
[2025-07-25T20:19:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:19:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:19:26+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-25T20:19:30+0700] [Info] [CMD]: Aaron_Fang used command /aod
[2025-07-25T20:19:32+0700] [Info] [CMD]: Aaron_Fang used command /aduty
[2025-07-25T20:19:33+0700] [Info] [chat] [Kenzo_Wazoski]: kenak cok
[2025-07-25T20:19:33+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:19:34+0700] [Info] [CMD]: Blake_Foster used command /auncuff
[2025-07-25T20:19:38+0700] [Info] [CMD]: Blake_Foster used command /acuff
[2025-07-25T20:19:41+0700] [Info] [CMD]: Blake_Foster used command /goto dlfx
[2025-07-25T20:19:43+0700] [Info] [CMD]: dflx used command /goto 1
[2025-07-25T20:19:45+0700] [Info] [CMD]: dflx used command /goto 1
[2025-07-25T20:19:45+0700] [Info] [CMD]: Blake_Foster used command /goto 3
[2025-07-25T20:19:52+0700] [Info] [CMD]: Albertus_Ragenmar used command /uncuff 3
[2025-07-25T20:19:53+0700] [Info] [CMD]: dflx used command /healall
[2025-07-25T20:19:53+0700] [Info] [chat] [Kenzo_Wazoski]: lahhh siapa cok
[2025-07-25T20:19:53+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:19:58+0700] [Info] [CMD]: Albertus_Ragenmar used command /admins
[2025-07-25T20:20:01+0700] [Info] [CMD]: Albertus_Ragenmar used command /online
[2025-07-25T20:20:08+0700] [Info] [chat] [Blake_Foster]: Hai
[2025-07-25T20:20:08+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:20:11+0700] [Info] [CMD]: dflx used command /createevent
[2025-07-25T20:20:11+0700] [Info] [chat] [Kenzo_Wazoski]: wkwkw rame cok
[2025-07-25T20:20:11+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:20:14+0700] [Info] [CMD]: dflx used command /editevent
[2025-07-25T20:20:21+0700] [Info] [CMD]: Albertus_Ragenmar used command /b wkwkwk
[2025-07-25T20:20:21+0700] [Info] [CMD]: Blake_Foster used command /me merasakan gatal pada biji pelernya
[2025-07-25T20:20:34+0700] [Info] [CMD]: dflx used command /editevent weapon 24
[2025-07-25T20:20:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-25T20:20:43+0700] [Info] [CMD]: dflx used command /editevent weapon
[2025-07-25T20:20:58+0700] [Info] [CMD]: dflx used command /editevent mode
[2025-07-25T20:21:03+0700] [Info] [CMD]: dflx used command /editevent mode weapon
[2025-07-25T20:21:07+0700] [Info] [CMD]: dflx used command /editevent mode dm
[2025-07-25T20:21:10+0700] [Info] [CMD]: Blake_Foster used command /health
[2025-07-25T20:21:12+0700] [Info] [CMD]: Blake_Foster used command /help
[2025-07-25T20:21:14+0700] [Info] [CMD]: dflx used command /editevent
[2025-07-25T20:21:15+0700] [Info] [chat] [Kenzo_Wazoski]: stop
[2025-07-25T20:21:18+0700] [Info] [CMD]: dflx used command /editevent weapon
[2025-07-25T20:21:20+0700] [Info] [chat] [Kenzo_Wazoski]: tiarap
[2025-07-25T20:21:21+0700] [Info] [CMD]: dflx used command /editevent weapon 24
[2025-07-25T20:21:22+0700] [Info] [CMD]: Blake_Foster used command /help
[2025-07-25T20:21:29+0700] [Info] [CMD]: Blake_Foster used command /help
[2025-07-25T20:21:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /me grabs his GLOCK.18 FROM Holster,Flicks the safety [OFF],AND aims the target
[2025-07-25T20:21:32+0700] [Info] [CMD]: dflx used command /handsup
[2025-07-25T20:21:42+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:21:44+0700] [Info] [CMD]: dflx used command /handsup
[2025-07-25T20:21:54+0700] [Info] [chat] [Kenzo_Wazoski]: tiarap
[2025-07-25T20:21:56+0700] [Info] [CMD]: dflx used command /lay 3
[2025-07-25T20:21:58+0700] [Info] [CMD]: dflx used command /lay 2
[2025-07-25T20:21:59+0700] [Info] [CMD]: dflx used command /lay 1
[2025-07-25T20:22:00+0700] [Info] [CMD]: dflx used command /lay 4
[2025-07-25T20:22:02+0700] [Info] [CMD]: dflx used command /lay 5
[2025-07-25T20:22:04+0700] [Info] [CMD]: dflx used command /lay 6
[2025-07-25T20:22:12+0700] [Info] [CMD]: Yuki_Haruto used command /goto bus
[2025-07-25T20:22:13+0700] [Info] [chat] [Kenzo_Wazoski]: madap arah ke dinding
[2025-07-25T20:22:13+0700] [Info] [CMD]: dflx used command /handsup
[2025-07-25T20:22:20+0700] [Info] [CMD]: Yuki_Haruto used command /v list
[2025-07-25T20:22:24+0700] [Info] [CMD]: dflx used command /edit event time 5
[2025-07-25T20:22:28+0700] [Info] [CMD]: dflx used command /editevent time 5
[2025-07-25T20:22:28+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-25T20:22:29+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 21
[2025-07-25T20:22:32+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:22:43+0700] [Info] [kill] Yuki_Haruto killed Tata_Wazoski Vehicle
[2025-07-25T20:22:43+0700] [Info] [death] Tata_Wazoski died 255
[2025-07-25T20:22:44+0700] [Info] [CMD]: dflx used command /editevent teamkill 30
[2025-07-25T20:22:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /me mengambil borgol dari dalam U.B dan memasangkan di kedua tangan dflx
[2025-07-25T20:22:55+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 5 100
[2025-07-25T20:22:56+0700] [Info] [CMD]: dflx used command /editevent teamkill 
[2025-07-25T20:22:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /do ada perlawanan
[2025-07-25T20:23:01+0700] [Info] [CMD]: dflx used command /editevent teamkill  1
[2025-07-25T20:23:03+0700] [Info] [sv:dbg:network:connect] : disconnecting player (5) ...
[2025-07-25T20:23:03+0700] [Info] [part] Tata_Wazoski has left the server (5:1)
[2025-07-25T20:23:06+0700] [Info] [CMD]: dflx used command /editevent teamkill  0
[2025-07-25T20:23:08+0700] [Info] [CMD]: dflx used command /editevent teamkill  1
[2025-07-25T20:23:21+0700] [Info] [CMD]: dflx used command /editevent winnermoney 200
[2025-07-25T20:23:22+0700] [Info] [sv:dbg:network:connect] : connecting player (5) with address (**************) ...
[2025-07-25T20:23:22+0700] [Info] [sv:dbg:network:connect] : player (5) assigned key (6e428a6e28434a4b)
[2025-07-25T20:23:22+0700] [Info] [connection] incoming connection: **************:62330 id: 5
[2025-07-25T20:23:22+0700] [Info] [join] tata has joined the server (5:**************)
[2025-07-25T20:23:25+0700] [Info] [CMD]: dflx used command /editevent winnermoney 20000
[2025-07-25T20:23:28+0700] [Info] [sv:dbg:network:receive] : player (5) identified (port:62331)
[2025-07-25T20:23:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 3
[2025-07-25T20:23:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 3
[2025-07-25T20:23:40+0700] [Info] [CMD]: dflx used command /createevent
[2025-07-25T20:23:44+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 5 100
[2025-07-25T20:23:50+0700] [Info] [CMD]: dflx used command /event
[2025-07-25T20:23:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuff 3
[2025-07-25T20:23:54+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-25T20:23:54+0700] [Info] [part] Blake_Foster has left the server (2:1)
[2025-07-25T20:23:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /me grabs his GLOCK.18 FROM Holster,Flicks the safety [OFF],AND aims the target
[2025-07-25T20:23:57+0700] [Info] [sv:dbg:network:connect] : disconnecting player (4) ...
[2025-07-25T20:23:57+0700] [Info] [part] Yuki_Haruto has left the server (4:1)
[2025-07-25T20:24:11+0700] [Info] [CMD]: dflx used command /refill 3
[2025-07-25T20:24:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /me Safe gun
[2025-07-25T20:24:18+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-25T20:24:18+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (e7a31c689ef6c1d8)
[2025-07-25T20:24:18+0700] [Info] [connection] incoming connection: **************:29754 id: 2
[2025-07-25T20:24:18+0700] [Info] [join] Weziza has joined the server (2:**************)
[2025-07-25T20:24:23+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:30870)
[2025-07-25T20:24:27+0700] [Info] [CMD]: Albertus_Ragenmar used command /setsalary
[2025-07-25T20:24:29+0700] [Info] [sv:dbg:network:connect] : connecting player (4) with address (**************) ...
[2025-07-25T20:24:29+0700] [Info] [sv:dbg:network:connect] : player (4) assigned key (6e428a6e5ce1d7ec)
[2025-07-25T20:24:29+0700] [Info] [connection] incoming connection: **************:62082 id: 4
[2025-07-25T20:24:29+0700] [Info] [join] FanID has joined the server (4:**************)
[2025-07-25T20:24:29+0700] [Info] [CMD]: Albertus_Ragenmar used command /fsalary
[2025-07-25T20:24:31+0700] [Info] [CMD]: Albertus_Ragenmar used command /editsalary
[2025-07-25T20:24:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /me grabs his GLOCK.18 FROM Holster,Flicks the safety [OFF],AND aims the target
[2025-07-25T20:24:34+0700] [Info] [CMD]: Albertus_Ragenmar used command /editfaction
[2025-07-25T20:24:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 2
[2025-07-25T20:24:48+0700] [Info] [sv:dbg:network:connect] : disconnecting player (4) ...
[2025-07-25T20:24:48+0700] [Info] [part] FanID has left the server (4:0)
[2025-07-25T20:24:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 2
[2025-07-25T20:25:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /me mengambil borgol dari dalam U.B dan memasangkan di kedua tangan dflx
[2025-07-25T20:25:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /me mengambil borgol dari dalam U.B dan memasangkan di kedua tangan Blake 
[2025-07-25T20:25:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /do ada perlawanan
[2025-07-25T20:25:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 2
[2025-07-25T20:26:04+0700] [Info] [CMD]: Blake_Foster used command /tazer
[2025-07-25T20:26:05+0700] [Info] [CMD]: Blake_Foster used command /tazer
[2025-07-25T20:26:06+0700] [Info] [CMD]: Blake_Foster used command /me takes out his Kimber Custom II from his holster, as he flicks the safety pin to [OFF].
[2025-07-25T20:26:09+0700] [Info] [CMD]: Blake_Foster used command /me Take out his P90 from gunrack, switch the safety from SAFE to AUTO.
[2025-07-25T20:26:14+0700] [Info] [CMD]: Albertus_Ragenmar used command /me grabs his GLOCK.18 from holster, flicks the safety [OFF], and aims the target.
[2025-07-25T20:26:15+0700] [Info] [CMD]: Albertus_Ragenmar used command /me puts his GLOCK.18 to holster and flicks the safety [ON].
[2025-07-25T20:26:25+0700] [Info] [chat] [Kenzo_Wazoski]: tazer
[2025-07-25T20:26:29+0700] [Info] [CMD]: Blake_Foster used command /inv
[2025-07-25T20:26:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-25T20:26:31+0700] [Info] [CMD]: Blake_Foster used command /items
[2025-07-25T20:26:46+0700] [Info] [CMD]: Blake_Foster used command /reedem
[2025-07-25T20:26:48+0700] [Info] [CMD]: Blake_Foster used command /redeem
[2025-07-25T20:26:53+0700] [Info] [CMD]: dflx used command /refill 2
[2025-07-25T20:26:54+0700] [Info] [CMD]: Blake_Foster used command /redeem
[2025-07-25T20:27:00+0700] [Info] [sv:dbg:network:connect] : connecting player (4) with address (**************) ...
[2025-07-25T20:27:00+0700] [Info] [sv:dbg:network:connect] : player (4) assigned key (6e428a6e114108a7)
[2025-07-25T20:27:00+0700] [Info] [connection] incoming connection: **************:55261 id: 4
[2025-07-25T20:27:00+0700] [Info] [join] FanID has joined the server (4:**************)
[2025-07-25T20:27:04+0700] [Info] [sv:dbg:network:receive] : player (4) identified (port:53502)
[2025-07-25T20:27:23+0700] [Info] [CMD]: Tata_Wazoski used command /drink water
[2025-07-25T20:27:37+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-25T20:27:37+0700] [Info] [CMD]: Blake_Foster used command /redeem
[2025-07-25T20:27:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-25T20:27:43+0700] [Info] [CMD]: Blake_Foster used command /redeem
[2025-07-25T20:27:50+0700] [Info] [CMD]: Yuki_Haruto used command /bring 5
[2025-07-25T20:27:59+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:28:02+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:28:03+0700] [Info] [CMD]: Blake_Foster used command /redeem
[2025-07-25T20:28:25+0700] [Info] [CMD]: dflx used command /createevent
[2025-07-25T20:28:28+0700] [Info] [CMD]: dflx used command /startevent
[2025-07-25T20:28:32+0700] [Info] [CMD]: dflx used command /joinevent
[2025-07-25T20:28:34+0700] [Info] [CMD]: Albertus_Ragenmar used command /joinevent\
[2025-07-25T20:28:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /joinevent
[2025-07-25T20:28:36+0700] [Info] [CMD]: Albertus_Ragenmar used command /joinevent
[2025-07-25T20:28:40+0700] [Info] [CMD]: Blake_Foster used command /joinevent
[2025-07-25T20:28:59+0700] [Info] [CMD]: Blake_Foster used command /o woi join event
[2025-07-25T20:29:09+0700] [Info] [CMD]: dflx used command /aduty
[2025-07-25T20:29:10+0700] [Info] [CMD]: Tata_Wazoski used command /joinevent
[2025-07-25T20:29:13+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:29:14+0700] [Info] [CMD]: Aaron_Fang used command /heal 3
[2025-07-25T20:29:18+0700] [Info] [CMD]: Aaron_Fang used command /sethp 3 100
[2025-07-25T20:29:23+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:29:37+0700] [Info] [kill] Aaron_Fang killed Kenzo_Wazoski Desert Eagle
[2025-07-25T20:29:52+0700] [Info] [kill] Aaron_Fang killed Blake_Foster Desert Eagle
[2025-07-25T20:30:18+0700] [Info] [CMD]: Aaron_Fang used command /sethp 3 100
[2025-07-25T20:30:26+0700] [Info] [kill] Kenzo_Wazoski killed Albertus_Ragenmar Desert Eagle
[2025-07-25T20:30:26+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:30:26+0700] [Info] [CMD]: Aaron_Fang used command /aduty
[2025-07-25T20:30:35+0700] [Info] [kill] dflx killed Blake_Foster Desert Eagle
[2025-07-25T20:30:35+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:30:41+0700] [Info] [kill] Albertus_Ragenmar killed Kenzo_Wazoski Desert Eagle
[2025-07-25T20:30:41+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:30:42+0700] [Info] [CMD]: Blake_Foster used command /damagelogs
[2025-07-25T20:30:43+0700] [Info] [kill] Albertus_Ragenmar killed dflx Desert Eagle
[2025-07-25T20:30:43+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:30:43+0700] [Info] [CMD]: Blake_Foster used command /damagelog
[2025-07-25T20:30:46+0700] [Info] [CMD]: Blake_Foster used command /damages
[2025-07-25T20:31:16+0700] [Info] [chat] [Kenzo_Wazoski]: `
[2025-07-25T20:31:19+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:31:19+0700] [Info] [kill] Blake_Foster killed dflx Desert Eagle
[2025-07-25T20:31:27+0700] [Info] [CMD]: dflx used command /unfreeze 1
[2025-07-25T20:31:34+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:31:44+0700] [Info] [kill] Kenzo_Wazoski killed Albertus_Ragenmar Desert Eagle
[2025-07-25T20:31:44+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:31:57+0700] [Info] [CMD]: dflx used command /setarmour 3 100w
[2025-07-25T20:32:01+0700] [Info] [CMD]: dflx used command /setarmour 3 100
[2025-07-25T20:32:04+0700] [Info] [kill] Albertus_Ragenmar killed Blake_Foster Desert Eagle
[2025-07-25T20:32:04+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:32:09+0700] [Info] [kill] Albertus_Ragenmar killed Kenzo_Wazoski Desert Eagle
[2025-07-25T20:32:09+0700] [Info] [connection] incoming connection: **************:63934 id: 6
[2025-07-25T20:32:09+0700] [Info] [join] Aldo has joined the server (6:**************)
[2025-07-25T20:32:16+0700] [Info] [CMD]: Tata_Wazoski used command /startmine
[2025-07-25T20:32:25+0700] [Info] [CMD]: Yuki_Haruto used command /startmine
[2025-07-25T20:32:36+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:32:46+0700] [Info] [kill] Blake_Foster killed dflx Desert Eagle
[2025-07-25T20:32:46+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:33:06+0700] [Info] [kill] dflx killed Kenzo_Wazoski Desert Eagle
[2025-07-25T20:33:06+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:33:06+0700] [Info] [CMD]: Yuki_Haruto used command /drink water
[2025-07-25T20:33:10+0700] [Info] [CMD]: dflx used command /setarmour 3 100
[2025-07-25T20:33:18+0700] [Info] [kill] Albertus_Ragenmar killed Blake_Foster Desert Eagle
[2025-07-25T20:33:18+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:33:28+0700] [Info] [CMD]: dflx used command /setarmour 3 100w
[2025-07-25T20:33:28+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 5 100
[2025-07-25T20:33:30+0700] [Info] [CMD]: dflx used command /setarmour 3 100
[2025-07-25T20:33:47+0700] [Info] [kill] Blake_Foster killed Albertus_Ragenmar Desert Eagle
[2025-07-25T20:33:54+0700] [Info] [kill] Blake_Foster killed dflx Desert Eagle
[2025-07-25T20:33:54+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:34:05+0700] [Info] [CMD]: dflx used command /stats
[2025-07-25T20:34:10+0700] [Info] [CMD]: dflx used command /createevent
[2025-07-25T20:34:13+0700] [Info] [CMD]: dflx used command /editevent
[2025-07-25T20:34:15+0700] [Info] [chat] [dflx]:  mode
[2025-07-25T20:34:17+0700] [Info] [CMD]: Blake_Foster used command /jetpack
[2025-07-25T20:34:18+0700] [Info] [CMD]: dflx used command /editevent mode
[2025-07-25T20:34:19+0700] [Info] [CMD]: Albertus_Ragenmar used command /mode
[2025-07-25T20:34:20+0700] [Info] [CMD]: dflx used command /aduty
[2025-07-25T20:34:23+0700] [Info] [CMD]: Albertus_Ragenmar used command /reports
[2025-07-25T20:34:25+0700] [Info] [CMD]: Aaron_Fang used command /editevent mode
[2025-07-25T20:34:30+0700] [Info] [CMD]: Aaron_Fang used command /editevent mode
[2025-07-25T20:34:35+0700] [Info] [CMD]: Aaron_Fang used command /editevent mode dm
[2025-07-25T20:34:48+0700] [Info] [CMD]: Aaron_Fang used command /editevent skin
[2025-07-25T20:34:52+0700] [Info] [CMD]: Aaron_Fang used command /editevent skin 23
[2025-07-25T20:34:59+0700] [Info] [CMD]: Aaron_Fang used command /editevent skin red 23
[2025-07-25T20:35:08+0700] [Info] [CMD]: Blake_Foster used command /ahelp
[2025-07-25T20:35:10+0700] [Info] [CMD]: Aaron_Fang used command /editevent skin blue 25
[2025-07-25T20:35:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T20:35:15+0700] [Info] [CMD]: Albertus_Ragenmar used command /reports
[2025-07-25T20:35:23+0700] [Info] [CMD]: Aaron_Fang used command /editevent winnermoney 200000
[2025-07-25T20:35:25+0700] [Info] [CMD]: Aaron_Fang used command /editevent winnermoney 20000
[2025-07-25T20:35:34+0700] [Info] [CMD]: Aaron_Fang used command /editevent health 100
[2025-07-25T20:35:39+0700] [Info] [CMD]: Aaron_Fang used command /editevent armour 100
[2025-07-25T20:35:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-25T20:35:48+0700] [Info] [CMD]: Aaron_Fang used command /editevent weapon 30
[2025-07-25T20:35:51+0700] [Info] [CMD]: Aaron_Fang used command /startevent
[2025-07-25T20:35:55+0700] [Info] [CMD]: Aaron_Fang used command /joinevent
[2025-07-25T20:35:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /joinevent
[2025-07-25T20:35:59+0700] [Info] [CMD]: Blake_Foster used command /joinevent
[2025-07-25T20:35:59+0700] [Info] [CMD]: Albertus_Ragenmar used command /joinevent
[2025-07-25T20:36:09+0700] [Info] [CMD]: Mark_Olrando used command /joinevent
[2025-07-25T20:36:12+0700] [Info] [CMD]: Tata_Wazoski used command /stats
[2025-07-25T20:36:36+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-25T20:36:55+0700] [Info] [CMD]: Blake_Foster used command /myconditions
[2025-07-25T20:36:57+0700] [Info] [CMD]: Blake_Foster used command /mycondition
[2025-07-25T20:37:03+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-25T20:37:12+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:37:16+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:37:23+0700] [Info] [kill] Aaron_Fang killed Kenzo_Wazoski AK47
[2025-07-25T20:37:23+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:37:32+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-25T20:37:43+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:37:44+0700] [Info] [kill] Blake_Foster killed Kenzo_Wazoski AK47
[2025-07-25T20:37:59+0700] [Info] [kill] Albertus_Ragenmar killed Blake_Foster AK47
[2025-07-25T20:38:09+0700] [Info] [kill] Mark_Olrando killed Albertus_Ragenmar AK47
[2025-07-25T20:38:09+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:38:12+0700] [Info] [CMD]: Aaron_Fang used command /refill 1
[2025-07-25T20:38:17+0700] [Info] [CMD]: Aaron_Fang used command /goto 2
[2025-07-25T20:38:20+0700] [Info] [kill] Kenzo_Wazoski killed Mark_Olrando AK47
[2025-07-25T20:38:20+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:38:26+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-25T20:38:28+0700] [Info] [CMD]: Aaron_Fang used command /revive 1
[2025-07-25T20:38:31+0700] [Info] [CMD]: Aaron_Fang used command /healall
[2025-07-25T20:38:41+0700] [Info] [kill] Albertus_Ragenmar killed Aaron_Fang AK47
[2025-07-25T20:38:41+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:38:47+0700] [Info] [kill] Albertus_Ragenmar killed Mark_Olrando AK47
[2025-07-25T20:38:48+0700] [Info] [CMD]: Aaron_Fang used command /goto 2
[2025-07-25T20:39:09+0700] [Info] [kill] Albertus_Ragenmar killed Blake_Foster AK47
[2025-07-25T20:39:09+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:39:15+0700] [Info] [kill] Aaron_Fang killed Kenzo_Wazoski AK47
[2025-07-25T20:39:15+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:39:34+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:39:38+0700] [Info] [CMD]: Aaron_Fang used command /revive 3
[2025-07-25T20:39:41+0700] [Info] [CMD]: Aaron_Fang used command /heal 3
[2025-07-25T20:39:42+0700] [Info] [chat] [Blake_Foster]: a
[2025-07-25T20:39:49+0700] [Info] [kill] Albertus_Ragenmar killed Mark_Olrando AK47
[2025-07-25T20:39:49+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:39:58+0700] [Info] [kill] Aaron_Fang killed Albertus_Ragenmar AK47
[2025-07-25T20:39:58+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:40:09+0700] [Info] [kill] Aaron_Fang killed Kenzo_Wazoski AK47
[2025-07-25T20:40:18+0700] [Info] [kill] Albertus_Ragenmar killed Aaron_Fang AK47
[2025-07-25T20:40:18+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:40:24+0700] [Info] [CMD]: Aaron_Fang used command /goto 2
[2025-07-25T20:40:27+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:40:27+0700] [Info] [kill] Mark_Olrando killed Kenzo_Wazoski AK47
[2025-07-25T20:40:28+0700] [Info] [kill] Albertus_Ragenmar killed Blake_Foster AK47
[2025-07-25T20:40:28+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:40:35+0700] [Info] [kill] Albertus_Ragenmar killed Mark_Olrando AK47
[2025-07-25T20:40:57+0700] [Info] [CMD]: Aaron_Fang used command /goto 2
[2025-07-25T20:40:59+0700] [Info] [CMD]: Aaron_Fang used command /goto 2
[2025-07-25T20:41:03+0700] [Info] [kill] Kenzo_Wazoski killed Aaron_Fang AK47
[2025-07-25T20:41:03+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:41:04+0700] [Info] [kill] Blake_Foster killed Kenzo_Wazoski AK47
[2025-07-25T20:41:04+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:41:08+0700] [Info] [CMD]: Aaron_Fang used command /goto 2
[2025-07-25T20:41:14+0700] [Info] [kill] Aaron_Fang killed Albertus_Ragenmar AK47
[2025-07-25T20:41:35+0700] [Info] [CMD]: Mark_Olrando used command /b apa
[2025-07-25T20:41:43+0700] [Info] [CMD]: Aaron_Fang used command /revive 3
[2025-07-25T20:41:47+0700] [Info] [CMD]: Blake_Foster used command /arevive
[2025-07-25T20:41:48+0700] [Info] [CMD]: Aaron_Fang used command /healall
[2025-07-25T20:41:48+0700] [Info] [CMD]: Blake_Foster used command /aheal
[2025-07-25T20:41:49+0700] [Info] [CMD]: Albertus_Ragenmar used command /o layar gue merah
[2025-07-25T20:41:52+0700] [Info] [CMD]: Albertus_Ragenmar used command /revive
[2025-07-25T20:41:55+0700] [Info] [CMD]: Mark_Olrando used command /b aku gk dape
[2025-07-25T20:41:59+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-25T20:41:59+0700] [Info] [CMD]: Aaron_Fang used command /goto 0
[2025-07-25T20:42:01+0700] [Info] [CMD]: Albertus_Ragenmar used command /arevive
[2025-07-25T20:42:02+0700] [Info] [CMD]: Blake_Foster used command /revive
[2025-07-25T20:42:04+0700] [Info] [CMD]: Albertus_Ragenmar used command /b /revive
[2025-07-25T20:42:05+0700] [Info] [CMD]: Blake_Foster used command /revive 1
[2025-07-25T20:42:07+0700] [Info] [CMD]: Aaron_Fang used command /sethp 1 0
[2025-07-25T20:42:07+0700] [Info] [CMD]: Albertus_Ragenmar used command /health
[2025-07-25T20:42:09+0700] [Info] [CMD]: Blake_Foster used command /revive 1
[2025-07-25T20:42:11+0700] [Info] [CMD]: Albertus_Ragenmar used command /mycondition
[2025-07-25T20:42:12+0700] [Info] [CMD]: Blake_Foster used command /kill
[2025-07-25T20:42:12+0700] [Info] [CMD]: Aaron_Fang used command /sethp 1 0
[2025-07-25T20:42:12+0700] [Info] [CMD]: Albertus_Ragenmar used command /mycondition
[2025-07-25T20:42:13+0700] [Info] [CMD]: Blake_Foster used command /kills
[2025-07-25T20:42:15+0700] [Info] [CMD]: Blake_Foster used command /akill
[2025-07-25T20:42:15+0700] [Info] [CMD]: Blake_Foster used command /akills
[2025-07-25T20:42:18+0700] [Info] [CMD]: Aaron_Fang used command /sethp 0 0
[2025-07-25T20:42:18+0700] [Info] [CMD]: Albertus_Ragenmar used command /inspect 0
[2025-07-25T20:42:20+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:42:22+0700] [Info] [CMD]: Aaron_Fang used command /revive 1
[2025-07-25T20:42:24+0700] [Info] [CMD]: Aaron_Fang used command /revive 0
[2025-07-25T20:42:25+0700] [Info] [CMD]: Albertus_Ragenmar used command /approve inspecr
[2025-07-25T20:42:25+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:42:26+0700] [Info] [CMD]: Blake_Foster used command /akills
[2025-07-25T20:42:27+0700] [Info] [CMD]: Albertus_Ragenmar used command /approve inspect
[2025-07-25T20:42:27+0700] [Info] [CMD]: Blake_Foster used command /akill
[2025-07-25T20:42:30+0700] [Info] [CMD]: Aaron_Fang used command /sethp 3 0
[2025-07-25T20:42:31+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:42:35+0700] [Info] [CMD]: Aaron_Fang used command /revive 3
[2025-07-25T20:42:37+0700] [Info] [CMD]: Blake_Foster used command /ahelps
[2025-07-25T20:42:38+0700] [Info] [CMD]: Blake_Foster used command /ahelp
[2025-07-25T20:42:45+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 3
[2025-07-25T20:42:47+0700] [Info] [CMD]: Yuki_Haruto used command /o event race c
[2025-07-25T20:42:51+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 1
[2025-07-25T20:42:52+0700] [Info] [CMD]: Blake_Foster used command /bleeding
[2025-07-25T20:42:53+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 0
[2025-07-25T20:42:55+0700] [Info] [CMD]: Blake_Foster used command /bleeding 2
[2025-07-25T20:42:58+0700] [Info] [CMD]: Blake_Foster used command /bleeding 2
[2025-07-25T20:43:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 3
[2025-07-25T20:43:01+0700] [Info] [CMD]: Aaron_Fang used command /healall
[2025-07-25T20:43:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 3
[2025-07-25T20:43:09+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:43:10+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:43:11+0700] [Info] [chat] [Blake_Foster]: bunuh guaa
[2025-07-25T20:43:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 3
[2025-07-25T20:43:19+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:43:19+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 0
[2025-07-25T20:43:22+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:43:22+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-25T20:43:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 3
[2025-07-25T20:43:24+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-25T20:43:25+0700] [Info] [CMD]: Blake_Foster used command /revive blake
[2025-07-25T20:43:25+0700] [Info] [CMD]: Albertus_Ragenmar used command /tazer
[2025-07-25T20:43:27+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 0
[2025-07-25T20:43:30+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 0
[2025-07-25T20:43:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 3
[2025-07-25T20:43:31+0700] [Info] [CMD]: Albertus_Ragenmar used command /tazer
[2025-07-25T20:43:31+0700] [Info] [CMD]: Yuki_Haruto used command /inventory
[2025-07-25T20:43:33+0700] [Info] [CMD]: Blake_Foster used command /revive 2
[2025-07-25T20:43:34+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 0
[2025-07-25T20:43:36+0700] [Info] [CMD]: Yuki_Haruto used command /drink water
[2025-07-25T20:43:37+0700] [Info] [CMD]: Blake_Foster used command /revive 2
[2025-07-25T20:43:37+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-25T20:43:37+0700] [Info] [CMD]: Albertus_Ragenmar used command /beanbag
[2025-07-25T20:43:37+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (reason %d)." < 6456495
[2025-07-25T20:43:39+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-25T20:43:41+0700] [Info] [CMD]: Blake_Foster used command /heal 2
[2025-07-25T20:43:43+0700] [Info] [CMD]: Aaron_Fang used command /revive 3
[2025-07-25T20:43:43+0700] [Info] [CMD]: Blake_Foster used command /revive 2
[2025-07-25T20:43:46+0700] [Info] [kill] Albertus_Ragenmar killed Aaron_Fang Shotgun
[2025-07-25T20:43:46+0700] [Info] [death] Aaron_Fang died 255
[2025-07-25T20:43:49+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:43:51+0700] [Info] [CMD]: Albertus_Ragenmar used command /beanbag
[2025-07-25T20:43:59+0700] [Info] [chat] [Aaron_Fang]: Heal
[2025-07-25T20:44:03+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-25T20:44:06+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T20:44:06+0700] [Info] [kill] Kenzo_Wazoski killed Albertus_Ragenmar Silenced Pistol
[2025-07-25T20:44:13+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-25T20:44:13+0700] [Info] [CMD]: Albertus_Ragenmar used command /giveup
[2025-07-25T20:44:19+0700] [Info] [sv:dbg:network:connect] : disconnecting player (3) ...
[2025-07-25T20:44:19+0700] [Info] [part] Aaron_Fang has left the server (3:1)
[2025-07-25T20:44:24+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-25T20:44:24+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-25T20:44:27+0700] [Info] [CMD]: Albertus_Ragenmar used command /or revive fik
[2025-07-25T20:44:27+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-25T20:44:27+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-25T20:44:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer 3
[2025-07-25T20:44:31+0700] [Info] [CMD]: Albertus_Ragenmar used command /online
[2025-07-25T20:44:37+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-25T20:44:44+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-25T20:44:56+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-25T20:45:26+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-25T20:45:47+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-25T20:46:01+0700] [Info] [chat] [Blake_Foster]: a
[2025-07-25T20:46:02+0700] [Info] [chat] [Blake_Foster]: a
[2025-07-25T20:46:04+0700] [Info] [part] Mark_Olrando has left the server (6:1)
[2025-07-25T20:46:05+0700] [Info] [CMD]: Blake_Foster used command /revive alber
[2025-07-25T20:46:07+0700] [Info] [chat] [Blake_Foster]: a
[2025-07-25T20:46:12+0700] [Info] [CMD]: Blake_Foster used command /heal 0
[2025-07-25T20:46:15+0700] [Info] [CMD]: Albertus_Ragenmar used command /b unfreeze coba
[2025-07-25T20:46:22+0700] [Info] [CMD]: Blake_Foster used command /unfreeze 0
[2025-07-25T20:46:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /unfreeze
[2025-07-25T20:46:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /freeze
[2025-07-25T20:46:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /freeze 2
[2025-07-25T20:46:47+0700] [Info] [CMD]: Yuki_Haruto used command /droptrash
[2025-07-25T20:46:49+0700] [Info] [CMD]: Yuki_Haruto used command /droptrash
[2025-07-25T20:46:52+0700] [Info] [sv:dbg:network:connect] : disconnecting player (5) ...
[2025-07-25T20:46:52+0700] [Info] [part] Tata_Wazoski has left the server (5:0)
[2025-07-25T20:47:02+0700] [Info] [sv:dbg:network:connect] : connecting player (3) with address (**************) ...
[2025-07-25T20:47:02+0700] [Info] [sv:dbg:network:connect] : player (3) assigned key (6e428a6e8b52f12c)
[2025-07-25T20:47:02+0700] [Info] [connection] incoming connection: **************:60747 id: 3
[2025-07-25T20:47:02+0700] [Info] [join] tata has joined the server (3:**************)
[2025-07-25T20:47:04+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-25T20:47:07+0700] [Info] [sv:dbg:network:receive] : player (3) identified (port:60748)
[2025-07-25T20:47:28+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-25T20:47:38+0700] [Info] [CMD]: Albertus_Ragenmar used command /b /beanbag
[2025-07-25T20:47:41+0700] [Info] [sv:dbg:network:connect] : connecting player (5) with address (**************) ...
[2025-07-25T20:47:41+0700] [Info] [sv:dbg:network:connect] : player (5) assigned key (6e428a6e7b64fd4d)
[2025-07-25T20:47:41+0700] [Info] [connection] incoming connection: **************:50409 id: 5
[2025-07-25T20:47:41+0700] [Info] [join] Aldo has joined the server (5:**************)
[2025-07-25T20:47:42+0700] [Info] [CMD]: Yuki_Haruto used command /o event lagi kagak?
[2025-07-25T20:47:45+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:47:46+0700] [Info] [sv:dbg:network:receive] : player (5) identified (port:50410)
[2025-07-25T20:48:22+0700] [Info] [CMD]: Blake_Foster used command /v list
[2025-07-25T20:48:23+0700] [Info] [CMD]: Albertus_Ragenmar used command /goto
[2025-07-25T20:48:29+0700] [Info] [CMD]: Blake_Foster used command /bringcar
[2025-07-25T20:48:31+0700] [Info] [CMD]: Blake_Foster used command /bringcar 16
[2025-07-25T20:48:35+0700] [Info] [CMD]: Blake_Foster used command /lock
[2025-07-25T20:48:43+0700] [Info] [INFO] Updated capacity for 6 garbage bins
[2025-07-25T20:48:44+0700] [Info] [chat] [Albertus_Ragenmar]: ke sapd dulu
[2025-07-25T20:48:44+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:48:47+0700] [Info] [CMD]: Mark_Olrando used command /b aloo
[2025-07-25T20:48:49+0700] [Info] [chat] [Kenzo_Wazoski]: ok ok
[2025-07-25T20:48:49+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:48:50+0700] [Info] [chat] [Kenzo_Wazoski]: alo
[2025-07-25T20:48:50+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:48:51+0700] [Info] [CMD]: Albertus_Ragenmar used command /b hallo
[2025-07-25T20:49:03+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-25T20:49:03+0700] [Info] [chat] [Blake_Foster]: pukulin tuh orang item
[2025-07-25T20:49:03+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:49:07+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-25T20:49:08+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-25T20:49:08+0700] [Info] [CMD]: Mark_Olrando used command /b mau kemana
[2025-07-25T20:49:09+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-25T20:49:12+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-25T20:49:24+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:49:26+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:49:29+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:49:54+0700] [Info] [chat] [Blake_Foster]: aku mau jadi fbi
[2025-07-25T20:49:54+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T20:50:10+0700] [Info] [chat] [Blake_Foster]: ayo begal
[2025-07-25T20:50:11+0700] [Info] [CMD]: Albertus_Ragenmar used command /flocker
[2025-07-25T20:50:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:50:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:50:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:50:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:50:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:50:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:50:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:50:53+0700] [Info] [CMD]: Yuki_Haruto used command /acc
[2025-07-25T20:50:57+0700] [Info] [CMD]: Yuki_Haruto used command /acc
[2025-07-25T20:51:55+0700] [Info] [CMD]: Albertus_Ragenmar used command /b gua ga pake voice wkwk
[2025-07-25T20:51:57+0700] [Info] [CMD]: Albertus_Ragenmar used command /b ga kedenger
[2025-07-25T20:52:08+0700] [Info] [CMD]: Albertus_Ragenmar used command /flocker
[2025-07-25T20:52:27+0700] [Info] [chat] [Blake_Foster]: Halo
[2025-07-25T20:52:46+0700] [Info] [CMD]: Albertus_Ragenmar used command /r Sergeant Ragenmar reporting for off duty.
[2025-07-25T20:53:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /r Sergent 
[2025-07-25T20:53:10+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T20:53:13+0700] [Info] [CMD]: Mark_Olrando used command /drink water
[2025-07-25T20:53:22+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:53:25+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:53:26+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:53:26+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:53:28+0700] [Info] [CMD]: Mark_Olrando used command /piss
[2025-07-25T20:53:29+0700] [Info] [part] Albertus_Ragenmar has left the server (0:1)
[2025-07-25T20:53:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /r Oficer | reporting for of duty
[2025-07-25T20:53:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /r Oficer | reporting for of duty
[2025-07-25T20:53:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:53:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /flocker
[2025-07-25T20:53:49+0700] [Info] [CMD]: Yuki_Haruto used command /aduty
[2025-07-25T20:53:54+0700] [Info] [CMD]: FanID used command /a a
[2025-07-25T20:54:00+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:54:00+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-25T20:54:12+0700] [Info] [CMD]: FanID used command /aduty
[2025-07-25T20:54:48+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-25T20:54:50+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-25T20:55:11+0700] [Info] [CMD]: Yuki_Haruto used command /paytoll
[2025-07-25T20:55:11+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:04+0700] [Info] [CMD]: Blake_Foster used command /goto kenzo
[2025-07-25T20:56:04+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:04+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:04+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:05+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:05+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:06+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:06+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:06+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:06+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:07+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:07+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:07+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:56:07+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T20:57:00+0700] [Info] [CMD]: Yuki_Haruto used command /buychainsaw
[2025-07-25T20:57:18+0700] [Info] [CMD]: Tata_Wazoski used command /buychainsaw
[2025-07-25T20:57:30+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T20:57:34+0700] [Info] [CMD]: Tata_Wazoski used command /v trunk
[2025-07-25T20:57:40+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (reason %d)." < 6456495
[2025-07-25T20:57:49+0700] [Info] [CMD]: Yuki_Haruto used command /help
[2025-07-25T20:57:51+0700] [Info] [CMD]: Yuki_Haruto used command /joblist
[2025-07-25T20:58:02+0700] [Info] [CMD]: Yuki_Haruto used command /joblist
[2025-07-25T20:58:08+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-25T20:58:10+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (reason %d)." < 6456495
[2025-07-25T20:58:11+0700] [Info] [CMD]: Yuki_Haruto used command /findtree
[2025-07-25T20:58:20+0700] [Info] [CMD]: Yuki_Haruto used command /findtree
[2025-07-25T20:58:40+0700] [Info] [kill] Mark_Olrando killed Kenzo_Wazoski Fist
[2025-07-25T20:58:40+0700] [Info] [death] Kenzo_Wazoski died 255
[2025-07-25T20:58:48+0700] [Info] [chat] [Kenzo_Wazoski]: pukur terus?
[2025-07-25T20:58:52+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-25T20:59:06+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-25T20:59:26+0700] [Info] [CMD]: Yuki_Haruto used command /findtree
[2025-07-25T21:00:01+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T21:00:29+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-25T21:00:40+0700] [Info] [CMD]: Yuki_Haruto used command /velist
[2025-07-25T21:00:44+0700] [Info] [CMD]: Yuki_Haruto used command /v list
[2025-07-25T21:00:50+0700] [Info] [CMD]: Yuki_Haruto used command /gotocar 
[2025-07-25T21:00:53+0700] [Info] [CMD]: Yuki_Haruto used command /v list
[2025-07-25T21:00:56+0700] [Info] [CMD]: Yuki_Haruto used command /gotocar 23
[2025-07-25T21:01:06+0700] [Info] [CMD]: Yuki_Haruto used command /findtree
[2025-07-25T21:01:57+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-25T21:02:43+0700] [Info] [CMD]: Yuki_Haruto used command /v trunk
[2025-07-25T21:02:46+0700] [Info] [CMD]: Yuki_Haruto used command /v trunk
[2025-07-25T21:02:54+0700] [Info] [CMD]: Yuki_Haruto used command /findtree
[2025-07-25T21:03:00+0700] [Info] [CMD]: Tata_Wazoski used command /v trunk
[2025-07-25T21:03:06+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-25T21:03:51+0700] [Info] [CMD]: Yuki_Haruto used command /findtree
[2025-07-25T21:04:20+0700] [Info] [CMD]: Yuki_Haruto used command /findtree
[2025-07-25T21:04:54+0700] [Info] [CMD]: Tata_Wazoski used command /selllumber
[2025-07-25T21:05:13+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-25T21:06:00+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-25T21:06:03+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-25T21:06:11+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-25T21:06:26+0700] [Info] [CMD]: Yuki_Haruto used command /selllumber
[2025-07-25T21:07:22+0700] [Info] [AFK] Player Mark_Olrando (ID: 5) masuk mode AFK
[2025-07-25T21:07:47+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:07:48+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:07:53+0700] [Info] [AFK] Player Blake_Foster (ID: 2) masuk mode AFK
[2025-07-25T21:08:02+0700] [Info] [CMD]: Yuki_Haruto used command /paytoll
[2025-07-25T21:08:02+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:08:37+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-25T21:08:37+0700] [Info] [AFK] Player Blake_Foster (ID: 2) disconnect saat AFK - data telah di-restore
[2025-07-25T21:08:37+0700] [Info] [part] Blake_Foster has left the server (2:1)
[2025-07-25T21:08:38+0700] [Info] [CMD]: Mark_Olrando used command /afk UBRND5
[2025-07-25T21:08:38+0700] [Info] [AFK] Player Mark_Olrando (ID: 5) keluar dari mode AFK
[2025-07-25T21:09:19+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-25T21:09:43+0700] [Info] [INFO] Updated capacity for 5 garbage bins
[2025-07-25T21:09:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T21:09:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-25T21:13:10+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-25T21:13:24+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T21:13:30+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T21:13:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-25T21:13:35+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-25T21:13:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /v list
[2025-07-25T21:14:10+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-25T21:14:14+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 15
[2025-07-25T21:14:21+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 15
[2025-07-25T21:14:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-25T21:15:00+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:15:01+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:15:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T21:15:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T21:15:16+0700] [Info] [CMD]: Yuki_Haruto used command /v list
[2025-07-25T21:15:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T21:15:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T21:15:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T21:15:25+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 21
[2025-07-25T21:15:29+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T21:15:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-25T21:15:40+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*************) ...
[2025-07-25T21:15:40+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (1e23ba67644145d5)
[2025-07-25T21:15:40+0700] [Info] [connection] incoming connection: *************:64372 id: 0
[2025-07-25T21:15:40+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-25T21:15:42+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:15:43+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:15:44+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:15:44+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-25T21:15:45+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:50779)
[2025-07-25T21:16:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-25T21:16:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-25T21:16:26+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T21:16:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /handsub
[2025-07-25T21:16:36+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 min
[2025-07-25T21:16:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 4
[2025-07-25T21:16:45+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 senjata gua hilang karna mati tadi
[2025-07-25T21:16:52+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 semua senjata
[2025-07-25T21:16:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuf 4
[2025-07-25T21:16:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /uncuff 4
[2025-07-25T21:17:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-25T21:17:14+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-25T21:17:21+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T21:17:28+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T21:17:31+0700] [Info] [chat] [Kenzo_Wazoski]: soryw
[2025-07-25T21:17:31+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T21:17:34+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-25T21:17:37+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-25T21:17:39+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-25T21:17:44+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 min???
[2025-07-25T21:18:33+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T21:18:36+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 min???
[2025-07-25T21:18:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /fill
[2025-07-25T21:18:44+0700] [Info] [chat] [Kenzo_Wazoski]: \
[2025-07-25T21:18:44+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-25T21:18:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /refuel
[2025-07-25T21:19:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 0 bang tambahanin belik sultan nak ap
[2025-07-25T21:19:11+0700] [Info] [CMD]: Mark_Olrando used command /paytoll
[2025-07-25T21:19:11+0700] [Info] [CMD]: Mark_Olrando used command /open
[2025-07-25T21:19:25+0700] [Info] [CMD]: Aaron_Fang used command /refill 0
[2025-07-25T21:19:40+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-25T21:19:41+0700] [Info] [CMD]: Aaron_Fang used command /goto 3
[2025-07-25T21:19:43+0700] [Info] [CMD]: Aaron_Fang used command /goto 3
[2025-07-25T21:19:53+0700] [Info] [CMD]: Aaron_Fang used command /givewep 3
[2025-07-25T21:19:58+0700] [Info] [CMD]: Aaron_Fang used command /givewep 3 24 200
[2025-07-25T21:20:02+0700] [Info] [CMD]: Aaron_Fang used command /givewep 3 2 200
[2025-07-25T21:20:09+0700] [Info] [CMD]: Aaron_Fang used command /b kenapa
[2025-07-25T21:20:39+0700] [Info] [CMD]: Mark_Olrando used command /paytoll
[2025-07-25T21:20:39+0700] [Info] [CMD]: Mark_Olrando used command /open
[2025-07-25T21:20:46+0700] [Info] [CMD]: Aaron_Fang used command /givewep 3 9 
[2025-07-25T21:20:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /m apala yuki
[2025-07-25T21:20:58+0700] [Info] [CMD]: Aaron_Fang used command /give 3 25 200
[2025-07-25T21:21:00+0700] [Info] [CMD]: Tata_Wazoski used command /pay 0 15
[2025-07-25T21:21:00+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-25T21:21:02+0700] [Info] [CMD]: Aaron_Fang used command /givewep 3 25 200
[2025-07-25T21:21:09+0700] [Info] [CMD]: Aaron_Fang used command /pay 3 100
[2025-07-25T21:21:09+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-25T21:21:14+0700] [Info] [CMD]: Aaron_Fang used command /healall
[2025-07-25T21:21:16+0700] [Info] [chat] [Tata_Wazoski]: kagak usah bang
[2025-07-25T21:21:35+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 yaudah,lah maksih bang
[2025-07-25T21:21:57+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-25T21:22:15+0700] [Info] [CMD]: Aaron_Fang used command /v list 1
[2025-07-25T21:22:21+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 3
[2025-07-25T21:22:38+0700] [Info] [CMD]: Aaron_Fang used command /arepair
[2025-07-25T21:22:43+0700] [Info] [CMD]: Aaron_Fang used command /arepair 15
[2025-07-25T21:22:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-25T21:22:48+0700] [Info] [CMD]: Aaron_Fang used command /goto cityhall
[2025-07-25T21:22:48+0700] [Info] [kill] Yuki_Haruto killed Tata_Wazoski Vehicle
[2025-07-25T21:22:49+0700] [Info] [death] Tata_Wazoski died 255
[2025-07-25T21:23:23+0700] [Info] [CMD]: Yuki_Haruto used command /bring 0
[2025-07-25T21:23:37+0700] [Info] [CMD]: Mark_Olrando used command /lcok
[2025-07-25T21:23:39+0700] [Info] [CMD]: Mark_Olrando used command /lcok
[2025-07-25T21:23:45+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-25T21:24:27+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-25T21:24:27+0700] [Info] [sv:dbg:network:connect] : disconnecting player (4) ...
[2025-07-25T21:24:27+0700] [Info] [part] Yuki_Haruto has left the server (4:1)
[2025-07-25T21:24:32+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-25T21:24:36+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-25T21:24:43+0700] [Info] [CMD]: Mark_Olrando used command /piss
[2025-07-25T21:24:57+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-25T21:24:57+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (6e428a6e4d242180)
[2025-07-25T21:24:57+0700] [Info] [connection] incoming connection: **************:52360 id: 2
[2025-07-25T21:24:57+0700] [Info] [join] FanID has joined the server (2:**************)
[2025-07-25T21:25:02+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:57352)
[2025-07-25T21:25:11+0700] [Info] [CMD]: Aaron_Fang used command /deposit
[2025-07-25T21:25:19+0700] [Info] [CMD]: Aaron_Fang used command /deposit 500
[2025-07-25T21:25:25+0700] [Info] [CMD]: Aaron_Fang used command /clearallchat
[2025-07-25T21:25:28+0700] [Info] [CMD]: Aaron_Fang used command /clearallchat
[2025-07-25T21:25:35+0700] [Info] [CMD]: Aaron_Fang used command /aduty
[2025-07-25T21:25:46+0700] [Info] [CMD]: dflx used command /goto 3
[2025-07-25T21:25:48+0700] [Info] [CMD]: dflx used command /goto 3
[2025-07-25T21:25:53+0700] [Info] [CMD]: dflx used command /goto cityhall
[2025-07-25T21:26:03+0700] [Info] [CMD]: dflx used command /gotohouse 11
[2025-07-25T21:26:06+0700] [Info] [CMD]: dflx used command /goto house 11
[2025-07-25T21:26:12+0700] [Info] [CMD]: dflx used command /aduty
[2025-07-25T21:26:21+0700] [Info] [CMD]: Aaron_Fang used command /b a
[2025-07-25T21:26:22+0700] [Info] [CMD]: Aaron_Fang used command /b a
[2025-07-25T21:26:22+0700] [Info] [CMD]: Tata_Wazoski used command /drink water
[2025-07-25T21:26:22+0700] [Info] [CMD]: Aaron_Fang used command /b a
[2025-07-25T21:26:27+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-25T21:27:07+0700] [Info] [CMD]: Tata_Wazoski used command /atm
[2025-07-25T21:27:11+0700] [Info] [CMD]: Tata_Wazoski used command /atm
[2025-07-25T21:27:18+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-25T21:27:36+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-25T21:27:36+0700] [Info] [part] FanID has left the server (2:0)
[2025-07-25T21:27:40+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-25T21:27:40+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (6e428a6e03b0e8d6)
[2025-07-25T21:27:40+0700] [Info] [connection] incoming connection: **************:51054 id: 2
[2025-07-25T21:27:40+0700] [Info] [join] FanID has joined the server (2:**************)
[2025-07-25T21:27:43+0700] [Info] [CMD]: Aaron_Fang used command /arepairw
[2025-07-25T21:27:45+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:51055)
[2025-07-25T21:27:45+0700] [Info] [CMD]: Aaron_Fang used command /arepair
[2025-07-25T21:27:52+0700] [Info] [CMD]: Tata_Wazoski used command /lay 3
[2025-07-25T21:27:57+0700] [Info] [CMD]: Tata_Wazoski used command /lay 3
[2025-07-25T21:28:07+0700] [Info] [AFK] Player Kenzo_Wazoski (ID: 1) masuk mode AFK
[2025-07-25T21:28:18+0700] [Info] [CMD]: Yuki_Haruto used command /bring 3
[2025-07-25T21:28:20+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-25T21:28:20+0700] [Info] [part] Yuki_Haruto has left the server (2:1)
[2025-07-25T21:29:01+0700] [Info] [sv:dbg:network:connect] : disconnecting player (3) ...
[2025-07-25T21:29:01+0700] [Info] [part] Tata_Wazoski has left the server (3:1)
[2025-07-25T21:29:28+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-25T21:29:28+0700] [Info] [AFK] Player Kenzo_Wazoski (ID: 1) disconnect saat AFK - data telah di-restore
[2025-07-25T21:29:28+0700] [Info] [part] Kenzo_Wazoski has left the server (1:0)
[2025-07-25T21:30:10+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-25T21:30:10+0700] [Info] [part] Aaron_Fang has left the server (0:1)
[2025-07-25T21:30:21+0700] [Info] [sv:dbg:network:connect] : disconnecting player (5) ...
[2025-07-25T21:30:21+0700] [Info] [part] Mark_Olrando has left the server (5:1)
[2025-07-25T21:30:43+0700] [Info] [INFO] Updated capacity for 5 garbage bins
[2025-07-25T21:45:58+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-25T21:45:58+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (e7a31c68e9c01597)
[2025-07-25T21:45:58+0700] [Info] [connection] incoming connection: **************:31559 id: 0
[2025-07-25T21:45:58+0700] [Info] [join] Weziza has joined the server (0:**************)
[2025-07-25T21:46:05+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:31555)
[2025-07-25T21:46:27+0700] [Info] [CMD]: Blake_Foster used command /ahelp
[2025-07-25T21:46:36+0700] [Info] [CMD]: Blake_Foster used command /goto
[2025-07-25T21:46:40+0700] [Info] [CMD]: Blake_Foster used command /goto smb
[2025-07-25T21:46:47+0700] [Info] [CMD]: Blake_Foster used command /fish
[2025-07-25T21:46:51+0700] [Info] [CMD]: Blake_Foster used command /goto smb2
[2025-07-25T21:46:55+0700] [Info] [CMD]: Blake_Foster used command /gps
[2025-07-25T21:46:59+0700] [Info] [CMD]: Blake_Foster used command /maps
[2025-07-25T21:47:12+0700] [Info] [CMD]: Blake_Foster used command /maps
[2025-07-25T21:47:15+0700] [Info] [CMD]: Blake_Foster used command /maps
[2025-07-25T21:47:18+0700] [Info] [CMD]: Blake_Foster used command /maps
[2025-07-25T21:48:00+0700] [Info] [CMD]: Blake_Foster used command /agive
[2025-07-25T21:48:06+0700] [Info] [CMD]: Blake_Foster used command /goto ls
[2025-07-25T21:48:15+0700] [Info] [CMD]: Blake_Foster used command /v list
[2025-07-25T21:48:23+0700] [Info] [CMD]: Blake_Foster used command /bringcar 15
[2025-07-25T21:49:13+0700] [Info] [CMD]: Blake_Foster used command /lock
[2025-07-25T21:49:15+0700] [Info] [CMD]: Blake_Foster used command /nlock
[2025-07-25T21:51:00+0700] [Info] [CMD]: Blake_Foster used command //lock
[2025-07-25T21:51:02+0700] [Info] [CMD]: Blake_Foster used command /lock
[2025-07-25T21:51:43+0700] [Info] [INFO] Updated capacity for 5 garbage bins
[2025-07-25T21:51:47+0700] [Info] [CMD]: Blake_Foster used command /buy
[2025-07-25T21:51:50+0700] [Info] [CMD]: Blake_Foster used command /buy
[2025-07-25T21:51:53+0700] [Info] [CMD]: Blake_Foster used command /buy
[2025-07-25T21:51:55+0700] [Info] [CMD]: Blake_Foster used command /buy
[2025-07-25T21:52:02+0700] [Info] [CMD]: Blake_Foster used command /buy
[2025-07-25T21:52:14+0700] [Info] [CMD]: Blake_Foster used command /lock
[2025-07-25T21:52:22+0700] [Info] [CMD]: Blake_Foster used command /goto smb2
[2025-07-25T21:52:42+0700] [Info] [CMD]: Blake_Foster used command /ame hook a bait and start fishing.
[2025-07-25T21:52:43+0700] [Info] [CMD]: Blake_Foster used command /fish
[2025-07-25T21:55:25+0700] [Info] [CMD]: Blake_Foster used command /goto ls
[2025-07-25T21:55:46+0700] [Info] [CMD]: Blake_Foster used command /flocker
[2025-07-25T21:55:48+0700] [Info] [CMD]: Blake_Foster used command /flocker
[2025-07-25T21:55:53+0700] [Info] [CMD]: Blake_Foster used command /flocker
[2025-07-25T21:55:57+0700] [Info] [CMD]: Blake_Foster used command /flocker
[2025-07-25T21:56:16+0700] [Info] [CMD]: Blake_Foster used command /crossarms
[2025-07-25T21:56:18+0700] [Info] [CMD]: Blake_Foster used command /crossarms 2
[2025-07-25T21:56:20+0700] [Info] [CMD]: Blake_Foster used command /crossarms 1
[2025-07-25T21:56:22+0700] [Info] [CMD]: Blake_Foster used command /crossarms 3
[2025-07-25T21:56:23+0700] [Info] [CMD]: Blake_Foster used command /crossarms 4
[2025-07-25T21:56:25+0700] [Info] [CMD]: Blake_Foster used command /crossarms 1
[2025-07-25T21:58:20+0700] [Info] [CMD]: Blake_Foster used command /flocker
[2025-07-25T21:58:27+0700] [Info] [CMD]: Blake_Foster used command /help
[2025-07-25T21:58:29+0700] [Info] [CMD]: Blake_Foster used command /joblist
[2025-07-25T21:58:52+0700] [Info] [CMD]: Blake_Foster used command /skill`
[2025-07-25T21:58:54+0700] [Info] [CMD]: Blake_Foster used command /skill`
[2025-07-25T21:58:55+0700] [Info] [CMD]: Blake_Foster used command /skills
[2025-07-25T22:04:11+0700] [Info] [AFK] Player Blake_Foster (ID: 0) masuk mode AFK
[2025-07-25T22:12:43+0700] [Info] [INFO] Updated capacity for 5 garbage bins
[2025-07-25T22:38:07+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (*************) ...
[2025-07-25T22:38:07+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (1e23ba67381b5483)
[2025-07-25T22:38:07+0700] [Info] [connection] incoming connection: *************:56689 id: 1
[2025-07-25T22:38:07+0700] [Info] [join] dflx has joined the server (1:*************)
[2025-07-25T22:38:11+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:52426)
[2025-07-25T22:38:20+0700] [Info] [CMD]: Aaron_Fang used command /goto 0
[2025-07-25T22:39:15+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-25T22:39:15+0700] [Info] [AFK] Player Blake_Foster (ID: 0) disconnect saat AFK - data telah di-restore
[2025-07-25T22:39:15+0700] [Info] [part] Blake_Foster has left the server (0:1)
[2025-07-25T22:48:05+0700] [Info] [AFK] Player Aaron_Fang (ID: 1) masuk mode AFK
[2025-07-25T22:49:43+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-25T22:49:43+0700] [Info] [AFK] Player Aaron_Fang (ID: 1) disconnect saat AFK - data telah di-restore
[2025-07-25T22:49:43+0700] [Info] [part] Aaron_Fang has left the server (1:1)
[2025-07-25T22:50:12+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*************) ...
[2025-07-25T22:50:12+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (1e23ba6779591c09)
[2025-07-25T22:50:12+0700] [Info] [connection] incoming connection: *************:60048 id: 0
[2025-07-25T22:50:12+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-25T22:50:17+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:53818)
[2025-07-25T22:55:28+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) masuk mode AFK
[2025-07-25T22:57:12+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-25T22:57:12+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) disconnect saat AFK - data telah di-restore
[2025-07-25T22:57:12+0700] [Info] [part] Aaron_Fang has left the server (0:0)
[2025-07-25T22:57:28+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*************) ...
[2025-07-25T22:57:28+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (1e23ba67c65e26af)
[2025-07-25T22:57:28+0700] [Info] [connection] incoming connection: *************:59668 id: 0
[2025-07-25T22:57:28+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-25T22:57:33+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:62469)
[2025-07-25T23:00:01+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-25T23:00:01+0700] [Info] [part] Aaron_Fang has left the server (0:1)
[2025-07-25T23:00:33+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*************) ...
[2025-07-25T23:00:33+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (1e23ba67e8138671)
[2025-07-25T23:00:33+0700] [Info] [connection] incoming connection: *************:64361 id: 0
[2025-07-25T23:00:33+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-25T23:00:37+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:55113)
[2025-07-25T23:02:12+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-25T23:02:12+0700] [Info] [part] Aaron_Fang has left the server (0:0)
[2025-07-25T23:03:44+0700] [Info] [connection] incoming connection: *************:59586 id: 0
[2025-07-25T23:03:44+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-25T23:07:55+0700] [Info] [connection] incoming connection: **************:35026 id: 1
[2025-07-25T23:07:55+0700] [Info] [join] Jenarrey has joined the server (1:**************)
[2025-07-25T23:08:32+0700] [Info] [part] Max_Jacob has left the server (1:1)
[2025-07-25T23:09:36+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-25T23:09:40+0700] [Info] [CMD]: Aaron_Fang used command /gps
[2025-07-25T23:10:02+0700] [Info] [connection] incoming connection: **************:35029 id: 1
[2025-07-25T23:10:02+0700] [Info] [join] Jenarrey has joined the server (1:**************)
[2025-07-25T23:10:20+0700] [Info] [CMD]: Albertus_Ragenmar used command /online
[2025-07-25T23:10:22+0700] [Info] [CMD]: Albertus_Ragenmar used command /trace
[2025-07-25T23:10:29+0700] [Info] [CMD]: Albertus_Ragenmar used command /mdc
[2025-07-25T23:11:23+0700] [Info] [CMD]: Albertus_Ragenmar used command /mdc
[2025-07-25T23:11:27+0700] [Info] [CMD]: Albertus_Ragenmar used command /mdc
[2025-07-25T23:11:46+0700] [Info] [CMD]: Albertus_Ragenmar used command /call
[2025-07-25T23:13:53+0700] [Info] [CMD]: Albertus_Ragenmar used command /stast
[2025-07-25T23:13:55+0700] [Info] [CMD]: Albertus_Ragenmar used command /stats
[2025-07-25T23:15:06+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-25T23:16:15+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-25T23:16:15+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (6e428a6e55dc8d5d)
[2025-07-25T23:16:15+0700] [Info] [connection] incoming connection: **************:57255 id: 2
[2025-07-25T23:16:15+0700] [Info] [join] Aldo has joined the server (2:**************)
[2025-07-25T23:16:20+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:65386)
[2025-07-25T23:16:38+0700] [Info] [CMD]: Aaron_Fang used command /inventory
[2025-07-25T23:16:45+0700] [Info] [CMD]: Aaron_Fang used command /drink water
[2025-07-25T23:16:47+0700] [Info] [CMD]: Aaron_Fang used command /inventory
[2025-07-25T23:19:01+0700] [Info] [AFK] Player Albertus_Ragenmar (ID: 1) masuk mode AFK
[2025-07-25T23:21:42+0700] [Info] [CMD]: Aaron_Fang used command /texsize
[2025-07-25T23:21:45+0700] [Info] [CMD]: Aaron_Fang used command /tsize
[2025-07-25T23:21:47+0700] [Info] [CMD]: Aaron_Fang used command /size
[2025-07-25T23:22:31+0700] [Info] [part] Aaron_Fang has left the server (0:1)
[2025-07-25T23:23:00+0700] [Info] [connection] incoming connection: *************:52660 id: 0
[2025-07-25T23:23:00+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-25T23:23:17+0700] [Info] [CMD]: Aaron_Fang used command /arepair
[2025-07-25T23:24:18+0700] [Info] [CMD]: Aaron_Fang used command /refuel
[2025-07-25T23:24:46+0700] [Info] [CMD]: Aaron_Fang used command /piss
[2025-07-25T23:25:35+0700] [Info] [CMD]: Aaron_Fang used command /open
[2025-07-25T23:25:35+0700] [Info] [CMD]: Aaron_Fang used command /open
[2025-07-25T23:26:00+0700] [Info] [CMD]: Albertus_Ragenmar used command /afk 2di2oo
[2025-07-25T23:26:00+0700] [Info] [AFK] Player Albertus_Ragenmar (ID: 1) keluar dari mode AFK
[2025-07-25T23:26:12+0700] [Info] [CMD]: Aaron_Fang used command /windows
[2025-07-25T23:26:16+0700] [Info] [CMD]: Aaron_Fang used command /v window
[2025-07-25T23:26:19+0700] [Info] [CMD]: Aaron_Fang used command /v window
[2025-07-25T23:26:21+0700] [Info] [CMD]: Aaron_Fang used command /v windows
[2025-07-25T23:26:25+0700] [Info] [chat] [Aaron_Fang]: Naik belakang
[2025-07-25T23:26:31+0700] [Info] [chat] [Albertus_Ragenmar]: okee
[2025-07-25T23:26:41+0700] [Info] [CMD]: Albertus_Ragenmar used command /lay 3
[2025-07-25T23:29:48+0700] [Info] [CMD]: Aaron_Fang used command /startmine
[2025-07-25T23:29:56+0700] [Info] [CMD]: Mark_Olrando used command /startmine
[2025-07-25T23:33:19+0700] [Info] [CMD]: Mark_Olrando used command /drink water
[2025-07-25T23:34:49+0700] [Info] [CMD]: Aaron_Fang used command /ahelp
[2025-07-25T23:34:58+0700] [Info] [CMD]: Aaron_Fang used command /setfaction
[2025-07-25T23:35:02+0700] [Info] [CMD]: Aaron_Fang used command /setfaction
[2025-07-25T23:35:06+0700] [Info] [CMD]: Aaron_Fang used command /drink water
[2025-07-25T23:35:09+0700] [Info] [CMD]: Aaron_Fang used command /setfaction
[2025-07-25T23:35:13+0700] [Info] [CMD]: Aaron_Fang used command /asetfaction
[2025-07-25T23:35:30+0700] [Info] [CMD]: Aaron_Fang used command /setrank
[2025-07-25T23:35:36+0700] [Info] [CMD]: Aaron_Fang used command /createfaction
[2025-07-25T23:35:48+0700] [Info] [CMD]: Aaron_Fang used command /createfaction 3 San Andreas Medical Department
[2025-07-25T23:35:51+0700] [Info] [part] Albertus_Ragenmar has left the server (1:1)
[2025-07-25T23:36:10+0700] [Info] [CMD]: Aaron_Fang used command /duty
[2025-07-25T23:36:17+0700] [Info] [CMD]: Aaron_Fang used command /asetfaction
[2025-07-25T23:36:21+0700] [Info] [connection] incoming connection: **************:37119 id: 1
[2025-07-25T23:36:21+0700] [Info] [join] Jenarrey has joined the server (1:**************)
[2025-07-25T23:36:27+0700] [Info] [CMD]: Aaron_Fang used command /asetfaction 0 1 1
[2025-07-25T23:36:30+0700] [Info] [CMD]: Aaron_Fang used command /r a
[2025-07-25T23:36:36+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:36:40+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 color
[2025-07-25T23:37:09+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 color f6203bff
[2025-07-25T23:37:11+0700] [Info] [CMD]: Aaron_Fang used command /r a
[2025-07-25T23:37:16+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 color f6203b
[2025-07-25T23:37:19+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 color f6203bbb
[2025-07-25T23:37:22+0700] [Info] [CMD]: Aaron_Fang used command /r a
[2025-07-25T23:37:26+0700] [Info] [CMD]: Aaron_Fang used command /duty
[2025-07-25T23:37:41+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 color f6203bff
[2025-07-25T23:37:46+0700] [Info] [CMD]: Aaron_Fang used command /goto hospital
[2025-07-25T23:37:53+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:37:57+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-25T23:38:09+0700] [Info] [CMD]: Aaron_Fang used command /editfaction maxranks 10
[2025-07-25T23:38:14+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-25T23:38:16+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 maxranks
[2025-07-25T23:38:21+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 maxranks 10
[2025-07-25T23:38:28+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:38:34+0700] [Info] [CMD]: Aaron_Fang used command /editfaction models
[2025-07-25T23:38:37+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:39:18+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:39:49+0700] [Info] [CMD]: Aaron_Fang used command /sendto 2 0
[2025-07-25T23:39:55+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:40:16+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:40:31+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:40:51+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:41:01+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:41:17+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:41:24+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 models
[2025-07-25T23:41:30+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:41:46+0700] [Info] [CMD]: Aaron_Fang used command /sendto 1 0
[2025-07-25T23:42:03+0700] [Info] [CMD]: Aaron_Fang used command /sendto 1 0
[2025-07-25T23:42:10+0700] [Info] [CMD]: Aaron_Fang used command /editfaction locker
[2025-07-25T23:42:16+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 locker
[2025-07-25T23:42:24+0700] [Info] [CMD]: Aaron_Fang used command /flocker
[2025-07-25T23:42:47+0700] [Info] [CMD]: Max_Jacob used command /flocker
[2025-07-25T23:42:49+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 locker
[2025-07-25T23:42:53+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:42:59+0700] [Info] [CMD]: Aaron_Fang used command /editfaction type
[2025-07-25T23:43:01+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 type
[2025-07-25T23:43:09+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 type 3
[2025-07-25T23:43:19+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 
[2025-07-25T23:43:26+0700] [Info] [CMD]: Aaron_Fang used command /editfaction ranks
[2025-07-25T23:43:34+0700] [Info] [CMD]: Aaron_Fang used command /editfaction ranks
[2025-07-25T23:43:43+0700] [Info] [CMD]: Aaron_Fang used command /editfaction ranks
[2025-07-25T23:43:46+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:43:52+0700] [Info] [CMD]: Aaron_Fang used command /finvite
[2025-07-25T23:43:56+0700] [Info] [CMD]: Aaron_Fang used command /asetfaction
[2025-07-25T23:43:59+0700] [Info] [CMD]: Aaron_Fang used command /setleader
[2025-07-25T23:44:08+0700] [Info] [CMD]: Aaron_Fang used command /setleader 1 1
[2025-07-25T23:44:15+0700] [Info] [CMD]: Aaron_Fang used command /flocker
[2025-07-25T23:44:20+0700] [Info] [CMD]: Aaron_Fang used command /flocker
[2025-07-25T23:44:40+0700] [Info] [CMD]: Max_Jacob used command /flocker
[2025-07-25T23:44:43+0700] [Info] [CMD]: Max_Jacob used command /flocker
[2025-07-25T23:44:57+0700] [Info] [CMD]: Max_Jacob used command /finvite
[2025-07-25T23:45:27+0700] [Info] [CMD]: Max_Jacob used command /acc
[2025-07-25T23:45:28+0700] [Info] [CMD]: Aaron_Fang used command /help
[2025-07-25T23:45:35+0700] [Info] [CMD]: Max_Jacob used command /acc
[2025-07-25T23:45:37+0700] [Info] [CMD]: Aaron_Fang used command /asetrank
[2025-07-25T23:45:41+0700] [Info] [CMD]: Aaron_Fang used command /frank
[2025-07-25T23:45:43+0700] [Info] [CMD]: Max_Jacob used command /or a
[2025-07-25T23:45:43+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-25T23:45:43+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-25T23:45:45+0700] [Info] [CMD]: Aaron_Fang used command /or a
[2025-07-25T23:45:45+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-25T23:45:46+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-25T23:45:46+0700] [Info] [CMD]: Max_Jacob used command /help
[2025-07-25T23:45:50+0700] [Info] [CMD]: Aaron_Fang used command /r a
[2025-07-25T23:45:53+0700] [Info] [CMD]: Max_Jacob used command /r a
[2025-07-25T23:46:05+0700] [Info] [CMD]: Max_Jacob used command /frank
[2025-07-25T23:46:05+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:46:08+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:46:12+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 name
[2025-07-25T23:46:14+0700] [Info] [CMD]: Aaron_Fang used command /ahelp
[2025-07-25T23:46:32+0700] [Info] [CMD]: Aaron_Fang used command /asetfaction
[2025-07-25T23:46:40+0700] [Info] [CMD]: Aaron_Fang used command /editfaction
[2025-07-25T23:46:44+0700] [Info] [CMD]: Aaron_Fang used command /editfaction 1 ranks
[2025-07-25T23:47:02+0700] [Info] [CMD]: Aaron_Fang used command /setleader
[2025-07-25T23:47:06+0700] [Info] [CMD]: Aaron_Fang used command /setleader 0 1
[2025-07-25T23:47:08+0700] [Info] [CMD]: Aaron_Fang used command /help
[2025-07-25T23:47:15+0700] [Info] [CMD]: Aaron_Fang used command /fac
[2025-07-25T23:47:16+0700] [Info] [CMD]: Aaron_Fang used command /faca
[2025-07-25T23:47:18+0700] [Info] [CMD]: Aaron_Fang used command /fac a
[2025-07-25T23:47:18+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-25T23:47:18+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-25T23:47:24+0700] [Info] [CMD]: Aaron_Fang used command /frank
[2025-07-25T23:47:30+0700] [Info] [CMD]: Aaron_Fang used command /dept
[2025-07-25T23:47:31+0700] [Info] [chat] [Aaron_Fang]:  a
[2025-07-25T23:47:33+0700] [Info] [CMD]: Aaron_Fang used command /dept a
[2025-07-25T23:47:33+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 30666175
[2025-07-25T23:47:33+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 30666175
[2025-07-25T23:47:33+0700] [Warning] Insufficient specifiers given to `format`: "[%s] [/dept] %s %s: %s" < 6456495
[2025-07-25T23:47:33+0700] [Info] [CMD]: Mark_Olrando used command /drink water
[2025-07-25T23:47:45+0700] [Info] [CMD]: Aaron_Fang used command /callsign
[2025-07-25T23:47:49+0700] [Info] [CMD]: Mark_Olrando used command /piss
[2025-07-25T23:48:06+0700] [Info] [CMD]: Max_Jacob used command /heal
[2025-07-25T23:48:09+0700] [Info] [CMD]: Max_Jacob used command /heal
[2025-07-25T23:48:12+0700] [Info] [CMD]: Max_Jacob used command /revive
[2025-07-25T23:48:14+0700] [Info] [CMD]: Aaron_Fang used command /callsign
[2025-07-25T23:48:15+0700] [Info] [CMD]: Max_Jacob used command /help
[2025-07-25T23:48:19+0700] [Info] [CMD]: Max_Jacob used command /loadinjured
[2025-07-25T23:48:23+0700] [Info] [CMD]: Max_Jacob used command /aheal
[2025-07-25T23:48:25+0700] [Info] [CMD]: Aaron_Fang used command /help
[2025-07-25T23:48:25+0700] [Info] [CMD]: Max_Jacob used command /heal
[2025-07-25T23:48:27+0700] [Info] [CMD]: Max_Jacob used command /revive
[2025-07-25T23:48:29+0700] [Info] [CMD]: Aaron_Fang used command /medicine
[2025-07-25T23:48:30+0700] [Info] [CMD]: Max_Jacob used command /b /heal
[2025-07-25T23:48:32+0700] [Info] [CMD]: Max_Jacob used command /bandage
[2025-07-25T23:48:32+0700] [Info] [CMD]: Aaron_Fang used command /heal
[2025-07-25T23:48:40+0700] [Info] [CMD]: Mark_Olrando used command /heal
[2025-07-25T23:48:43+0700] [Info] [CMD]: Aaron_Fang used command /loadinjured
[2025-07-25T23:48:44+0700] [Info] [CMD]: Max_Jacob used command /medicine
[2025-07-25T23:48:51+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 0
[2025-07-25T23:48:54+0700] [Info] [CMD]: Aaron_Fang used command /call
[2025-07-25T23:49:02+0700] [Info] [CMD]: Max_Jacob used command /call
[2025-07-25T23:49:03+0700] [Info] [CMD]: Max_Jacob used command /call 1
[2025-07-25T23:49:04+0700] [Info] [CMD]: Aaron_Fang used command /call 223
[2025-07-25T23:49:07+0700] [Info] [CMD]: Aaron_Fang used command /h
[2025-07-25T23:49:07+0700] [Info] [chat] [Max_Jacob]: 912
[2025-07-25T23:49:11+0700] [Info] [CMD]: Aaron_Fang used command /call 912
[2025-07-25T23:49:11+0700] [Info] [chat] [Max_Jacob]: 888
[2025-07-25T23:49:13+0700] [Info] [chat] [Max_Jacob]: 911
[2025-07-25T23:49:14+0700] [Info] [CMD]: Aaron_Fang used command /call 888
[2025-07-25T23:49:17+0700] [Info] [CMD]: Aaron_Fang used command /call 911
[2025-07-25T23:49:20+0700] [Info] [chat] [Aaron_Fang]: medics
[2025-07-25T23:49:25+0700] [Info] [CMD]: Aaron_Fang used command /h
[2025-07-25T23:49:30+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-25T23:49:42+0700] [Info] [CMD]: Aaron_Fang used command /atm
[2025-07-25T23:49:44+0700] [Info] [CMD]: Max_Jacob used command /heal
[2025-07-25T23:49:46+0700] [Info] [CMD]: Aaron_Fang used command /atm
[2025-07-25T23:49:47+0700] [Info] [CMD]: Max_Jacob used command /bandage 2
[2025-07-25T23:49:50+0700] [Info] [CMD]: Aaron_Fang used command /atm
[2025-07-25T23:49:52+0700] [Info] [CMD]: Max_Jacob used command /bomb 1
[2025-07-25T23:49:59+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-25T23:50:25+0700] [Info] [CMD]: Aaron_Fang used command /mysalary
[2025-07-25T23:50:37+0700] [Info] [CMD]: Max_Jacob used command /salary
[2025-07-25T23:50:41+0700] [Info] [CMD]: Max_Jacob used command /mysalary
[2025-07-25T23:50:45+0700] [Info] [CMD]: Max_Jacob used command /time
[2025-07-25T23:50:49+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 2
[2025-07-25T23:50:56+0700] [Info] [CMD]: Max_Jacob used command /help
[2025-07-25T23:51:02+0700] [Info] [CMD]: Max_Jacob used command /fac
[2025-07-25T23:51:05+0700] [Info] [CMD]: Max_Jacob used command /fac a
[2025-07-25T23:51:05+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-25T23:51:05+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-25T23:51:10+0700] [Info] [CMD]: Max_Jacob used command /setmedicine
[2025-07-25T23:51:34+0700] [Info] [CMD]: Max_Jacob used command /createpharmacy
[2025-07-25T23:51:37+0700] [Info] [CMD]: Max_Jacob used command /b /createpharmacy
[2025-07-25T23:51:42+0700] [Info] [CMD]: Aaron_Fang used command /createpharmacy
[2025-07-25T23:51:47+0700] [Info] [CMD]: Max_Jacob used command /medicine
[2025-07-25T23:51:50+0700] [Info] [CMD]: Aaron_Fang used command /medicine
[2025-07-25T23:51:57+0700] [Info] [CMD]: Mark_Olrando used command /createpharmacy
[2025-07-25T23:51:57+0700] [Info] [CMD]: Aaron_Fang used command /medicine
[2025-07-25T23:51:58+0700] [Info] [CMD]: Max_Jacob used command /medicine
[2025-07-25T23:52:01+0700] [Info] [CMD]: Max_Jacob used command /medicine
[2025-07-25T23:52:02+0700] [Info] [CMD]: Mark_Olrando used command /createpharmacy
[2025-07-25T23:52:05+0700] [Info] [CMD]: Mark_Olrando used command /createpharmacy
[2025-07-25T23:52:07+0700] [Info] [CMD]: Aaron_Fang used command /medicine
[2025-07-25T23:52:07+0700] [Info] [CMD]: Mark_Olrando used command /createpharmacy
[2025-07-25T23:52:10+0700] [Info] [CMD]: Mark_Olrando used command /createpharmacy
[2025-07-25T23:52:11+0700] [Info] [CMD]: Aaron_Fang used command /medicine
[2025-07-25T23:52:12+0700] [Info] [CMD]: Max_Jacob used command /phone
[2025-07-25T23:52:13+0700] [Info] [CMD]: Mark_Olrando used command /createpharmacy
[2025-07-25T23:52:17+0700] [Info] [CMD]: Max_Jacob used command /surgery
[2025-07-25T23:52:20+0700] [Info] [CMD]: Max_Jacob used command /givemedicine
[2025-07-25T23:52:23+0700] [Info] [CMD]: Max_Jacob used command /medicine
[2025-07-25T23:52:26+0700] [Info] [CMD]: Max_Jacob used command /pharmacy
[2025-07-25T23:52:27+0700] [Info] [CMD]: Aaron_Fang used command /give
[2025-07-25T23:52:30+0700] [Info] [CMD]: Mark_Olrando used command /phone
[2025-07-25T23:52:30+0700] [Info] [CMD]: Max_Jacob used command /health
[2025-07-25T23:52:31+0700] [Info] [CMD]: Mark_Olrando used command /phone
[2025-07-25T23:52:34+0700] [Info] [CMD]: Mark_Olrando used command /phone
[2025-07-25T23:52:38+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has given %d %s to %s (%s)." < 6456495
[2025-07-25T23:53:16+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-25T23:59:02+0700] [Info] [CMD]: Mark_Olrando used command /flocker to access the locker
[2025-07-25T23:59:07+0700] [Info] [CMD]: Mark_Olrando used command /flocker to access the locker
[2025-07-25T23:59:12+0700] [Info] [part] Max_Jacob has left the server (1:1)
[2025-07-26T00:04:51+0700] [Info] [CMD]: Mark_Olrando used command /atm
[2025-07-26T00:06:53+0700] [Info] [CMD]: Mark_Olrando used command /atm
[2025-07-26T00:06:59+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-26T00:07:22+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-26T00:08:32+0700] [Info] [CMD]: Aaron_Fang used command /drink water
[2025-07-26T00:09:37+0700] [Info] [CMD]: Aaron_Fang used command /piss
[2025-07-26T00:09:54+0700] [Info] [CMD]: Aaron_Fang used command /flocker
[2025-07-26T00:10:05+0700] [Info] [CMD]: Aaron_Fang used command /flocker
[2025-07-26T00:11:25+0700] [Info] [part] Aaron_Fang has left the server (0:1)
[2025-07-26T00:11:27+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-26T00:11:27+0700] [Info] [part] Mark_Olrando has left the server (2:1)
[2025-07-26T11:40:49+0700] [Info] [connection] incoming connection: *************:57175 id: 0
[2025-07-26T11:40:49+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-26T11:41:07+0700] [Info] [CMD]: Aaron_Fang used command /f a
[2025-07-26T11:41:07+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-26T11:41:07+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-26T11:41:12+0700] [Info] [CMD]: Aaron_Fang used command /or a
[2025-07-26T11:41:12+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-26T11:41:12+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-26T11:41:15+0700] [Info] [CMD]: Aaron_Fang used command /f a
[2025-07-26T11:41:15+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-26T11:41:15+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-26T11:41:44+0700] [Info] [part] Aaron_Fang has left the server (0:1)
[2025-07-26T11:43:34+0700] [Info] [connection] incoming connection: **************:61479 id: 0
[2025-07-26T11:43:34+0700] [Info] [join] Jenarrey has joined the server (0:**************)
[2025-07-26T11:44:56+0700] [Info] [CMD]: Max_Jacob used command /stats
[2025-07-26T11:57:51+0700] [Info] [CMD]: Max_Jacob used command /inspect 0
[2025-07-26T11:57:59+0700] [Info] [CMD]: Max_Jacob used command /approve inspect
[2025-07-26T11:58:25+0700] [Info] [part] Max_Jacob has left the server (0:1)
[2025-07-26T12:04:52+0700] [Info] [connection] incoming connection: **************:58720 id: 0
[2025-07-26T12:04:53+0700] [Info] [join] KakZah has joined the server (0:**************)
[2025-07-26T12:05:15+0700] [Info] [connection] incoming connection: **************:64017 id: 1
[2025-07-26T12:05:15+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-26T12:05:33+0700] [Info] [CMD]: Jack_Heversont used command /goto 1
[2025-07-26T12:05:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-26T12:05:48+0700] [Info] [CMD]: Jack_Heversont used command /setvip
[2025-07-26T12:05:52+0700] [Info] [CMD]: Jack_Heversont used command /setvip 1 easly 1
[2025-07-26T12:05:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-26T12:05:59+0700] [Info] [CMD]: Jack_Heversont used command /giveitem
[2025-07-26T12:06:01+0700] [Info] [CMD]: Jack_Heversont used command /giveitem 1
[2025-07-26T12:06:14+0700] [Info] [CMD]: Jack_Heversont used command /giveitem 1
[2025-07-26T12:06:18+0700] [Info] [connection] incoming connection: **************:63587 id: 2
[2025-07-26T12:06:18+0700] [Info] [join] Jenarrey has joined the server (2:**************)
[2025-07-26T12:06:22+0700] [Info] [CMD]: Jack_Heversont used command /giveitem 1
[2025-07-26T12:06:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-26T12:07:14+0700] [Info] [CMD]: Jack_Heversont used command /pay 1 100
[2025-07-26T12:07:14+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-26T12:07:17+0700] [Info] [CMD]: Jack_Heversont used command /giveitem 1
[2025-07-26T12:07:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /o hallo bang Max
[2025-07-26T12:07:24+0700] [Info] [CMD]: Jack_Heversont used command /giveitem 1
[2025-07-26T12:07:27+0700] [Info] [CMD]: Jack_Heversont used command /giveitem 1
[2025-07-26T12:07:30+0700] [Info] [CMD]: Jack_Heversont used command /giveitem 1
[2025-07-26T12:07:34+0700] [Info] [CMD]: Jack_Heversont used command /giveitem 1
[2025-07-26T12:07:37+0700] [Info] [CMD]: Max_Jacob used command /o hallo
[2025-07-26T12:07:41+0700] [Info] [CMD]: Jack_Heversont used command /givecoin
[2025-07-26T12:07:41+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-26T12:07:47+0700] [Info] [CMD]: Jack_Heversont used command /agive
[2025-07-26T12:07:59+0700] [Info] [CMD]: Jack_Heversont used command /agive 1 vipcoin 300
[2025-07-26T12:08:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /o abang bang Fazar ya?
[2025-07-26T12:08:14+0700] [Info] [CMD]: Max_Jacob used command /o iyaa
[2025-07-26T12:08:17+0700] [Info] [CMD]: Jack_Heversont used command /agive 1 numberpohine
[2025-07-26T12:08:21+0700] [Info] [CMD]: Jack_Heversont used command /agive 1 numberphone
[2025-07-26T12:08:28+0700] [Info] [CMD]: Jack_Heversont used command /agive 1 numberphone 1234
[2025-07-26T12:09:07+0700] [Info] [CMD]: Jack_Heversont used command /properties 1
[2025-07-26T12:09:17+0700] [Info] [CMD]: Jack_Heversont used command /gotohouse 2
[2025-07-26T12:09:19+0700] [Info] [CMD]: Jack_Heversont used command /goto house 2
[2025-07-26T12:09:24+0700] [Info] [CMD]: Jack_Heversont used command /creategate
[2025-07-26T12:09:24+0700] [Info] [Gate_Create] New gate created with ID: 18
[2025-07-26T12:09:24+0700] [Info] [Gate_Save] Saving gate 10 (SQL ID 18): Pos=(2012.4229, -1737.6563, 13.5468)
[2025-07-26T12:09:24+0700] [Info] [Gate Save] Gate ID 10 (SQL ID 18) save result: 1 rows affected
[2025-07-26T12:09:31+0700] [Info] [CMD]: Jack_Heversont used command /editgate
[2025-07-26T12:09:36+0700] [Info] [CMD]: Jack_Heversont used command /editgate 10 model 988
[2025-07-26T12:09:36+0700] [Info] [Gate_Save] Saving gate 10 (SQL ID 18): Pos=(2012.4229, -1737.6563, 13.5468)
[2025-07-26T12:09:36+0700] [Info] [Gate Save] Gate ID 10 (SQL ID 18) save result: 1 rows affected
[2025-07-26T12:09:49+0700] [Info] [CMD]: Jack_Heversont used command /editgate 10 pos
[2025-07-26T12:09:55+0700] [Info] [Gate] Setting PVars for player 0 - Gate ID: 18, X: 2011.7280, Y: -1738.4111, Z: 13.5468
[2025-07-26T12:09:55+0700] [Info] [GATE DEBUG] Player KakZah (0) saving gate ID 10 (SQL: 18)
[2025-07-26T12:09:55+0700] [Info] [GATE DEBUG] New position: X=2011.7280 Y=-1738.4111 Z=13.5468 RX=0.0000 RY=0.0000 RZ=271.9352
[2025-07-26T12:09:55+0700] [Info] [Gate] Object recreated for gate ID: 18 with position: 2011.7280, -1738.4111, 13.5468
[2025-07-26T12:10:00+0700] [Info] [CMD]: Jack_Heversont used command /editgate 10 move
[2025-07-26T12:10:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-26T12:10:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /o bang engga duty PD
[2025-07-26T12:10:21+0700] [Info] [Gate] Setting PVars for player 0 - Gate ID: 18, X: 2015.4726, Y: -1742.2836, Z: 13.5468
[2025-07-26T12:10:22+0700] [Info] [GATE DEBUG] Player KakZah (0) saving gate ID 10 (SQL: 18)
[2025-07-26T12:10:22+0700] [Info] [GATE DEBUG] New position: X=2015.4726 Y=-1742.2836 Z=13.5468 RX=0.0000 RY=0.0000 RZ=0.4352
[2025-07-26T12:10:23+0700] [Info] [CMD]: Jack_Heversont used command /open
[2025-07-26T12:10:24+0700] [Info] [CMD]: Jack_Heversont used command /open
[2025-07-26T12:10:29+0700] [Info] [CMD]: Jack_Heversont used command /editgate 10 speed 1
[2025-07-26T12:10:29+0700] [Info] [Gate_Save] Saving gate 10 (SQL ID 18): Pos=(2011.7280, -1738.4111, 13.5468)
[2025-07-26T12:10:29+0700] [Info] [Gate Save] Gate ID 10 (SQL ID 18) save result: 1 rows affected
[2025-07-26T12:10:29+0700] [Info] [CMD]: Jack_Heversont used command /open
[2025-07-26T12:10:31+0700] [Info] [CMD]: Jack_Heversont used command /open
[2025-07-26T12:10:33+0700] [Info] [CMD]: Jack_Heversont used command /editgate 10 speed 1.5
[2025-07-26T12:10:33+0700] [Info] [Gate_Save] Saving gate 10 (SQL ID 18): Pos=(2011.7280, -1738.4111, 13.5468)
[2025-07-26T12:10:33+0700] [Info] [Gate Save] Gate ID 10 (SQL ID 18) save result: 1 rows affected
[2025-07-26T12:10:33+0700] [Info] [CMD]: Jack_Heversont used command /open
[2025-07-26T12:10:37+0700] [Info] [CMD]: Jack_Heversont used command /open
[2025-07-26T12:10:43+0700] [Info] [CMD]: Jack_Heversont used command /editgate 10 
[2025-07-26T12:10:46+0700] [Info] [CMD]: Jack_Heversont used command /editgate 10 pass
[2025-07-26T12:10:57+0700] [Info] [CMD]: Jack_Heversont used command /editgate 10 pass nando123
[2025-07-26T12:10:57+0700] [Info] [Gate_Save] Saving gate 10 (SQL ID 18): Pos=(2011.7280, -1738.4111, 13.5468)
[2025-07-26T12:10:57+0700] [Info] [Gate Save] Gate ID 10 (SQL ID 18) save result: 1 rows affected
[2025-07-26T12:10:59+0700] [Info] [CMD]: Jack_Heversont used command /open
[2025-07-26T12:11:03+0700] [Info] [CMD]: Jack_Heversont used command /open
[2025-07-26T12:11:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-26T12:11:13+0700] [Info] [part] Jack_Heversont has left the server (0:1)
[2025-07-26T12:11:31+0700] [Info] [CMD]: Max_Jacob used command /o engga
[2025-07-26T12:11:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /o masi Newbie ni
[2025-07-26T12:11:54+0700] [Info] [connection] incoming connection: **************:57862 id: 0
[2025-07-26T12:11:54+0700] [Info] [join] Aldo has joined the server (0:**************)
[2025-07-26T12:12:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /inventory
[2025-07-26T12:12:29+0700] [Info] [sv:dbg:network:connect] : connecting player (3) with address (*************) ...
[2025-07-26T12:12:29+0700] [Info] [sv:dbg:network:connect] : player (3) assigned key (1e23ba671dedc5d2)
[2025-07-26T12:12:29+0700] [Info] [connection] incoming connection: *************:55316 id: 3
[2025-07-26T12:12:29+0700] [Info] [join] dflx has joined the server (3:*************)
[2025-07-26T12:12:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /boombox place
[2025-07-26T12:13:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /boombox place
[2025-07-26T12:13:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /boombox
[2025-07-26T12:13:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /palve
[2025-07-26T12:13:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /palace
[2025-07-26T12:13:32+0700] [Info] [chat] [Kenzo_Wazoski]: place
[2025-07-26T12:13:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /place
[2025-07-26T12:13:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /pickup
[2025-07-26T12:13:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /o oi
[2025-07-26T12:13:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-26T12:14:01+0700] [Info] [chat] [Kenzo_Wazoski]: place
[2025-07-26T12:14:01+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T12:14:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /boombox
[2025-07-26T12:14:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /url
[2025-07-26T12:14:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /url
[2025-07-26T12:14:29+0700] [Info] [CMD]: Max_Jacob used command /stats
[2025-07-26T12:14:36+0700] [Info] [CMD]: Max_Jacob used command /levels
[2025-07-26T12:14:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /pickup
[2025-07-26T12:14:41+0700] [Info] [CMD]: Kenzo_Wazoski used command /pickup
[2025-07-26T12:14:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /pickup
[2025-07-26T12:14:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /place
[2025-07-26T12:14:55+0700] [Info] [CMD]: Aaron_Fang used command /o oi
[2025-07-26T12:14:55+0700] [Info] [CMD]: Max_Jacob used command /piss
[2025-07-26T12:15:05+0700] [Info] [CMD]: Aaron_Fang used command /o bentar relogw
[2025-07-26T12:15:06+0700] [Info] [sv:dbg:network:connect] : disconnecting player (3) ...
[2025-07-26T12:15:06+0700] [Info] [part] Aaron_Fang has left the server (3:1)
[2025-07-26T12:15:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /o bang ke gunaan nya Boombox ap
[2025-07-26T12:15:16+0700] [Info] [CMD]: Max_Jacob used command /o buat musik
[2025-07-26T12:15:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /o cara ambil nya gimana
[2025-07-26T12:15:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /pickup
[2025-07-26T12:15:40+0700] [Info] [CMD]: Max_Jacob used command /bombbox
[2025-07-26T12:15:41+0700] [Info] [CMD]: Kenzo_Wazoski used command /boombox
[2025-07-26T12:15:43+0700] [Info] [CMD]: Max_Jacob used command /boombox
[2025-07-26T12:15:48+0700] [Info] [CMD]: Max_Jacob used command /o /boombox
[2025-07-26T12:15:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /boombox place
[2025-07-26T12:16:00+0700] [Info] [CMD]: Max_Jacob used command /stats
[2025-07-26T12:16:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /boombox pickup
[2025-07-26T12:16:07+0700] [Info] [CMD]: Max_Jacob used command /getcar
[2025-07-26T12:16:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /o uda uda bang
[2025-07-26T12:16:15+0700] [Info] [CMD]: Max_Jacob used command /drink water
[2025-07-26T12:16:18+0700] [Info] [CMD]: Max_Jacob used command /drink water
[2025-07-26T12:16:23+0700] [Info] [CMD]: Max_Jacob used command /factions
[2025-07-26T12:16:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /o bang kan aku uda VIP terus buat /o nya jadi berwana gimana
[2025-07-26T12:16:43+0700] [Info] [CMD]: Max_Jacob used command /customrank
[2025-07-26T12:16:46+0700] [Info] [CMD]: Max_Jacob used command /customooc
[2025-07-26T12:16:49+0700] [Info] [CMD]: Max_Jacob used command /costumrank
[2025-07-26T12:16:52+0700] [Info] [CMD]: Max_Jacob used command /customooc
[2025-07-26T12:16:55+0700] [Info] [CMD]: Max_Jacob used command /costumooc
[2025-07-26T12:16:58+0700] [Info] [CMD]: Max_Jacob used command /setrank
[2025-07-26T12:17:00+0700] [Info] [CMD]: Max_Jacob used command /setooc
[2025-07-26T12:17:04+0700] [Info] [CMD]: Max_Jacob used command /changerank
[2025-07-26T12:17:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /o
[2025-07-26T12:17:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-26T12:17:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-26T12:17:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-26T12:18:29+0700] [Info] [CMD]: Max_Jacob used command /customrank
[2025-07-26T12:18:30+0700] [Info] [CMD]: Max_Jacob used command /o gatau
[2025-07-26T12:18:39+0700] [Info] [CMD]: Max_Jacob used command /s
[2025-07-26T12:18:41+0700] [Info] [CMD]: Max_Jacob used command /raports
[2025-07-26T12:18:43+0700] [Info] [CMD]: Max_Jacob used command /ar
[2025-07-26T12:18:45+0700] [Info] [CMD]: Max_Jacob used command /dr
[2025-07-26T12:18:49+0700] [Info] [CMD]: Max_Jacob used command /kick
[2025-07-26T12:18:50+0700] [Info] [CMD]: Max_Jacob used command /slap
[2025-07-26T12:18:52+0700] [Info] [CMD]: Max_Jacob used command /slap
[2025-07-26T12:18:54+0700] [Info] [CMD]: Max_Jacob used command /permissions
[2025-07-26T12:18:56+0700] [Info] [CMD]: Max_Jacob used command /permission
[2025-07-26T12:18:59+0700] [Info] [CMD]: Max_Jacob used command /setpermissions
[2025-07-26T12:19:02+0700] [Info] [CMD]: Max_Jacob used command /editpermissions
[2025-07-26T12:19:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /o bang kata nya Mark Jam berapa Interview nya
[2025-07-26T12:19:09+0700] [Info] [CMD]: Max_Jacob used command /reports
[2025-07-26T12:19:11+0700] [Info] [CMD]: Max_Jacob used command /ad
[2025-07-26T12:20:11+0700] [Info] [CMD]: Max_Jacob used command /asks
[2025-07-26T12:20:17+0700] [Info] [CMD]: Max_Jacob used command /fc
[2025-07-26T12:20:28+0700] [Info] [AFK] Player Mark_Olrando (ID: 0) masuk mode AFK
[2025-07-26T12:21:09+0700] [Info] [CMD]: Mark_Olrando used command /afk K57weo
[2025-07-26T12:21:13+0700] [Info] [CMD]: Mark_Olrando used command /afk K57we0
[2025-07-26T12:21:13+0700] [Info] [AFK] Player Mark_Olrando (ID: 0) keluar dari mode AFK
[2025-07-26T12:23:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-26T12:24:00+0700] [Info] [part] Kenzo_Wazoski has left the server (1:0)
[2025-07-26T12:25:45+0700] [Info] [part] Max_Jacob has left the server (2:1)
[2025-07-26T12:26:15+0700] [Info] [AFK] Player Mark_Olrando (ID: 0) masuk mode AFK
[2025-07-26T12:26:55+0700] [Info] [AFK] Player Mark_Olrando (ID: 0) disconnect saat AFK - data telah di-restore
[2025-07-26T12:26:56+0700] [Info] [part] Mark_Olrando has left the server (0:1)
[2025-07-26T12:34:20+0700] [Info] [connection] incoming connection: **************:56367 id: 0
[2025-07-26T12:34:20+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-26T12:34:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-26T12:34:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-26T12:35:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-26T12:35:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-26T12:35:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /usecoin
[2025-07-26T12:35:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-26T12:35:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /Srp coin
[2025-07-26T12:35:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-26T12:36:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /faq
[2025-07-26T12:36:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-26T12:36:53+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-26T12:50:49+0700] [Info] [connection] incoming connection: **************:62596 id: 0
[2025-07-26T12:50:49+0700] [Info] [join] Aldo has joined the server (0:**************)
[2025-07-26T12:52:42+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-26T12:53:01+0700] [Info] [CMD]: Mark_Olrando used command /lay 1
[2025-07-26T12:53:09+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-26T12:53:14+0700] [Info] [CMD]: Mark_Olrando used command /lay 1
[2025-07-26T12:53:16+0700] [Info] [CMD]: Mark_Olrando used command /lay 2
[2025-07-26T12:53:20+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-26T12:53:29+0700] [Info] [CMD]: Mark_Olrando used command /lay 4
[2025-07-26T12:53:32+0700] [Info] [CMD]: Mark_Olrando used command /lay 5
[2025-07-26T12:53:39+0700] [Info] [CMD]: Mark_Olrando used command /lay 3
[2025-07-26T12:54:11+0700] [Info] [CMD]: Mark_Olrando used command /drink water
[2025-07-26T12:54:23+0700] [Info] [CMD]: Mark_Olrando used command /piss
[2025-07-26T12:59:32+0700] [Info] [AFK] Player Mark_Olrando (ID: 0) masuk mode AFK
[2025-07-26T13:00:27+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (*************) ...
[2025-07-26T13:00:28+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (1e23ba6739847304)
[2025-07-26T13:00:28+0700] [Info] [connection] incoming connection: *************:64933 id: 1
[2025-07-26T13:00:28+0700] [Info] [join] dflx has joined the server (1:*************)
[2025-07-26T13:03:18+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T13:03:21+0700] [Info] [CMD]: Aaron_Fang used command /getcar 15
[2025-07-26T13:03:25+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T13:03:37+0700] [Info] [CMD]: Aaron_Fang used command /getcar 0
[2025-07-26T13:03:41+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T13:03:44+0700] [Info] [CMD]: Aaron_Fang used command /getcar 17
[2025-07-26T13:04:26+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-26T13:04:26+0700] [Info] [part] Aaron_Fang has left the server (1:1)
[2025-07-26T13:04:39+0700] [Info] [CMD]: Mark_Olrando used command /afk 24NSPT
[2025-07-26T13:04:39+0700] [Info] [AFK] Player Mark_Olrando (ID: 0) keluar dari mode AFK
[2025-07-26T13:10:05+0700] [Info] [AFK] Player Mark_Olrando (ID: 0) masuk mode AFK
[2025-07-26T13:13:17+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (*************) ...
[2025-07-26T13:13:17+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (1e23ba67c377e1c0)
[2025-07-26T13:13:17+0700] [Info] [connection] incoming connection: *************:58349 id: 1
[2025-07-26T13:13:17+0700] [Info] [join] dflx has joined the server (1:*************)
[2025-07-26T13:15:28+0700] [Info] [AFK] Player Mark_Olrando (ID: 0) disconnect saat AFK - data telah di-restore
[2025-07-26T13:15:28+0700] [Info] [part] Mark_Olrando has left the server (0:1)
[2025-07-26T13:18:03+0700] [Info] [CMD]: Aaron_Fang used command /arepair
[2025-07-26T13:18:20+0700] [Info] [CMD]: Aaron_Fang used command /setwheater
[2025-07-26T13:18:25+0700] [Info] [CMD]: Aaron_Fang used command /setweather
[2025-07-26T13:18:27+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T13:18:28+0700] [Info] [CMD]: Aaron_Fang used command /setweather 2
[2025-07-26T13:18:29+0700] [Info] [CMD]: Aaron_Fang used command /setweather 3
[2025-07-26T13:18:30+0700] [Info] [CMD]: Aaron_Fang used command /setweather 4
[2025-07-26T13:18:32+0700] [Info] [CMD]: Aaron_Fang used command /setweather 5
[2025-07-26T13:18:35+0700] [Info] [CMD]: Aaron_Fang used command /setweather 6
[2025-07-26T13:18:36+0700] [Info] [CMD]: Aaron_Fang used command /setweather 7
[2025-07-26T13:18:38+0700] [Info] [CMD]: Aaron_Fang used command /setweather 8
[2025-07-26T13:18:41+0700] [Info] [CMD]: Aaron_Fang used command /setweather 9
[2025-07-26T13:18:44+0700] [Info] [CMD]: Aaron_Fang used command /setweather 10
[2025-07-26T13:18:46+0700] [Info] [CMD]: Aaron_Fang used command /setweather 11
[2025-07-26T13:18:48+0700] [Info] [CMD]: Aaron_Fang used command /setweather 12
[2025-07-26T13:18:50+0700] [Info] [CMD]: Aaron_Fang used command /setweather 13
[2025-07-26T13:18:51+0700] [Info] [CMD]: Aaron_Fang used command /setweather 14
[2025-07-26T13:18:53+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T13:19:29+0700] [Info] [CMD]: Aaron_Fang used command /fps
[2025-07-26T13:19:31+0700] [Info] [CMD]: Aaron_Fang used command /fps
[2025-07-26T13:29:00+0700] [Info] [CMD]: Aaron_Fang used command /givecar
[2025-07-26T13:29:55+0700] [Info] [CMD]: Aaron_Fang used command /givecar 1 560
[2025-07-26T13:30:08+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob
[2025-07-26T13:30:11+0700] [Info] [CMD]: Aaron_Fang used command /ahelp
[2025-07-26T13:30:14+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob
[2025-07-26T13:30:18+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 1
[2025-07-26T13:30:20+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 2
[2025-07-26T13:30:22+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 3
[2025-07-26T13:30:24+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 4
[2025-07-26T13:30:26+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 5
[2025-07-26T13:30:28+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 6
[2025-07-26T13:30:33+0700] [Info] [CMD]: Aaron_Fang used command /destroycar
[2025-07-26T13:30:36+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T13:30:53+0700] [Info] [CMD]: Aaron_Fang used command /givecar 1 562
[2025-07-26T13:31:07+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob
[2025-07-26T13:31:11+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob
[2025-07-26T13:31:14+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 2
[2025-07-26T13:31:16+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 3
[2025-07-26T13:31:18+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 1
[2025-07-26T13:31:37+0700] [Info] [CMD]: Aaron_Fang used command /acolor
[2025-07-26T13:31:41+0700] [Info] [CMD]: Aaron_Fang used command /acolour
[2025-07-26T13:31:52+0700] [Info] [CMD]: Aaron_Fang used command /ahelp
[2025-07-26T13:32:20+0700] [Info] [CMD]: Aaron_Fang used command /atune
[2025-07-26T13:32:31+0700] [Info] [CMD]: Aaron_Fang used command /atune
[2025-07-26T13:32:33+0700] [Info] [connection] incoming connection: *************:60398 id: 0
[2025-07-26T13:32:33+0700] [Info] [join] Jenarrey has joined the server (0:*************)
[2025-07-26T13:32:41+0700] [Info] [CMD]: Aaron_Fang used command /atune
[2025-07-26T13:32:45+0700] [Info] [CMD]: Aaron_Fang used command /atune
[2025-07-26T13:32:56+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar
[2025-07-26T13:32:59+0700] [Info] [CMD]: Max_Jacob used command /veh
[2025-07-26T13:33:09+0700] [Info] [CMD]: Max_Jacob used command /a a
[2025-07-26T13:33:12+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 1 2
[2025-07-26T13:34:43+0700] [Info] [CMD]: Aaron_Fang used command /sendto 1 0
[2025-07-26T13:34:47+0700] [Info] [CMD]: Max_Jacob used command /gethere
[2025-07-26T13:34:48+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T13:34:49+0700] [Info] [CMD]: Max_Jacob used command /get
[2025-07-26T13:34:53+0700] [Info] [CMD]: Aaron_Fang used command /gotocar 14
[2025-07-26T13:34:53+0700] [Info] [CMD]: Max_Jacob used command /sendhere
[2025-07-26T13:34:57+0700] [Info] [CMD]: Max_Jacob used command /getid
[2025-07-26T13:35:00+0700] [Info] [CMD]: Max_Jacob used command /getid
[2025-07-26T13:35:00+0700] [Info] [CMD]: Aaron_Fang used command /sendto 0 1
[2025-07-26T13:35:03+0700] [Info] [CMD]: Aaron_Fang used command /refill 1
[2025-07-26T13:35:23+0700] [Info] [CMD]: Max_Jacob used command /agethere
[2025-07-26T13:35:25+0700] [Info] [CMD]: Max_Jacob used command /gethere
[2025-07-26T13:35:27+0700] [Info] [CMD]: Max_Jacob used command /getid
[2025-07-26T13:35:31+0700] [Info] [CMD]: Max_Jacob used command /sendid
[2025-07-26T13:36:06+0700] [Info] [CMD]: Aaron_Fang used command /atune
[2025-07-26T13:36:14+0700] [Info] [CMD]: Aaron_Fang used command /atune
[2025-07-26T13:36:19+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob
[2025-07-26T13:36:22+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 1
[2025-07-26T13:36:22+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 2
[2025-07-26T13:36:24+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 3
[2025-07-26T13:36:29+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 2
[2025-07-26T13:36:41+0700] [Info] [CMD]: Max_Jacob used command /bring
[2025-07-26T13:36:50+0700] [Info] [CMD]: Max_Jacob used command /b /bring
[2025-07-26T13:36:59+0700] [Info] [CMD]: Aaron_Fang used command /bring
[2025-07-26T13:37:02+0700] [Info] [CMD]: Aaron_Fang used command /bring 0
[2025-07-26T13:38:24+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-26T13:38:24+0700] [Info] [part] Aaron_Fang has left the server (1:1)
[2025-07-26T13:40:47+0700] [Info] [part] Max_Jacob has left the server (0:1)
[2025-07-26T13:43:54+0700] [Info] [connection] incoming connection: *************:60408 id: 0
[2025-07-26T13:43:54+0700] [Info] [join] Jenarrey has joined the server (0:*************)
[2025-07-26T13:44:12+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (*************) ...
[2025-07-26T13:44:12+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (1e23ba673d407c67)
[2025-07-26T13:44:12+0700] [Info] [connection] incoming connection: *************:54626 id: 1
[2025-07-26T13:44:12+0700] [Info] [join] dflx has joined the server (1:*************)
[2025-07-26T13:45:04+0700] [Info] [CMD]: Max_Jacob used command /reports
[2025-07-26T13:45:06+0700] [Info] [CMD]: Max_Jacob used command /asks
[2025-07-26T13:45:09+0700] [Info] [CMD]: Aaron_Fang used command /setwheater 1
[2025-07-26T13:45:12+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T13:45:14+0700] [Info] [CMD]: Aaron_Fang used command /setweather 4
[2025-07-26T13:45:15+0700] [Info] [CMD]: Aaron_Fang used command /setweather 5
[2025-07-26T13:45:16+0700] [Info] [CMD]: Aaron_Fang used command /setweather 3
[2025-07-26T13:45:18+0700] [Info] [CMD]: Aaron_Fang used command /setweather 2
[2025-07-26T13:45:20+0700] [Info] [CMD]: Aaron_Fang used command /setweather 7
[2025-07-26T13:45:22+0700] [Info] [CMD]: Aaron_Fang used command /setweather 8
[2025-07-26T13:45:24+0700] [Info] [CMD]: Aaron_Fang used command /setweather 9
[2025-07-26T13:45:27+0700] [Info] [CMD]: Aaron_Fang used command /setweather 8
[2025-07-26T13:45:36+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-26T13:45:36+0700] [Info] [part] Aaron_Fang has left the server (1:1)
[2025-07-26T13:47:09+0700] [Info] [part] Max_Jacob has left the server (0:0)
[2025-07-26T13:52:51+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*************) ...
[2025-07-26T13:52:51+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (1e23ba6787fe8dfc)
[2025-07-26T13:52:51+0700] [Info] [connection] incoming connection: *************:59086 id: 0
[2025-07-26T13:52:51+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-26T13:53:04+0700] [Info] [CMD]: Aaron_Fang used command /setwather 1
[2025-07-26T13:53:08+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T13:53:10+0700] [Info] [CMD]: Aaron_Fang used command /setweather 2
[2025-07-26T13:56:03+0700] [Info] [CMD]: Aaron_Fang used command /setweather 3
[2025-07-26T13:56:04+0700] [Info] [CMD]: Aaron_Fang used command /setweather 2
[2025-07-26T13:56:06+0700] [Info] [CMD]: Aaron_Fang used command /setweather 5
[2025-07-26T13:56:08+0700] [Info] [CMD]: Aaron_Fang used command /setweather 4
[2025-07-26T13:56:10+0700] [Info] [CMD]: Aaron_Fang used command /setweather 7
[2025-07-26T13:56:12+0700] [Info] [chat] [Aaron_Fang]: 8
[2025-07-26T13:56:16+0700] [Info] [CMD]: Aaron_Fang used command /setweather 8
[2025-07-26T13:56:19+0700] [Info] [CMD]: Aaron_Fang used command /setweather 9
[2025-07-26T13:56:20+0700] [Info] [CMD]: Aaron_Fang used command /setweather 10
[2025-07-26T13:56:22+0700] [Info] [CMD]: Aaron_Fang used command /setweather 11
[2025-07-26T13:56:23+0700] [Info] [CMD]: Aaron_Fang used command /setweather 12
[2025-07-26T13:56:25+0700] [Info] [CMD]: Aaron_Fang used command /setweather 13
[2025-07-26T13:56:26+0700] [Info] [chat] [Aaron_Fang]: 4
[2025-07-26T13:56:30+0700] [Info] [CMD]: Aaron_Fang used command /setweather 14
[2025-07-26T13:56:31+0700] [Info] [CMD]: Aaron_Fang used command /setweather 16
[2025-07-26T13:56:34+0700] [Info] [CMD]: Aaron_Fang used command /setweather 16
[2025-07-26T13:56:36+0700] [Info] [CMD]: Aaron_Fang used command /setweather 17
[2025-07-26T13:56:37+0700] [Info] [CMD]: Aaron_Fang used command /setweather 18
[2025-07-26T13:56:39+0700] [Info] [CMD]: Aaron_Fang used command /setweather 19
[2025-07-26T13:56:43+0700] [Info] [CMD]: Aaron_Fang used command /setweather 20
[2025-07-26T13:56:45+0700] [Info] [CMD]: Aaron_Fang used command /setweather 21
[2025-07-26T13:56:46+0700] [Info] [CMD]: Aaron_Fang used command /setweather 22
[2025-07-26T13:56:47+0700] [Info] [CMD]: Aaron_Fang used command /setweather 23
[2025-07-26T13:56:49+0700] [Info] [CMD]: Aaron_Fang used command /setweather 24
[2025-07-26T13:56:50+0700] [Info] [CMD]: Aaron_Fang used command /setweather 25
[2025-07-26T13:56:51+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T14:09:06+0700] [Info] [connection] incoming connection: **************:58037 id: 1
[2025-07-26T14:09:06+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-26T14:09:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /o uy
[2025-07-26T14:10:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-26T14:10:38+0700] [Info] [part] Kenzo_Wazoski has left the server (1:1)
[2025-07-26T14:10:56+0700] [Info] [connection] incoming connection: **************:56954 id: 1
[2025-07-26T14:10:56+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-26T14:11:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /l
[2025-07-26T14:12:13+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) masuk mode AFK
[2025-07-26T14:12:16+0700] [Info] [chat] [Kenzo_Wazoski]: p
[2025-07-26T14:12:16+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T14:12:27+0700] [Info] [chat] [Kenzo_Wazoski]: ppk ppls
[2025-07-26T14:12:27+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T14:12:28+0700] [Info] [chat] [Kenzo_Wazoski]: sd
[2025-07-26T14:12:28+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T14:12:29+0700] [Info] [chat] [Kenzo_Wazoski]: sd
[2025-07-26T14:12:29+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T14:16:48+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-26T14:16:48+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (93428a6e494eafb0)
[2025-07-26T14:16:48+0700] [Info] [connection] incoming connection: **************:62013 id: 2
[2025-07-26T14:16:48+0700] [Info] [join] tata has joined the server (2:**************)
[2025-07-26T14:16:51+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:62014)
[2025-07-26T14:17:13+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-26T14:17:17+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:17:21+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T14:17:30+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T14:17:58+0700] [Info] [chat] [Kenzo_Wazoski]: p
[2025-07-26T14:17:58+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T14:17:59+0700] [Info] [part] Kenzo_Wazoski has left the server (1:1)
[2025-07-26T14:19:47+0700] [Info] [CMD]: Tata_Wazoski used command /startmine
[2025-07-26T14:21:25+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-26T14:22:41+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-26T14:22:42+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) disconnect saat AFK - data telah di-restore
[2025-07-26T14:22:42+0700] [Info] [part] Aaron_Fang has left the server (0:1)
[2025-07-26T14:23:26+0700] [Info] [CMD]: Tata_Wazoski used command /stats
[2025-07-26T14:23:49+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-26T14:23:51+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-26T14:24:15+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T14:24:47+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*************) ...
[2025-07-26T14:24:47+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (1e23ba6706e1d047)
[2025-07-26T14:24:47+0700] [Info] [connection] incoming connection: *************:54175 id: 0
[2025-07-26T14:24:47+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-26T14:26:39+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T14:27:51+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-26T14:28:11+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-26T14:28:40+0700] [Info] [connection] incoming connection: **************:50032 id: 1
[2025-07-26T14:28:40+0700] [Info] [join] Aldo has joined the server (1:**************)
[2025-07-26T14:29:11+0700] [Info] [CMD]: Mark_Olrando used command /pm 0 bang aro
[2025-07-26T14:29:32+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 min
[2025-07-26T14:29:39+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 senjata gua hilang
[2025-07-26T14:29:47+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 gua baru main loh
[2025-07-26T14:29:57+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:30:01+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:30:05+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-26T14:30:05+0700] [Info] [part] Tata_Wazoski has left the server (2:1)
[2025-07-26T14:30:11+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-26T14:30:11+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (93428a6e0218a7bc)
[2025-07-26T14:30:11+0700] [Info] [connection] incoming connection: **************:60831 id: 2
[2025-07-26T14:30:11+0700] [Info] [join] tata has joined the server (2:**************)
[2025-07-26T14:30:14+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:60832)
[2025-07-26T14:30:39+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:30:40+0700] [Info] [CMD]: Mark_Olrando used command /pm 0 bang aro
[2025-07-26T14:30:43+0700] [Info] [CMD]: Mark_Olrando used command /pm 0 bang aro
[2025-07-26T14:30:44+0700] [Info] [CMD]: Mark_Olrando used command /pm 0 bang aro
[2025-07-26T14:30:45+0700] [Info] [CMD]: Mark_Olrando used command /pm 0 bang aro
[2025-07-26T14:30:50+0700] [Info] [CMD]: Aaron_Fang used command /pm 1 bodo amatd
[2025-07-26T14:30:56+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:30:57+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:30:58+0700] [Info] [CMD]: Aaron_Fang used command /pm 1 kemarin udang bilang jan minta lagi
[2025-07-26T14:31:20+0700] [Info] [CMD]: Aaron_Fang used command /pm 1 jan minta mulu cari sendiri lah
[2025-07-26T14:31:29+0700] [Info] [CMD]: Aaron_Fang used command /paytoll
[2025-07-26T14:31:29+0700] [Info] [CMD]: Aaron_Fang used command /open
[2025-07-26T14:31:43+0700] [Info] [CMD]: Aaron_Fang used command /clearchat
[2025-07-26T14:31:51+0700] [Info] [CMD]: Mark_Olrando used command /pm 0 bang hhhh laa
[2025-07-26T14:31:54+0700] [Info] [CMD]: Aaron_Fang used command /goto bank
[2025-07-26T14:32:01+0700] [Info] [CMD]: Aaron_Fang used command /deposit
[2025-07-26T14:32:07+0700] [Info] [CMD]: Mark_Olrando used command /pm 0 bang aku minta tolon
[2025-07-26T14:32:08+0700] [Info] [CMD]: Aaron_Fang used command /deposit 600000
[2025-07-26T14:32:10+0700] [Info] [CMD]: Aaron_Fang used command /deposit 60000
[2025-07-26T14:32:11+0700] [Info] [CMD]: Aaron_Fang used command /deposit 6000
[2025-07-26T14:32:21+0700] [Info] [CMD]: Aaron_Fang used command /withdraw 500
[2025-07-26T14:32:25+0700] [Info] [CMD]: Aaron_Fang used command /withdraw 500
[2025-07-26T14:32:29+0700] [Info] [CMD]: Aaron_Fang used command /withdraw
[2025-07-26T14:32:33+0700] [Info] [CMD]: Aaron_Fang used command /withdraw
[2025-07-26T14:32:41+0700] [Info] [CMD]: Aaron_Fang used command /withdraw
[2025-07-26T14:32:46+0700] [Info] [CMD]: Aaron_Fang used command /atm
[2025-07-26T14:32:59+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T14:33:02+0700] [Info] [CMD]: Aaron_Fang used command /goto 15
[2025-07-26T14:33:05+0700] [Info] [CMD]: Aaron_Fang used command /gotocar 15
[2025-07-26T14:33:21+0700] [Info] [CMD]: Aaron_Fang used command /pm 2 gua cape ye lagi cari player di suruh" mulu
[2025-07-26T14:33:31+0700] [Info] [CMD]: Aaron_Fang used command /pm 2 main aja sih mau instant terus
[2025-07-26T14:33:35+0700] [Info] [CMD]: Aaron_Fang used command /refill 0
[2025-07-26T14:33:47+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 kan abang yang ngajak gua robert
[2025-07-26T14:34:05+0700] [Info] [CMD]: Mark_Olrando used command /pm 0 bang aku minta tolon
[2025-07-26T14:34:54+0700] [Info] [sv:dbg:network:connect] : connecting player (3) with address (**************) ...
[2025-07-26T14:34:54+0700] [Info] [sv:dbg:network:connect] : player (3) assigned key (93428a6eaa310041)
[2025-07-26T14:34:54+0700] [Info] [connection] incoming connection: **************:58325 id: 3
[2025-07-26T14:34:54+0700] [Info] [join] FanID has joined the server (3:**************)
[2025-07-26T14:34:57+0700] [Info] [sv:dbg:network:receive] : player (3) identified (port:64653)
[2025-07-26T14:35:13+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:35:13+0700] [Info] [CMD]: Aaron_Fang used command /CLEARCHAT
[2025-07-26T14:35:20+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T14:35:24+0700] [Info] [CMD]: Aaron_Fang used command /FINDTREE
[2025-07-26T14:36:11+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-26T14:36:25+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-26T14:36:36+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-26T14:36:36+0700] [Info] [kill] Yuki_Haruto killed Mark_Olrando Shotgun
[2025-07-26T14:36:44+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-26T14:36:55+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-26T14:37:09+0700] [Info] [CMD]: Yuki_Haruto used command /b /v list
[2025-07-26T14:37:11+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-26T14:37:16+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-26T14:37:17+0700] [Info] [CMD]: Mark_Olrando used command /v list
[2025-07-26T14:37:24+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-26T14:37:35+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:37:41+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-26T14:37:42+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:37:42+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-26T14:37:42+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-26T14:37:48+0700] [Info] [CMD]: Tata_Wazoski used command /lock/delayjob\
[2025-07-26T14:37:54+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-26T14:38:13+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-26T14:38:13+0700] [Info] [kill] Yuki_Haruto killed Mark_Olrando Shotgun
[2025-07-26T14:38:17+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-26T14:38:32+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 18
[2025-07-26T14:38:34+0700] [Info] [CMD]: Tata_Wazoski used command /refuel
[2025-07-26T14:38:36+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T14:39:17+0700] [Info] [CMD]: Aaron_Fang used command /BUYCHAINSAW
[2025-07-26T14:39:30+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 0
[2025-07-26T14:39:35+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 50
[2025-07-26T14:39:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T14:40:21+0700] [Info] [CMD]: Aaron_Fang used command /ME mengeluarkan chainsaw dan memotong pohon dengan kedua tangan
[2025-07-26T14:40:32+0700] [Info] [sv:dbg:network:connect] : disconnecting player (3) ...
[2025-07-26T14:40:32+0700] [Info] [part] Yuki_Haruto has left the server (3:1)
[2025-07-26T14:42:06+0700] [Info] [CMD]: Mark_Olrando used command /depopsi
[2025-07-26T14:42:18+0700] [Info] [CMD]: Mark_Olrando used command /depopsit 1000
[2025-07-26T14:42:25+0700] [Info] [CMD]: Mark_Olrando used command /deposit 1000
[2025-07-26T14:42:40+0700] [Info] [part] Mark_Olrando has left the server (1:1)
[2025-07-26T14:44:35+0700] [Info] [CMD]: Aaron_Fang used command /v trunk
[2025-07-26T14:46:53+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T14:46:57+0700] [Info] [CMD]: Aaron_Fang used command /gotoar 15
[2025-07-26T14:46:57+0700] [Info] [death] Aaron_Fang died 255
[2025-07-26T14:47:02+0700] [Info] [death] Aaron_Fang died 255
[2025-07-26T14:50:16+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T14:50:19+0700] [Info] [CMD]: Aaron_Fang used command /gotoar 15
[2025-07-26T14:51:03+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-26T14:51:03+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T14:51:49+0700] [Info] [CMD]: Aaron_Fang used command /gotocar
[2025-07-26T14:51:51+0700] [Info] [CMD]: Aaron_Fang used command /gotocar 15
[2025-07-26T14:52:03+0700] [Info] [CMD]: Aaron_Fang used command /goto job 3
[2025-07-26T14:52:03+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-26T14:52:03+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T14:52:08+0700] [Info] [CMD]: Aaron_Fang used command /buychainsaw
[2025-07-26T14:52:12+0700] [Info] [CMD]: Aaron_Fang used command /gotocar 15
[2025-07-26T14:53:41+0700] [Info] [CMD]: Aaron_Fang used command /v trunk
[2025-07-26T14:54:40+0700] [Info] [CMD]: Aaron_Fang used command /selllumber
[2025-07-26T14:54:43+0700] [Info] [CMD]: Aaron_Fang used command /selllumber
[2025-07-26T14:54:44+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T14:54:52+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T14:54:58+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T14:55:05+0700] [Info] [CMD]: Aaron_Fang used command /mysalary
[2025-07-26T14:55:07+0700] [Info] [CMD]: Aaron_Fang used command /mysalary
[2025-07-26T14:55:14+0700] [Info] [CMD]: Aaron_Fang used command /inventory
[2025-07-26T14:55:16+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-26T14:55:20+0700] [Info] [CMD]: Aaron_Fang used command /drink water
[2025-07-26T14:55:22+0700] [Info] [CMD]: Aaron_Fang used command /inventory
[2025-07-26T14:55:25+0700] [Info] [CMD]: Aaron_Fang used command /inventory
[2025-07-26T14:56:31+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T14:56:49+0700] [Info] [CMD]: Aaron_Fang used command /refuel
[2025-07-26T14:56:55+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-26T14:57:04+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T14:57:39+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T14:57:41+0700] [Info] [CMD]: Aaron_Fang used command /vault
[2025-07-26T14:57:49+0700] [Info] [CMD]: Aaron_Fang used command /piss
[2025-07-26T14:57:58+0700] [Info] [CMD]: Aaron_Fang used command /atm
[2025-07-26T14:58:07+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T14:58:11+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T14:58:16+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T14:58:36+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T14:58:40+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T14:58:44+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T14:58:47+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T14:58:48+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T14:58:53+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T14:59:15+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T14:59:41+0700] [Info] [CMD]: Aaron_Fang used command /paytoll
[2025-07-26T14:59:41+0700] [Info] [CMD]: Aaron_Fang used command /open
[2025-07-26T14:59:51+0700] [Info] [CMD]: Aaron_Fang used command /open
[2025-07-26T14:59:51+0700] [Info] [CMD]: Aaron_Fang used command /open
[2025-07-26T14:59:51+0700] [Info] [CMD]: Aaron_Fang used command /open
[2025-07-26T14:59:51+0700] [Info] [CMD]: Aaron_Fang used command /open
[2025-07-26T15:00:01+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T15:00:44+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T15:00:49+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T15:01:20+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T15:01:54+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T15:02:25+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T15:02:56+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T15:03:30+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-26T15:05:19+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-26T15:05:23+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-26T15:05:27+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-26T15:06:36+0700] [Info] [connection] incoming connection: 114.10.78.206:19783 id: 1
[2025-07-26T15:06:37+0700] [Info] [join] Jenarrey has joined the server (1:114.10.78.206)
[2025-07-26T15:06:38+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 1
[2025-07-26T15:07:08+0700] [Info] [CMD]: Derryl_Winston used command /helm
[2025-07-26T15:07:16+0700] [Info] [CMD]: Tata_Wazoski used command /sellfish
[2025-07-26T15:07:17+0700] [Info] [FISH WAREHOUSE] Stock saved successfully: 83 units
[2025-07-26T15:07:24+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-26T15:07:41+0700] [Info] [CMD]: Derryl_Winston used command /ask
[2025-07-26T15:07:42+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-26T15:07:42+0700] [Info] [part] Tata_Wazoski has left the server (2:1)
[2025-07-26T15:09:55+0700] [Info] [CMD]: Aaron_Fang used command /anitro
[2025-07-26T15:09:57+0700] [Info] [CMD]: Aaron_Fang used command /ahelp
[2025-07-26T15:10:07+0700] [Info] [CMD]: Aaron_Fang used command /atune
[2025-07-26T15:10:18+0700] [Info] [CMD]: Aaron_Fang used command /apaintjob 1
[2025-07-26T15:11:07+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T15:11:18+0700] [Info] [CMD]: Derryl_Winston used command /open
[2025-07-26T15:13:19+0700] [Info] [part] Derryl_Winston has left the server (1:1)
[2025-07-26T15:16:36+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) masuk mode AFK
[2025-07-26T15:21:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T15:26:31+0700] [Info] [CMD]: Aaron_Fang used command /afk ylk063
[2025-07-26T15:26:37+0700] [Info] [CMD]: Aaron_Fang used command /unfreee 0
[2025-07-26T15:26:41+0700] [Info] [CMD]: Aaron_Fang used command /unfreee 0
[2025-07-26T15:27:19+0700] [Info] [connection] incoming connection: *************:57542 id: 1
[2025-07-26T15:27:19+0700] [Info] [join] Blood has joined the server (1:*************)
[2025-07-26T15:27:36+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /o NUNU NGENTOD YOK
[2025-07-26T15:27:36+0700] [Info] [part] Vyacheslav_Smirnov has left the server (1:1)
[2025-07-26T15:29:09+0700] [Info] [CMD]: Aaron_Fang used command /unfrezee 0
[2025-07-26T15:29:17+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 0
[2025-07-26T15:29:58+0700] [Info] [connection] incoming connection: *************:55550 id: 1
[2025-07-26T15:29:58+0700] [Info] [join] Blood has joined the server (1:*************)
[2025-07-26T15:30:21+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /hep
[2025-07-26T15:30:22+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /gps
[2025-07-26T15:30:26+0700] [Info] [CMD]: Aaron_Fang used command /o halo
[2025-07-26T15:30:28+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /stats
[2025-07-26T15:30:32+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /o halo aaron
[2025-07-26T15:30:35+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /o anda dimana
[2025-07-26T15:30:37+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /id 0
[2025-07-26T15:30:40+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /id aaron
[2025-07-26T15:30:42+0700] [Info] [CMD]: Aaron_Fang used command /o player baru?
[2025-07-26T15:30:44+0700] [Info] [CMD]: Aaron_Fang used command /refill 0
[2025-07-26T15:30:46+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /o iya pak
[2025-07-26T15:30:50+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /stats
[2025-07-26T15:30:51+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T15:31:06+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-26T15:31:08+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-26T15:31:08+0700] [Info] [death] Aaron_Fang died 54
[2025-07-26T15:31:09+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /gps
[2025-07-26T15:31:14+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /gps
[2025-07-26T15:31:15+0700] [Info] [CMD]: Aaron_Fang used command /revive 0
[2025-07-26T15:31:17+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /gps
[2025-07-26T15:31:19+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /gps
[2025-07-26T15:31:19+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-26T15:31:22+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /gps
[2025-07-26T15:31:24+0700] [Info] [CMD]: Aaron_Fang used command /spectate 1
[2025-07-26T15:31:25+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /gps
[2025-07-26T15:31:32+0700] [Info] [CMD]: Aaron_Fang used command /spectate 1
[2025-07-26T15:31:40+0700] [Info] [connection] incoming connection: 114.10.77.206:31486 id: 2
[2025-07-26T15:31:41+0700] [Info] [join] Jenarrey has joined the server (2:114.10.77.206)
[2025-07-26T15:31:44+0700] [Info] [CMD]: Aaron_Fang used command /ban
[2025-07-26T15:31:55+0700] [Info] [CMD]: Vyacheslav_Smirnov used command /buy
[2025-07-26T15:32:04+0700] [Info] [CMD]: Aaron_Fang used command /ban 1 flying hack
[2025-07-26T15:32:04+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s was banned by %s for: %s." < 6456495
[2025-07-26T15:32:04+0700] [Info] [part] Vyacheslav_Smirnov has left the server (1:2)
[2025-07-26T15:32:10+0700] [Info] [CMD]: Aaron_Fang used command /checkip
[2025-07-26T15:32:12+0700] [Info] [CMD]: Aaron_Fang used command /ip
[2025-07-26T15:32:17+0700] [Info] [CMD]: Albertus_Ragenmar used command /checkip
[2025-07-26T15:32:19+0700] [Info] [CMD]: Albertus_Ragenmar used command /ip
[2025-07-26T15:32:20+0700] [Info] [CMD]: Albertus_Ragenmar used command /getip
[2025-07-26T15:32:23+0700] [Info] [CMD]: Albertus_Ragenmar used command /o /getip
[2025-07-26T15:32:24+0700] [Info] [CMD]: Aaron_Fang used command /getip
[2025-07-26T15:32:25+0700] [Info] [CMD]: Albertus_Ragenmar used command /ogetip
[2025-07-26T15:32:26+0700] [Info] [CMD]: Aaron_Fang used command /getip 1
[2025-07-26T15:32:28+0700] [Info] [CMD]: Albertus_Ragenmar used command /oipcheck
[2025-07-26T15:32:33+0700] [Info] [CMD]: Aaron_Fang used command /getip Vyacheslav_Smirnov
[2025-07-26T15:32:35+0700] [Info] [CMD]: Aaron_Fang used command /getip Vyacheslav_Smirnov
[2025-07-26T15:32:37+0700] [Info] [CMD]: Albertus_Ragenmar used command /ah
[2025-07-26T15:32:38+0700] [Info] [CMD]: Aaron_Fang used command /goto 2
[2025-07-26T15:32:42+0700] [Info] [CMD]: Albertus_Ragenmar used command /oipcheck
[2025-07-26T15:32:45+0700] [Info] [CMD]: Aaron_Fang used command /ahelp
[2025-07-26T15:32:45+0700] [Info] [CMD]: Albertus_Ragenmar used command /ah
[2025-07-26T15:32:48+0700] [Info] [CMD]: Albertus_Ragenmar used command /ip
[2025-07-26T15:32:51+0700] [Info] [CMD]: Albertus_Ragenmar used command /checkip
[2025-07-26T15:32:56+0700] [Info] [CMD]: Albertus_Ragenmar used command /username
[2025-07-26T15:32:59+0700] [Info] [CMD]: Albertus_Ragenmar used command /o /username
[2025-07-26T15:33:02+0700] [Info] [CMD]: Aaron_Fang used command /username
[2025-07-26T15:33:10+0700] [Info] [CMD]: Aaron_Fang used command /username Vyacheslav_Smirnov
[2025-07-26T15:33:28+0700] [Info] [CMD]: Albertus_Ragenmar used command /blood
[2025-07-26T15:34:03+0700] [Info] [CMD]: Albertus_Ragenmar used command /reports
[2025-07-26T15:34:04+0700] [Info] [CMD]: Albertus_Ragenmar used command /ask
[2025-07-26T15:34:15+0700] [Info] [CMD]: Aaron_Fang used command /goto job 1
[2025-07-26T15:34:25+0700] [Info] [CMD]: Albertus_Ragenmar used command /vmodels
[2025-07-26T15:34:30+0700] [Info] [CMD]: Albertus_Ragenmar used command /vehmodels
[2025-07-26T15:34:31+0700] [Info] [CMD]: Aaron_Fang used command /models
[2025-07-26T15:34:35+0700] [Info] [CMD]: Albertus_Ragenmar used command /vid
[2025-07-26T15:34:44+0700] [Info] [CMD]: Albertus_Ragenmar used command /drink water
[2025-07-26T15:34:45+0700] [Info] [CMD]: Albertus_Ragenmar used command /drink water
[2025-07-26T15:34:59+0700] [Info] [CMD]: Aaron_Fang used command /createveh
[2025-07-26T15:35:01+0700] [Info] [CMD]: Aaron_Fang used command /createcar
[2025-07-26T15:35:03+0700] [Info] [CMD]: Albertus_Ragenmar used command /startmine
[2025-07-26T15:35:07+0700] [Info] [CMD]: Aaron_Fang used command /createveg
[2025-07-26T15:35:09+0700] [Info] [CMD]: Aaron_Fang used command /createveh
[2025-07-26T15:35:12+0700] [Info] [CMD]: Aaron_Fang used command /ahelp
[2025-07-26T15:36:16+0700] [Info] [CMD]: Aaron_Fang used command /getip
[2025-07-26T15:36:24+0700] [Info] [CMD]: Aaron_Fang used command /getip Vyacheslav_Smirnov
[2025-07-26T15:36:27+0700] [Info] [CMD]: Aaron_Fang used command /getip Vyacheslav_Smirnov
[2025-07-26T15:36:28+0700] [Info] [CMD]: Aaron_Fang used command /getip Vyacheslav_Smirnov
[2025-07-26T15:36:41+0700] [Info] [CMD]: Aaron_Fang used command /infologging
[2025-07-26T15:36:48+0700] [Info] [CMD]: Aaron_Fang used command /infologging Vyacheslav_Smirnov
[2025-07-26T15:37:07+0700] [Info] [chat] [Aaron_Fang]: a
[2025-07-26T15:37:30+0700] [Info] [CMD]: Aaron_Fang used command /spawncar
[2025-07-26T15:37:50+0700] [Info] [CMD]: Aaron_Fang used command /veh
[2025-07-26T15:37:53+0700] [Info] [CMD]: Albertus_Ragenmar used command /stats
[2025-07-26T15:38:09+0700] [Info] [CMD]: Aaron_Fang used command /veh 455
[2025-07-26T15:38:09+0700] [Warning] Vehicle created with respawn delay 0 which is undefined behaviour that might change in the future.
[2025-07-26T15:38:18+0700] [Info] [CMD]: Aaron_Fang used command /acolour
[2025-07-26T15:38:23+0700] [Info] [CMD]: Aaron_Fang used command /asetcolor
[2025-07-26T15:38:26+0700] [Info] [CMD]: Aaron_Fang used command /asetcolour
[2025-07-26T15:38:38+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 5
[2025-07-26T15:38:40+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 5 1
[2025-07-26T15:38:43+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 7 1
[2025-07-26T15:38:45+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 7 2
[2025-07-26T15:38:47+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 8
[2025-07-26T15:38:49+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 8 2
[2025-07-26T15:38:52+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 9 3
[2025-07-26T15:39:01+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 6 2
[2025-07-26T15:39:37+0700] [Info] [CMD]: Aaron_Fang used command /veh 455
[2025-07-26T15:39:46+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 8 2
[2025-07-26T15:39:50+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 6 2
[2025-07-26T15:39:52+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 7 2
[2025-07-26T15:40:08+0700] [Info] [CMD]: Albertus_Ragenmar used command /veh
[2025-07-26T15:40:28+0700] [Info] [CMD]: Aaron_Fang used command /veh 455
[2025-07-26T15:40:52+0700] [Info] [CMD]: Aaron_Fang used command /veh 455
[2025-07-26T15:40:58+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 5
[2025-07-26T15:40:59+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 5 1
[2025-07-26T15:41:04+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 4 2
[2025-07-26T15:41:35+0700] [Info] [CMD]: Aaron_Fang used command /veh 455
[2025-07-26T15:41:57+0700] [Info] [CMD]: Aaron_Fang used command /veh 455
[2025-07-26T15:42:04+0700] [Info] [CMD]: Aaron_Fang used command /acolorcar 8 2
[2025-07-26T15:42:34+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-26T15:42:34+0700] [Info] [part] Aaron_Fang has left the server (0:1)
[2025-07-26T15:42:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T15:45:32+0700] [Info] [AFK] Player Albertus_Ragenmar (ID: 2) masuk mode AFK
[2025-07-26T15:48:04+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*************) ...
[2025-07-26T15:48:04+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (1e23ba674e29377f)
[2025-07-26T15:48:04+0700] [Info] [connection] incoming connection: *************:51348 id: 0
[2025-07-26T15:48:04+0700] [Info] [join] dflx has joined the server (0:*************)
[2025-07-26T15:49:24+0700] [Info] [CMD]: Albertus_Ragenmar used command /afk chggwn
[2025-07-26T15:49:24+0700] [Info] [AFK] Player Albertus_Ragenmar (ID: 2) keluar dari mode AFK
[2025-07-26T15:49:31+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T15:49:39+0700] [Info] [CMD]: Albertus_Ragenmar used command /drink water
[2025-07-26T15:49:41+0700] [Info] [CMD]: Albertus_Ragenmar used command /drink water
[2025-07-26T15:49:43+0700] [Info] [CMD]: Albertus_Ragenmar used command /piss
[2025-07-26T15:50:34+0700] [Info] [CMD]: Aaron_Fang used command /toggle
[2025-07-26T15:50:38+0700] [Info] [CMD]: Aaron_Fang used command /toggle
[2025-07-26T15:50:40+0700] [Info] [CMD]: Aaron_Fang used command /toggle
[2025-07-26T15:50:50+0700] [Info] [CMD]: Aaron_Fang used command /clearchat
[2025-07-26T15:50:54+0700] [Info] [CMD]: Aaron_Fang used command /toggle
[2025-07-26T15:51:00+0700] [Info] [CMD]: Aaron_Fang used command /clearchat
[2025-07-26T15:52:17+0700] [Info] [CMD]: Albertus_Ragenmar used command /cammode
[2025-07-26T15:52:18+0700] [Info] [CMD]: Albertus_Ragenmar used command /cam
[2025-07-26T15:54:48+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-26T15:54:48+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (93428a6eab94ac50)
[2025-07-26T15:54:48+0700] [Info] [connection] incoming connection: **************:52887 id: 1
[2025-07-26T15:54:48+0700] [Info] [join] tata has joined the server (1:**************)
[2025-07-26T15:54:50+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:57979)
[2025-07-26T15:55:05+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T15:55:16+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-26T15:56:19+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-26T15:56:32+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-26T15:56:34+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-26T15:56:47+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash\
[2025-07-26T15:56:49+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash\
[2025-07-26T15:56:50+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-26T15:57:11+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-26T15:57:13+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-26T15:57:24+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-26T15:57:32+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T16:00:13+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T16:00:16+0700] [Info] [CMD]: Aaron_Fang used command /setweather 2
[2025-07-26T16:00:18+0700] [Info] [CMD]: Aaron_Fang used command /setweather 3
[2025-07-26T16:00:19+0700] [Info] [CMD]: Aaron_Fang used command /setweather 4
[2025-07-26T16:00:20+0700] [Info] [CMD]: Aaron_Fang used command /setweather 5
[2025-07-26T16:00:21+0700] [Info] [CMD]: Aaron_Fang used command /setweather 6
[2025-07-26T16:00:23+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T16:00:34+0700] [Info] [CMD]: Aaron_Fang used command /clearchat
[2025-07-26T16:00:42+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 min buat siang aja
[2025-07-26T16:01:44+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T16:02:07+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T16:02:12+0700] [Info] [connection] incoming connection: **************:55186 id: 3
[2025-07-26T16:02:12+0700] [Info] [join] Aldo has joined the server (3:**************)
[2025-07-26T16:02:30+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T16:02:42+0700] [Info] [CMD]: Tata_Wazoski used command /myslary
[2025-07-26T16:02:45+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-26T16:03:43+0700] [Info] [INFO] Updated capacity for 4 garbage bins
[2025-07-26T16:05:02+0700] [Info] [CMD]: Tata_Wazoski used command /startmine
[2025-07-26T16:06:09+0700] [Info] [CMD]: Mark_Olrando used command /startmine
[2025-07-26T16:07:33+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-26T16:08:05+0700] [Info] [CMD]: Mark_Olrando used command /drink water
[2025-07-26T16:10:17+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T16:10:18+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T16:10:25+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-26T16:10:32+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T16:10:32+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) masuk mode AFK
[2025-07-26T16:10:37+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T16:12:07+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T16:13:22+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-26T16:13:22+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:13:38+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T16:13:42+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T16:16:21+0700] [Info] [part] Albertus_Ragenmar has left the server (2:1)
[2025-07-26T16:16:29+0700] [Info] [CMD]: Tata_Wazoski used command /buychainsaw
[2025-07-26T16:16:42+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-26T16:17:16+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-26T16:17:27+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T16:17:31+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T16:17:35+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T16:17:39+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T16:17:42+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T16:17:47+0700] [Info] [CMD]: Mark_Olrando used command /pis
[2025-07-26T16:17:49+0700] [Info] [CMD]: Mark_Olrando used command /piss
[2025-07-26T16:18:13+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-26T16:18:50+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-26T16:19:07+0700] [Info] [CMD]: Aaron_Fang used command /goto
[2025-07-26T16:19:19+0700] [Info] [CMD]: Aaron_Fang used command /goto
[2025-07-26T16:19:21+0700] [Info] [CMD]: Aaron_Fang used command /gotojob 1
[2025-07-26T16:19:24+0700] [Info] [CMD]: Aaron_Fang used command /goto job 1
[2025-07-26T16:19:26+0700] [Info] [CMD]: Aaron_Fang used command /goto job 2
[2025-07-26T16:19:32+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze1
[2025-07-26T16:19:36+0700] [Info] [CMD]: Aaron_Fang used command /freeze 0
[2025-07-26T16:19:39+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 0
[2025-07-26T16:19:42+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T16:19:45+0700] [Info] [death] Tata_Wazoski died 54
[2025-07-26T16:20:17+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-26T16:20:17+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (93428a6ec368a781)
[2025-07-26T16:20:17+0700] [Info] [connection] incoming connection: **************:50783 id: 2
[2025-07-26T16:20:17+0700] [Info] [join] FanID has joined the server (2:**************)
[2025-07-26T16:20:21+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:58776)
[2025-07-26T16:20:57+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-26T16:21:01+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-26T16:21:03+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-26T16:21:22+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-26T16:21:32+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-26T16:21:34+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-26T16:21:39+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-26T16:21:39+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-26T16:21:40+0700] [Info] [part] Yuki_Haruto has left the server (2:1)
[2025-07-26T16:21:40+0700] [Info] [part] Mark_Olrando has left the server (3:1)
[2025-07-26T16:21:58+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-26T16:21:58+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (93428a6e5199c36c)
[2025-07-26T16:21:58+0700] [Info] [connection] incoming connection: **************:57925 id: 2
[2025-07-26T16:21:58+0700] [Info] [join] FanID has joined the server (2:**************)
[2025-07-26T16:22:01+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:54743)
[2025-07-26T16:22:38+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-26T16:22:41+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-26T16:22:55+0700] [Info] [CMD]: Aaron_Fang used command /goto
[2025-07-26T16:22:59+0700] [Info] [CMD]: Aaron_Fang used command /goto job 2
[2025-07-26T16:23:00+0700] [Info] [CMD]: Aaron_Fang used command /goto job 3
[2025-07-26T16:23:10+0700] [Info] [connection] incoming connection: **************:64587 id: 3
[2025-07-26T16:23:10+0700] [Info] [join] Aldo has joined the server (3:**************)
[2025-07-26T16:23:25+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-26T16:23:25+0700] [Info] [part] Tata_Wazoski has left the server (1:1)
[2025-07-26T16:23:31+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-26T16:23:31+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (93428a6e38eb6e57)
[2025-07-26T16:23:31+0700] [Info] [connection] incoming connection: **************:52043 id: 1
[2025-07-26T16:23:31+0700] [Info] [join] tata has joined the server (1:**************)
[2025-07-26T16:23:36+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:52044)
[2025-07-26T16:23:36+0700] [Info] [CMD]: Aaron_Fang used command /goto job 4
[2025-07-26T16:23:40+0700] [Info] [CMD]: Aaron_Fang used command /goto job 5
[2025-07-26T16:23:51+0700] [Info] [CMD]: Aaron_Fang used command /help
[2025-07-26T16:23:57+0700] [Info] [CMD]: Aaron_Fang used command /help
[2025-07-26T16:23:59+0700] [Info] [CMD]: Aaron_Fang used command /joblist
[2025-07-26T16:24:09+0700] [Info] [CMD]: Aaron_Fang used command /goto cityhall
[2025-07-26T16:24:17+0700] [Info] [CMD]: Aaron_Fang used command /goto job 1
[2025-07-26T16:24:25+0700] [Info] [CMD]: Aaron_Fang used command /destroycar
[2025-07-26T16:24:27+0700] [Info] [CMD]: Aaron_Fang used command /destroycar
[2025-07-26T16:24:29+0700] [Info] [CMD]: Aaron_Fang used command /destroyveh
[2025-07-26T16:24:31+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-26T16:24:34+0700] [Info] [CMD]: Aaron_Fang used command /destroyveh
[2025-07-26T16:24:37+0700] [Info] [CMD]: Mark_Olrando used command /o tolo lu
[2025-07-26T16:24:39+0700] [Info] [CMD]: Aaron_Fang used command /destroyveh
[2025-07-26T16:24:40+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 1
[2025-07-26T16:24:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T16:24:46+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (reason %d)." < 6456495
[2025-07-26T16:24:50+0700] [Info] [CMD]: Aaron_Fang used command /destroyveh
[2025-07-26T16:24:55+0700] [Info] [CMD]: Aaron_Fang used command /destroyveh
[2025-07-26T16:24:58+0700] [Info] [CMD]: Aaron_Fang used command /destroyveh
[2025-07-26T16:25:00+0700] [Info] [CMD]: Aaron_Fang used command /destroyveh
[2025-07-26T16:25:05+0700] [Info] [CMD]: Tata_Wazoski used command /v trunk
[2025-07-26T16:25:06+0700] [Info] [CMD]: Aaron_Fang used command /gps
[2025-07-26T16:25:07+0700] [Info] [CMD]: Mark_Olrando used command /fer uel
[2025-07-26T16:25:16+0700] [Info] [CMD]: Mark_Olrando used command /feruel
[2025-07-26T16:25:22+0700] [Info] [CMD]: Aaron_Fang used command /goto cityhall
[2025-07-26T16:25:25+0700] [Info] [CMD]: Mark_Olrando used command /freuel
[2025-07-26T16:25:30+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-26T16:25:34+0700] [Info] [CMD]: Mark_Olrando used command /refuel
[2025-07-26T16:26:01+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-26T16:26:01+0700] [Info] [CMD]: Mark_Olrando used command /o tolo anj lu
[2025-07-26T16:26:06+0700] [Info] [CMD]: Aaron_Fang used command /togooc
[2025-07-26T16:26:09+0700] [Info] [CMD]: Aaron_Fang used command /cealallchat
[2025-07-26T16:26:25+0700] [Info] [CMD]: Aaron_Fang used command /gps
[2025-07-26T16:26:36+0700] [Info] [CMD]: Aaron_Fang used command /gps
[2025-07-26T16:26:38+0700] [Info] [CMD]: Yuki_Haruto used command /pm 0 jail aja itu bang
[2025-07-26T16:26:45+0700] [Info] [CMD]: Aaron_Fang used command /o a
[2025-07-26T16:26:48+0700] [Info] [CMD]: Yuki_Haruto used command /pm 0 bandel kali sumpah 
[2025-07-26T16:26:53+0700] [Info] [CMD]: Aaron_Fang used command /togooc
[2025-07-26T16:26:59+0700] [Info] [CMD]: Aaron_Fang used command /o kenapa lagi dah
[2025-07-26T16:27:03+0700] [Info] [CMD]: Yuki_Haruto used command /pm 0 orang lagi kerja di di pukulin 
[2025-07-26T16:27:03+0700] [Info] [connection] incoming connection: *************:64336 id: 4
[2025-07-26T16:27:03+0700] [Info] [join] Blood has joined the server (4:*************)
[2025-07-26T16:27:04+0700] [Info] [part] Blood has left the server (4:2)
[2025-07-26T16:27:05+0700] [Info] [CMD]: Aaron_Fang used command /o kaga penting banget sih brantem
[2025-07-26T16:27:21+0700] [Info] [CMD]: Aaron_Fang used command /goto business 4
[2025-07-26T16:27:25+0700] [Info] [CMD]: Aaron_Fang used command /goto business 1
[2025-07-26T16:27:41+0700] [Info] [CMD]: Tata_Wazoski used command /pay 2 71
[2025-07-26T16:27:41+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-26T16:28:02+0700] [Info] [CMD]: Mark_Olrando used command /o apa bang aku aldo
[2025-07-26T16:28:25+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-26T16:28:38+0700] [Info] [CMD]: Aaron_Fang used command /gotojob 1
[2025-07-26T16:28:43+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-26T16:28:44+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-26T16:28:56+0700] [Info] [chat] [Tata_Wazoski]: stats
[2025-07-26T16:28:56+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T16:29:00+0700] [Info] [CMD]: Tata_Wazoski used command /stats
[2025-07-26T16:29:14+0700] [Info] [chat] [Tata_Wazoski]: stats
[2025-07-26T16:29:14+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T16:29:16+0700] [Info] [CMD]: Tata_Wazoski used command /findtree
[2025-07-26T16:29:32+0700] [Info] [CMD]: Aaron_Fang used command /togooc
[2025-07-26T16:29:38+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:38+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:38+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:39+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:39+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:39+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:39+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:39+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:40+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:40+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:40+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:40+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:40+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-26T16:29:42+0700] [Info] [CMD]: Aaron_Fang used command /goto
[2025-07-26T16:29:45+0700] [Info] [CMD]: Aaron_Fang used command / entrance
[2025-07-26T16:29:47+0700] [Info] [CMD]: Aaron_Fang used command /goto entrance
[2025-07-26T16:29:49+0700] [Info] [CMD]: Aaron_Fang used command /goto entrance 1
[2025-07-26T16:29:54+0700] [Info] [CMD]: Aaron_Fang used command /goto entrance 2
[2025-07-26T16:29:55+0700] [Info] [CMD]: Aaron_Fang used command /goto entrance 3
[2025-07-26T16:29:56+0700] [Info] [CMD]: Aaron_Fang used command /goto entrance 4
[2025-07-26T16:30:03+0700] [Info] [CMD]: Aaron_Fang used command /goto entrance 3
[2025-07-26T16:30:06+0700] [Info] [CMD]: Aaron_Fang used command /goto entrance 5
[2025-07-26T16:30:11+0700] [Info] [CMD]: Aaron_Fang used command /goto
[2025-07-26T16:30:19+0700] [Info] [CMD]: Aaron_Fang used command /jail
[2025-07-26T16:30:22+0700] [Info] [CMD]: Aaron_Fang used command /prison
[2025-07-26T16:30:25+0700] [Info] [CMD]: Aaron_Fang used command /j
[2025-07-26T16:30:27+0700] [Info] [CMD]: Aaron_Fang used command /jail
[2025-07-26T16:30:29+0700] [Info] [CMD]: Aaron_Fang used command /ajail
[2025-07-26T16:30:33+0700] [Info] [CMD]: Aaron_Fang used command /goto jail
[2025-07-26T16:30:39+0700] [Info] [CMD]: Mark_Olrando used command /o oo
[2025-07-26T16:30:46+0700] [Info] [CMD]: Tata_Wazoski used command /v trunk
[2025-07-26T16:30:49+0700] [Info] [CMD]: Mark_Olrando used command /b hhhhhhhhh
[2025-07-26T16:30:53+0700] [Info] [CMD]: Aaron_Fang used command /goto 
[2025-07-26T16:30:54+0700] [Info] [CMD]: Aaron_Fang used command /interor
[2025-07-26T16:30:57+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:02+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:05+0700] [Info] [CMD]: Yuki_Haruto used command /goto lumberjack
[2025-07-26T16:31:09+0700] [Info] [CMD]: Yuki_Haruto used command /goto lumber
[2025-07-26T16:31:11+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:14+0700] [Info] [CMD]: Yuki_Haruto used command /goto
[2025-07-26T16:31:20+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:24+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:28+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:35+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:38+0700] [Info] [CMD]: Tata_Wazoski used command /levels
[2025-07-26T16:31:41+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:47+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T16:31:51+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-26T16:31:51+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:31:59+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-26T16:32:07+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:32:19+0700] [Info] [CMD]: Aaron_Fang used command /bring 1
[2025-07-26T16:32:21+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-26T16:32:21+0700] [Info] [CMD]: Aaron_Fang used command /bring 2
[2025-07-26T16:32:23+0700] [Info] [CMD]: Aaron_Fang used command /bring 3
[2025-07-26T16:32:32+0700] [Info] [CMD]: Aaron_Fang used command /b sini lu semua
[2025-07-26T16:32:42+0700] [Info] [CMD]: Aaron_Fang used command /b turun
[2025-07-26T16:32:43+0700] [Info] [chat] [Mark_Olrando]: apa
[2025-07-26T16:32:43+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T16:32:47+0700] [Info] [chat] [Mark_Olrando]: apa
[2025-07-26T16:32:47+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T16:32:50+0700] [Info] [CMD]: Aaron_Fang used command /b turun
[2025-07-26T16:32:51+0700] [Info] [chat] [Mark_Olrando]: apa
[2025-07-26T16:32:51+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T16:32:53+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-26T16:33:05+0700] [Info] [CMD]: Aaron_Fang used command /b turun aldo
[2025-07-26T16:33:13+0700] [Info] [CMD]: Aaron_Fang used command /goto
[2025-07-26T16:33:15+0700] [Info] [chat] [Mark_Olrando]: aku lagi kerjs
[2025-07-26T16:33:15+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T16:33:17+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:33:21+0700] [Info] [CMD]: Yuki_Haruto used command /bring 3
[2025-07-26T16:33:22+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:33:26+0700] [Info] [CMD]: Yuki_Haruto used command /bring 3
[2025-07-26T16:33:27+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:33:30+0700] [Info] [CMD]: Yuki_Haruto used command /bring 3
[2025-07-26T16:33:31+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:33:35+0700] [Info] [CMD]: Aaron_Fang used command /goto interior
[2025-07-26T16:33:35+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-26T16:33:40+0700] [Info] [CMD]: Aaron_Fang used command /bring 1
[2025-07-26T16:33:42+0700] [Info] [CMD]: Aaron_Fang used command /bring 2
[2025-07-26T16:33:43+0700] [Info] [CMD]: Aaron_Fang used command /bring 3
[2025-07-26T16:33:51+0700] [Info] [CMD]: Aaron_Fang used command /b turun
[2025-07-26T16:33:54+0700] [Info] [chat] [Tata_Wazoski]: ngapain?
[2025-07-26T16:33:59+0700] [Info] [CMD]: Aaron_Fang used command /goto cityhall 
[2025-07-26T16:34:01+0700] [Info] [chat] [Tata_Wazoski]: lagi kerja aku cok
[2025-07-26T16:34:06+0700] [Info] [CMD]: Aaron_Fang used command /bring 3
[2025-07-26T16:34:20+0700] [Info] [CMD]: Aaron_Fang used command /goto 3
[2025-07-26T16:34:25+0700] [Info] [CMD]: Yuki_Haruto used command /goto 0
[2025-07-26T16:34:29+0700] [Info] [CMD]: Aaron_Fang used command /bring 1
[2025-07-26T16:34:36+0700] [Info] [CMD]: Aaron_Fang used command /b lu pada berantem ngapa sih?
[2025-07-26T16:34:37+0700] [Info] [chat] [Tata_Wazoski]: ngapain?\
[2025-07-26T16:34:40+0700] [Info] [CMD]: Yuki_Haruto used command /goto 0
[2025-07-26T16:34:44+0700] [Info] [chat] [Tata_Wazoski]: mana ada
[2025-07-26T16:34:50+0700] [Info] [chat] [Tata_Wazoski]: kami deketan nih
[2025-07-26T16:34:50+0700] [Info] [CMD]: Aaron_Fang used command /b si aldo jangan afk dulu
[2025-07-26T16:34:56+0700] [Info] [CMD]: Aaron_Fang used command /bring 3
[2025-07-26T16:35:00+0700] [Info] [chat] [Tata_Wazoski]: kami lagi kerja cok
[2025-07-26T16:35:02+0700] [Info] [CMD]: Aaron_Fang used command /bring 3
[2025-07-26T16:35:05+0700] [Info] [CMD]: Yuki_Haruto used command /b yang berantem aldo sama si tata tadi 
[2025-07-26T16:35:06+0700] [Info] [chat] [Tata_Wazoski]: ada adaa aja yahh
[2025-07-26T16:35:10+0700] [Info] [CMD]: Yuki_Haruto used command /b aku kagak ikutan 
[2025-07-26T16:35:15+0700] [Info] [CMD]: Aaron_Fang used command /b bisa gak rp bener
[2025-07-26T16:35:21+0700] [Info] [CMD]: Aaron_Fang used command /b bantuin aku lah ini biar server rame
[2025-07-26T16:35:25+0700] [Info] [chat] [Tata_Wazoski]: sementang lku admin seenaknya aja lu tp kan orang
[2025-07-26T16:35:27+0700] [Info] [CMD]: Aaron_Fang used command /b bukan ngurusin kalian berantem
[2025-07-26T16:35:33+0700] [Info] [CMD]: Mark_Olrando used command /o apa
[2025-07-26T16:35:38+0700] [Info] [chat] [Tata_Wazoski]: gak ada yang berantem
[2025-07-26T16:35:38+0700] [Info] [chat] [Mark_Olrando]: apa
[2025-07-26T16:35:46+0700] [Info] [CMD]: Aaron_Fang used command /b itu di ooc kenapa ngomong kasar terus sih
[2025-07-26T16:35:52+0700] [Info] [chat] [Mark_Olrando]: aku lagi keja
[2025-07-26T16:35:54+0700] [Info] [CMD]: Yuki_Haruto used command /b si aldo
[2025-07-26T16:35:55+0700] [Info] [chat] [Tata_Wazoski]: itu cuman candaan tempat kami
[2025-07-26T16:36:08+0700] [Info] [chat] [Mark_Olrando]: ya
[2025-07-26T16:36:10+0700] [Info] [chat] [Tata_Wazoski]: yahh tpkan aldo aja ngapain aku
[2025-07-26T16:36:19+0700] [Info] [CMD]: Aaron_Fang used command /b bantuin rame in ini server lah
[2025-07-26T16:36:24+0700] [Info] [CMD]: Yuki_Haruto used command /b pakai /b
[2025-07-26T16:36:30+0700] [Info] [CMD]: Aaron_Fang used command /b kalian ada tiktok gak?
[2025-07-26T16:36:36+0700] [Info] [CMD]: Mark_Olrando used command /b apa
[2025-07-26T16:36:38+0700] [Info] [CMD]: Tata_Wazoski used command /b gak min tik tok
[2025-07-26T16:37:07+0700] [Info] [CMD]: Aaron_Fang used command /b fb main?
[2025-07-26T16:37:17+0700] [Info] [CMD]: Mark_Olrando used command /b aku lagi keja min gk usa apap
[2025-07-26T16:37:19+0700] [Info] [CMD]: Tata_Wazoski used command /b gua g main sosmed
[2025-07-26T16:37:24+0700] [Info] [CMD]: Aaron_Fang used command /b nih
[2025-07-26T16:37:28+0700] [Info] [CMD]: Aaron_Fang used command /b aku ada tugas buat kalian
[2025-07-26T16:37:31+0700] [Info] [CMD]: Tata_Wazoski used command /b selain ig sama discord
[2025-07-26T16:37:32+0700] [Info] [CMD]: Aaron_Fang used command /b dari pada berantem
[2025-07-26T16:37:36+0700] [Info] [CMD]: Aaron_Fang used command /b bantuin ramein ini server
[2025-07-26T16:37:37+0700] [Info] [CMD]: Yuki_Haruto used command /b si aldo ada tiktok tuh 
[2025-07-26T16:37:41+0700] [Info] [CMD]: Tata_Wazoski used command /b brapa dapat
[2025-07-26T16:37:48+0700] [Info] [CMD]: Aaron_Fang used command /b tar gua kasih uang ic 10k
[2025-07-26T16:37:54+0700] [Info] [CMD]: Aaron_Fang used command /b kalo pada ramein server
[2025-07-26T16:37:56+0700] [Info] [CMD]: Mark_Olrando used command /b apa min
[2025-07-26T16:38:09+0700] [Info] [CMD]: Aaron_Fang used command /b upload tiktok
[2025-07-26T16:38:11+0700] [Info] [CMD]: Aaron_Fang used command /b dll
[2025-07-26T16:38:11+0700] [Info] [CMD]: Tata_Wazoski used command /b yah pas gua mau belik rumah sama sultan
[2025-07-26T16:38:21+0700] [Info] [CMD]: Aaron_Fang used command /b tar gua kasih 10
[2025-07-26T16:38:23+0700] [Info] [CMD]: Aaron_Fang used command /b tar gua kasih 10k
[2025-07-26T16:38:25+0700] [Info] [CMD]: Tata_Wazoski used command /b jadi apa tuganya???
[2025-07-26T16:38:31+0700] [Info] [CMD]: Aaron_Fang used command /b ramein ini server
[2025-07-26T16:38:38+0700] [Info] [CMD]: Aaron_Fang used command /b post di tiktok sama facebook
[2025-07-26T16:38:44+0700] [Info] [CMD]: Tata_Wazoski used command /b ig
[2025-07-26T16:38:51+0700] [Info] [CMD]: Aaron_Fang used command /b ig gak begitu rame
[2025-07-26T16:38:53+0700] [Info] [CMD]: Aaron_Fang used command /b tiktok aja
[2025-07-26T16:38:54+0700] [Info] [CMD]: Tata_Wazoski used command /b g mau aku yang laen
[2025-07-26T16:38:55+0700] [Info] [CMD]: Aaron_Fang used command /b sering update gitu
[2025-07-26T16:39:06+0700] [Info] [CMD]: Aaron_Fang used command /b pokoknya tiktok
[2025-07-26T16:39:08+0700] [Info] [CMD]: Yuki_Haruto used command /lay 3
[2025-07-26T16:39:11+0700] [Info] [CMD]: Yuki_Haruto used command /lay 3
[2025-07-26T16:39:11+0700] [Info] [CMD]: Tata_Wazoski used command /b gua g ada main fb sama tik tok
[2025-07-26T16:39:13+0700] [Info] [CMD]: Aaron_Fang used command /b kalo gak, gak di kasih 10k
[2025-07-26T16:39:15+0700] [Info] [CMD]: Mark_Olrando used command /b aku gk tau
[2025-07-26T16:39:18+0700] [Info] [CMD]: Aaron_Fang used command /b ya buat bisa
[2025-07-26T16:39:39+0700] [Info] [CMD]: Tata_Wazoski used command /b ganti sini 200$ 
[2025-07-26T16:39:44+0700] [Info] [CMD]: Mark_Olrando used command /b gk tau
[2025-07-26T16:39:44+0700] [Info] [CMD]: Aaron_Fang used command /b tar gua ganti
[2025-07-26T16:39:51+0700] [Info] [CMD]: Tata_Wazoski used command /b gua lagi kerja lu tp kan
[2025-07-26T16:39:52+0700] [Info] [CMD]: Aaron_Fang used command /b bisa gak?
[2025-07-26T16:39:57+0700] [Info] [CMD]: Tata_Wazoski used command /b gak terima gua
[2025-07-26T16:40:03+0700] [Info] [CMD]: Yuki_Haruto used command /b ssttt
[2025-07-26T16:40:03+0700] [Info] [CMD]: Tata_Wazoski used command /b bisa
[2025-07-26T16:40:08+0700] [Info] [CMD]: Aaron_Fang used command /b post di tiktok?
[2025-07-26T16:40:15+0700] [Info] [CMD]: Tata_Wazoski used command /b kasih aba aba dlu lah syang
[2025-07-26T16:40:16+0700] [Info] [CMD]: Aaron_Fang used command /b nomor wa lu pada berapa?
[2025-07-26T16:40:26+0700] [Info] [CMD]: Tata_Wazoski used command /b saya g ada tik tok
[2025-07-26T16:40:28+0700] [Info] [CMD]: Yuki_Haruto used command /b hp rusak :v
[2025-07-26T16:40:33+0700] [Info] [CMD]: Aaron_Fang used command /b irfan bisa bantu juga gak?
[2025-07-26T16:40:33+0700] [Info] [CMD]: Tata_Wazoski used command /b g ada hp saya
[2025-07-26T16:40:40+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-26T16:40:42+0700] [Info] [CMD]: Aaron_Fang used command /b yaudah posting di pc aja
[2025-07-26T16:40:44+0700] [Info] [CMD]: Mark_Olrando used command /b g adatik tok]
[2025-07-26T16:40:48+0700] [Info] [CMD]: Yuki_Haruto used command /b hp ku rusak
[2025-07-26T16:40:49+0700] [Info] [CMD]: Aaron_Fang used command /toggle
[2025-07-26T16:40:56+0700] [Info] [CMD]: Yuki_Haruto used command /b ada nya cuma fb 
[2025-07-26T16:40:56+0700] [Info] [CMD]: Aaron_Fang used command /refill 0
[2025-07-26T16:40:57+0700] [Info] [CMD]: Aaron_Fang used command /refill 1
[2025-07-26T16:40:58+0700] [Info] [CMD]: Aaron_Fang used command /refill 2
[2025-07-26T16:40:59+0700] [Info] [CMD]: Aaron_Fang used command /refill 3
[2025-07-26T16:41:09+0700] [Info] [CMD]: Mark_Olrando used command /b apa
[2025-07-26T16:41:10+0700] [Info] [CMD]: Aaron_Fang used command /b buat tiktok gua kasih 10k
[2025-07-26T16:41:12+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (reason %d)." < 6456495
[2025-07-26T16:41:20+0700] [Info] [CMD]: Aaron_Fang used command /revive 3
[2025-07-26T16:41:23+0700] [Info] [CMD]: Yuki_Haruto used command /bring 2
[2025-07-26T16:41:28+0700] [Info] [CMD]: Aaron_Fang used command /b lu juga jangan mukul?
[2025-07-26T16:41:28+0700] [Info] [CMD]: Yuki_Haruto used command /bring 3
[2025-07-26T16:41:29+0700] [Info] [CMD]: Tata_Wazoski used command /b sorry terlanjur emosi
[2025-07-26T16:41:31+0700] [Info] [CMD]: Mark_Olrando used command /b ya uda aku gk mau
[2025-07-26T16:41:31+0700] [Info] [CMD]: Aaron_Fang used command /b bisa gak?
[2025-07-26T16:41:37+0700] [Info] [CMD]: Tata_Wazoski used command /b bisa
[2025-07-26T16:41:43+0700] [Info] [CMD]: Aaron_Fang used command /b elu aldo
[2025-07-26T16:41:45+0700] [Info] [CMD]: Tata_Wazoski used command /b tapi dia terlalu kelewatan
[2025-07-26T16:41:48+0700] [Info] [kill] Tata_Wazoski killed Aaron_Fang Fist
[2025-07-26T16:41:48+0700] [Info] [AFK] Player Aaron_Fang (ID: 0) keluar dari mode AFK
[2025-07-26T16:41:48+0700] [Info] [death] Aaron_Fang died 255
[2025-07-26T16:41:54+0700] [Info] [kill] Tata_Wazoski killed Yuki_Haruto Fist
[2025-07-26T16:41:54+0700] [Info] [death] Yuki_Haruto died 255
[2025-07-26T16:41:56+0700] [Info] [death] Tata_Wazoski died 255
[2025-07-26T16:42:00+0700] [Info] [death] Tata_Wazoski died 255
[2025-07-26T16:42:15+0700] [Info] [CMD]: Mark_Olrando used command /b ya 
[2025-07-26T16:42:24+0700] [Info] [CMD]: Mark_Olrando used command /b aku dimana
[2025-07-26T16:42:33+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-26T16:42:33+0700] [Info] [part] Aaron_Fang has left the server (0:1)
[2025-07-26T16:42:35+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-26T16:42:35+0700] [Info] [part] Yuki_Haruto has left the server (2:1)
[2025-07-26T16:42:44+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-26T16:42:44+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e57ec0087)
[2025-07-26T16:42:44+0700] [Info] [connection] incoming connection: **************:55274 id: 0
[2025-07-26T16:42:44+0700] [Info] [join] FanID has joined the server (0:**************)
[2025-07-26T16:42:48+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:55275)
[2025-07-26T16:43:41+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (*************) ...
[2025-07-26T16:43:41+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (1e23ba67acb86b8c)
[2025-07-26T16:43:41+0700] [Info] [connection] incoming connection: *************:53583 id: 2
[2025-07-26T16:43:41+0700] [Info] [join] dflx has joined the server (2:*************)
[2025-07-26T16:45:31+0700] [Info] [part] Mark_Olrando has left the server (3:1)
[2025-07-26T16:45:43+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-26T16:45:43+0700] [Info] [part] Tata_Wazoski has left the server (1:1)
[2025-07-26T16:45:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T16:45:52+0700] [Info] [connection] incoming connection: **************:59481 id: 1
[2025-07-26T16:45:52+0700] [Info] [join] Aldo has joined the server (1:**************)
[2025-07-26T16:46:22+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 1
[2025-07-26T16:46:33+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-26T16:46:36+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-26T16:46:49+0700] [Info] [CMD]: Yuki_Haruto used command /unfreeze 1
[2025-07-26T16:46:59+0700] [Info] [CMD]: Mark_Olrando used command /v 
[2025-07-26T16:47:04+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T16:47:16+0700] [Info] [CMD]: Yuki_Haruto used command /b /v list
[2025-07-26T16:47:22+0700] [Info] [CMD]: Mark_Olrando used command /v list
[2025-07-26T16:47:30+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 22
[2025-07-26T16:47:35+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 22
[2025-07-26T16:47:38+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 22
[2025-07-26T16:47:41+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 22
[2025-07-26T16:47:42+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T16:47:44+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 22
[2025-07-26T16:47:47+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 22
[2025-07-26T16:47:47+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T16:47:51+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T16:47:52+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 22
[2025-07-26T16:47:57+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T16:48:20+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-26T16:48:22+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-26T16:48:23+0700] [Info] [CMD]: Aaron_Fang used command /goto 1
[2025-07-26T16:48:25+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-26T16:48:27+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-26T16:48:32+0700] [Info] [CMD]: Aaron_Fang used command /b gua kasih duit sini
[2025-07-26T16:48:35+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-26T16:48:42+0700] [Info] [CMD]: Aaron_Fang used command /pay 1 200
[2025-07-26T16:48:42+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-26T16:48:42+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-26T16:48:52+0700] [Info] [CMD]: Aaron_Fang used command /goto 0
[2025-07-26T16:48:58+0700] [Info] [CMD]: Aaron_Fang used command /b yuiki
[2025-07-26T16:49:01+0700] [Info] [CMD]: Aaron_Fang used command /b dc bisa gak
[2025-07-26T16:49:02+0700] [Info] [CMD]: Mark_Olrando used command /o teq
[2025-07-26T16:49:13+0700] [Info] [CMD]: Yuki_Haruto used command /b bising di sini bng 
[2025-07-26T16:49:16+0700] [Info] [CMD]: Yuki_Haruto used command /b lagi rame
[2025-07-26T16:49:25+0700] [Info] [CMD]: Aaron_Fang used command /b gpp
[2025-07-26T16:50:08+0700] [Info] [CMD]: Aaron_Fang used command /b ada hs kan lu
[2025-07-26T16:56:59+0700] [Info] [AFK] Player Aaron_Fang (ID: 2) masuk mode AFK
[2025-07-26T16:57:19+0700] [Info] [AFK] Player Yuki_Haruto (ID: 0) masuk mode AFK
[2025-07-26T17:00:26+0700] [Info] [AFK] Player Mark_Olrando (ID: 1) masuk mode AFK
[2025-07-26T17:02:01+0700] [Info] [CMD]: Aaron_Fang used command /afk sxfqnx
[2025-07-26T17:02:01+0700] [Info] [AFK] Player Aaron_Fang (ID: 2) keluar dari mode AFK
[2025-07-26T17:02:06+0700] [Info] [CMD]: Aaron_Fang used command /refill 2
[2025-07-26T17:06:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T17:07:07+0700] [Info] [AFK] Player Aaron_Fang (ID: 2) masuk mode AFK
[2025-07-26T17:09:20+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-26T17:09:20+0700] [Info] [AFK] Player Aaron_Fang (ID: 2) disconnect saat AFK - data telah di-restore
[2025-07-26T17:09:20+0700] [Info] [part] Aaron_Fang has left the server (2:1)
[2025-07-26T17:16:40+0700] [Info] [AFK] Player Mark_Olrando (ID: 1) disconnect saat AFK - data telah di-restore
[2025-07-26T17:16:40+0700] [Info] [part] Mark_Olrando has left the server (1:1)
[2025-07-26T17:17:07+0700] [Info] [CMD]: Yuki_Haruto used command /afk 6TXCOR
[2025-07-26T17:17:07+0700] [Info] [AFK] Player Yuki_Haruto (ID: 0) keluar dari mode AFK
[2025-07-26T17:17:16+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-26T17:17:16+0700] [Info] [part] Yuki_Haruto has left the server (0:1)
[2025-07-26T17:27:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T17:52:05+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (************) ...
[2025-07-26T17:52:05+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (ca3a622970e0cdfc)
[2025-07-26T17:52:05+0700] [Info] [connection] incoming connection: ************:44433 id: 0
[2025-07-26T17:52:05+0700] [Info] [join] ANES_BITI has joined the server (0:************)
[2025-07-26T17:52:10+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:55003)
[2025-07-26T17:53:00+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-26T17:53:00+0700] [Info] [part] ANES_BITI has left the server (0:0)
[2025-07-26T18:25:57+0700] [Info] [connection] incoming connection: *************:50603 id: 0
[2025-07-26T18:25:57+0700] [Info] [join] Blood has joined the server (0:*************)
[2025-07-26T18:25:57+0700] [Info] [part] Blood has left the server (0:2)
[2025-07-26T18:26:33+0700] [Info] [connection] incoming connection: *************:52157 id: 0
[2025-07-26T18:26:33+0700] [Info] [join] Blood has joined the server (0:*************)
[2025-07-26T18:26:33+0700] [Info] [part] Blood has left the server (0:2)
[2025-07-26T18:26:48+0700] [Info] [connection] incoming connection: *************:62687 id: 0
[2025-07-26T18:26:48+0700] [Info] [join] Blood has joined the server (0:*************)
[2025-07-26T18:26:48+0700] [Info] [part] Blood has left the server (0:2)
[2025-07-26T18:27:03+0700] [Info] [connection] incoming connection: *************:62689 id: 0
[2025-07-26T18:27:03+0700] [Info] [join] Blood has joined the server (0:*************)
[2025-07-26T18:27:04+0700] [Info] [part] Blood has left the server (0:2)
[2025-07-26T18:27:19+0700] [Info] [connection] incoming connection: *************:49807 id: 0
[2025-07-26T18:27:19+0700] [Info] [join] Blood has joined the server (0:*************)
[2025-07-26T18:27:19+0700] [Info] [part] Blood has left the server (0:2)
[2025-07-26T18:27:54+0700] [Info] [connection] incoming connection: *************:59744 id: 0
[2025-07-26T18:27:54+0700] [Info] [join] Blood has joined the server (0:*************)
[2025-07-26T18:27:54+0700] [Info] [part] Blood has left the server (0:2)
[2025-07-26T18:28:47+0700] [Info] [connection] incoming connection: *************:50513 id: 0
[2025-07-26T18:28:47+0700] [Info] [join] Blood has joined the server (0:*************)
[2025-07-26T18:28:47+0700] [Info] [part] Blood has left the server (0:2)
[2025-07-26T22:00:44+0700] [Info] [connection] incoming connection: 112.215.230.114:21129 id: 0
[2025-07-26T22:00:44+0700] [Info] [join] nandogtg has joined the server (0:112.215.230.114)
[2025-07-26T22:01:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-26T22:01:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-26T22:02:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-26T22:02:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-26T22:05:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-26T22:06:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /collectrash
[2025-07-26T22:06:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /collectrash
[2025-07-26T22:07:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-26T22:07:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-26T22:07:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /droptrash
[2025-07-26T22:08:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-26T22:08:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-26T22:08:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-26T22:08:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /mission
[2025-07-26T22:09:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-26T22:09:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-26T22:09:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /droptrash
[2025-07-26T22:10:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:10:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:10:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:10:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:11:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:11:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:11:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:11:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:11:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:11:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:12:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:12:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-26T22:12:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:14:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-26T22:15:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-26T22:15:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-26T22:16:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:16:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:16:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:16:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:16:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:17:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:17:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:17:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:17:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:17:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-26T22:17:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:17:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:17:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:17:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:17:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:17:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:17:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:17:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:18:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:18:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:18:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-26T22:18:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-26T22:19:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-26T22:21:04+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-26T22:21:12+0700] [Info] [connection] incoming connection: **************:58345 id: 0
[2025-07-26T22:21:12+0700] [Info] [join] Kenzo_Wazoski has joined the server (0:**************)
[2025-07-26T22:21:23+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-26T22:21:36+0700] [Info] [connection] incoming connection: 112.215.230.114:26464 id: 0
[2025-07-26T22:21:36+0700] [Info] [join] nandogtg has joined the server (0:112.215.230.114)
[2025-07-26T22:21:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T22:22:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-26T22:25:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-26T22:27:11+0700] [Info] [connection] incoming connection: **************:58079 id: 1
[2025-07-26T22:27:11+0700] [Info] [join] FanID has joined the server (1:**************)
[2025-07-26T22:27:34+0700] [Info] [CMD]: Yuki_Haruto used command /goto 0
[2025-07-26T22:27:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /v list
[2025-07-26T22:27:47+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 14
[2025-07-26T22:28:36+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-26T22:29:13+0700] [Info] [connection] incoming connection: 112.215.230.114:54527 id: 0
[2025-07-26T22:29:13+0700] [Info] [join] nandogtg has joined the server (0:112.215.230.114)
[2025-07-26T22:29:25+0700] [Info] [part] nandogtg has left the server (0:0)
[2025-07-26T22:29:28+0700] [Info] [connection] incoming connection: 112.215.230.139:5245 id: 0
[2025-07-26T22:29:28+0700] [Info] [join] nandogtg has joined the server (0:112.215.230.139)
[2025-07-26T22:29:46+0700] [Info] [CMD]: Yuki_Haruto used command /goto
[2025-07-26T22:30:16+0700] [Info] [CMD]: Yuki_Haruto used command /goto prison
[2025-07-26T22:30:26+0700] [Info] [CMD]: Yuki_Haruto used command /goto ff
[2025-07-26T22:30:32+0700] [Info] [CMD]: Yuki_Haruto used command /goto is
[2025-07-26T22:30:38+0700] [Info] [CMD]: Yuki_Haruto used command /goto ls
[2025-07-26T22:30:38+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-26T22:30:46+0700] [Info] [CMD]: Yuki_Haruto used command /goto cityhall
[2025-07-26T22:31:13+0700] [Info] [CMD]: Yuki_Haruto used command /goto sanfierro
[2025-07-26T22:31:25+0700] [Info] [CMD]: Yuki_Haruto used command /goto
[2025-07-26T22:31:32+0700] [Info] [CMD]: Yuki_Haruto used command /goto lasventuras
[2025-07-26T22:32:26+0700] [Info] [CMD]: Yuki_Haruto used command /goto entrance
[2025-07-26T22:32:29+0700] [Info] [CMD]: Yuki_Haruto used command /goto entrance 1
[2025-07-26T22:32:45+0700] [Info] [CMD]: Yuki_Haruto used command /goto gate
[2025-07-26T22:32:49+0700] [Info] [CMD]: Yuki_Haruto used command /goto gate 1
[2025-07-26T22:33:01+0700] [Info] [CMD]: Yuki_Haruto used command /goto business
[2025-07-26T22:33:05+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 2
[2025-07-26T22:33:07+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 3
[2025-07-26T22:33:11+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 4
[2025-07-26T22:33:14+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 5
[2025-07-26T22:33:20+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 6
[2025-07-26T22:33:24+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 6
[2025-07-26T22:33:32+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-26T22:33:40+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-26T22:33:57+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-26T22:34:07+0700] [Info] [CMD]: Yuki_Haruto used command /v list
[2025-07-26T22:34:16+0700] [Info] [CMD]: Yuki_Haruto used command /v list
[2025-07-26T22:34:25+0700] [Info] [CMD]: Yuki_Haruto used command /bringcar 15
[2025-07-26T22:34:29+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-26T22:34:40+0700] [Info] [connection] incoming connection: 114.10.115.64:28222 id: 0
[2025-07-26T22:34:40+0700] [Info] [join] Jenarrey has joined the server (0:114.10.115.64)
[2025-07-26T22:34:51+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-26T22:34:53+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-26T22:34:55+0700] [Info] [connection] incoming connection: *************:14202 id: 2
[2025-07-26T22:34:55+0700] [Info] [join] dflx has joined the server (2:*************)
[2025-07-26T22:35:04+0700] [Info] [CMD]: Max_Jacob used command /ask
[2025-07-26T22:35:18+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T22:35:24+0700] [Info] [CMD]: Aaron_Fang used command /getcar
[2025-07-26T22:35:27+0700] [Info] [CMD]: Aaron_Fang used command /v list
[2025-07-26T22:35:30+0700] [Info] [CMD]: Aaron_Fang used command /getcar 23
[2025-07-26T22:35:44+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T22:35:46+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T22:35:49+0700] [Info] [CMD]: Yuki_Haruto used command /switch
[2025-07-26T22:35:49+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T22:36:00+0700] [Info] [part] Yuki_Haruto has left the server (1:1)
[2025-07-26T22:36:14+0700] [Info] [CMD]: Aaron_Fang used command /atune
[2025-07-26T22:36:31+0700] [Info] [connection] incoming connection: **************:61849 id: 1
[2025-07-26T22:36:31+0700] [Info] [join] Aldo has joined the server (1:**************)
[2025-07-26T22:36:53+0700] [Info] [CMD]: Aaron_Fang used command /bring 0
[2025-07-26T22:37:05+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T22:37:21+0700] [Info] [CMD]: Aaron_Fang used command /setweather 1
[2025-07-26T22:37:23+0700] [Info] [CMD]: Aaron_Fang used command /setweather 2
[2025-07-26T22:37:24+0700] [Info] [CMD]: Aaron_Fang used command /setweather 3
[2025-07-26T22:37:26+0700] [Info] [CMD]: Aaron_Fang used command /setweather 10
[2025-07-26T22:37:29+0700] [Info] [CMD]: Aaron_Fang used command /setweather 8
[2025-07-26T22:37:51+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T22:38:28+0700] [Info] [CMD]: Aaron_Fang used command /setarmour
[2025-07-26T22:38:31+0700] [Info] [CMD]: Aaron_Fang used command /setarmour 2 100
[2025-07-26T22:38:33+0700] [Info] [CMD]: Max_Jacob used command /spectate
[2025-07-26T22:38:36+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 100
[2025-07-26T22:38:38+0700] [Info] [CMD]: Max_Jacob used command /b /spectate
[2025-07-26T22:38:42+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T22:38:42+0700] [Info] [CMD]: Aaron_Fang used command /spectate 1
[2025-07-26T22:38:58+0700] [Info] [CMD]: Max_Jacob used command /mask
[2025-07-26T22:39:00+0700] [Info] [CMD]: Max_Jacob used command /items
[2025-07-26T22:39:02+0700] [Info] [CMD]: Aaron_Fang used command /spectate 1
[2025-07-26T22:39:02+0700] [Info] [CMD]: Max_Jacob used command /health
[2025-07-26T22:39:06+0700] [Info] [CMD]: Max_Jacob used command /inv
[2025-07-26T22:39:08+0700] [Info] [CMD]: Aaron_Fang used command /spectate off
[2025-07-26T22:39:09+0700] [Info] [CMD]: Max_Jacob used command /inventory
[2025-07-26T22:39:17+0700] [Info] [CMD]: Aaron_Fang used command /aslap 2
[2025-07-26T22:39:21+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T22:39:24+0700] [Info] [connection] incoming connection: 112.215.230.139:2167 id: 3
[2025-07-26T22:39:24+0700] [Info] [join] nandogtg has joined the server (3:112.215.230.139)
[2025-07-26T22:39:25+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T22:39:48+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T22:39:59+0700] [Info] [CMD]: Max_Jacob used command /b /setitem
[2025-07-26T22:40:02+0700] [Info] [CMD]: Max_Jacob used command /giveitem
[2025-07-26T22:40:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /o yang admin tolongin
[2025-07-26T22:40:13+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T22:40:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 0 0
[2025-07-26T22:40:19+0700] [Info] [CMD]: Aaron_Fang used command /setitem
[2025-07-26T22:40:20+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T22:40:21+0700] [Info] [CMD]: Aaron_Fang used command /setitem 0
[2025-07-26T22:40:28+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T22:40:36+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T22:40:41+0700] [Info] [CMD]: Max_Jacob used command /mask
[2025-07-26T22:40:41+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 2 bang tolongin bug ini
[2025-07-26T22:40:47+0700] [Info] [chat] [Max_Jacob]: gas
[2025-07-26T22:40:50+0700] [Info] [CMD]: Aaron_Fang used command /togmask
[2025-07-26T22:40:52+0700] [Info] [CMD]: Aaron_Fang used command /mask
[2025-07-26T22:41:00+0700] [Info] [CMD]: Aaron_Fang used command /setitem 2
[2025-07-26T22:41:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 2 nyangkut
[2025-07-26T22:41:06+0700] [Info] [CMD]: Aaron_Fang used command /togmask
[2025-07-26T22:41:10+0700] [Info] [CMD]: Aaron_Fang used command /send
[2025-07-26T22:41:11+0700] [Info] [CMD]: Max_Jacob used command /inventory
[2025-07-26T22:41:14+0700] [Info] [CMD]: Aaron_Fang used command /send 3 hospital
[2025-07-26T22:41:17+0700] [Info] [CMD]: Max_Jacob used command /inventory
[2025-07-26T22:41:22+0700] [Info] [CMD]: Max_Jacob used command /b /mask
[2025-07-26T22:41:25+0700] [Info] [CMD]: Aaron_Fang used command /mask
[2025-07-26T22:41:25+0700] [Info] [part] Kenzo_Wazoski has left the server (3:0)
[2025-07-26T22:41:31+0700] [Info] [connection] incoming connection: 112.215.230.139:48702 id: 3
[2025-07-26T22:41:31+0700] [Info] [join] nandogtg has joined the server (3:112.215.230.139)
[2025-07-26T22:41:33+0700] [Info] [CMD]: Aaron_Fang used command /setitem 2
[2025-07-26T22:41:35+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T22:41:38+0700] [Info] [CMD]: Aaron_Fang used command /mask
[2025-07-26T22:41:42+0700] [Info] [CMD]: Aaron_Fang used command /togmaskw
[2025-07-26T22:41:49+0700] [Info] [CMD]: Aaron_Fang used command /m ask
[2025-07-26T22:41:51+0700] [Info] [CMD]: Aaron_Fang used command /mask
[2025-07-26T22:41:57+0700] [Info] [CMD]: Max_Jacob used command /setmask
[2025-07-26T22:41:59+0700] [Info] [CMD]: Max_Jacob used command /stats
[2025-07-26T22:42:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-26T22:42:03+0700] [Info] [CMD]: Max_Jacob used command /stats
[2025-07-26T22:42:19+0700] [Info] [CMD]: Aaron_Fang used command /buy
[2025-07-26T22:42:22+0700] [Info] [CMD]: Max_Jacob used command /mypv
[2025-07-26T22:42:22+0700] [Info] [CMD]: Aaron_Fang used command /buy
[2025-07-26T22:42:25+0700] [Info] [CMD]: Max_Jacob used command /v list
[2025-07-26T22:42:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 2 bang tp inn kendaraan ku bang
[2025-07-26T22:42:29+0700] [Info] [CMD]: Max_Jacob used command /v list
[2025-07-26T22:42:32+0700] [Info] [CMD]: Max_Jacob used command /v
[2025-07-26T22:42:38+0700] [Info] [CMD]: Max_Jacob used command /v unstuck
[2025-07-26T22:42:39+0700] [Info] [CMD]: Aaron_Fang used command /buy
[2025-07-26T22:42:40+0700] [Info] [CMD]: Max_Jacob used command /gps
[2025-07-26T22:42:42+0700] [Info] [CMD]: Max_Jacob used command /gps
[2025-07-26T22:42:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T22:42:44+0700] [Info] [CMD]: Aaron_Fang used command /mask
[2025-07-26T22:42:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 2 bug aku tolongin
[2025-07-26T22:42:50+0700] [Info] [CMD]: Aaron_Fang used command /goto 3
[2025-07-26T22:42:56+0700] [Info] [CMD]: Aaron_Fang used command /v list 3
[2025-07-26T22:43:00+0700] [Info] [CMD]: Aaron_Fang used command /getcar 18
[2025-07-26T22:43:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /b anjir leduk  tibatiba
[2025-07-26T22:43:06+0700] [Info] [CMD]: Aaron_Fang used command /spectate 1
[2025-07-26T22:43:11+0700] [Info] [CMD]: Max_Jacob used command /trunk
[2025-07-26T22:43:14+0700] [Info] [CMD]: Max_Jacob used command /v trunk
[2025-07-26T22:43:28+0700] [Info] [CMD]: Aaron_Fang used command /spectate off
[2025-07-26T22:43:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /b gimana ni keluar nya?
[2025-07-26T22:43:38+0700] [Info] [CMD]: Aaron_Fang used command /goto 0
[2025-07-26T22:43:42+0700] [Info] [CMD]: Max_Jacob used command /v trunk
[2025-07-26T22:43:44+0700] [Info] [CMD]: Max_Jacob used command /lock
[2025-07-26T22:43:46+0700] [Info] [CMD]: Aaron_Fang used command /givegun
[2025-07-26T22:43:48+0700] [Info] [CMD]: Max_Jacob used command /v trunk
[2025-07-26T22:43:50+0700] [Info] [CMD]: Aaron_Fang used command /givewep
[2025-07-26T22:43:50+0700] [Info] [CMD]: Mark_Olrando used command /lock\
[2025-07-26T22:43:53+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T22:43:57+0700] [Info] [CMD]: Max_Jacob used command /v lock
[2025-07-26T22:44:00+0700] [Info] [CMD]: Aaron_Fang used command /givewep 2 24 100
[2025-07-26T22:44:03+0700] [Info] [CMD]: Aaron_Fang used command /givewep 2 24 1000
[2025-07-26T22:44:07+0700] [Info] [chat] [Kenzo_Wazoski]: gimana ni
[2025-07-26T22:44:08+0700] [Info] [CMD]: Mark_Olrando used command /phone
[2025-07-26T22:44:11+0700] [Info] [CMD]: Mark_Olrando used command /phone
[2025-07-26T22:44:11+0700] [Info] [CMD]: Max_Jacob used command /open
[2025-07-26T22:44:22+0700] [Info] [CMD]: Mark_Olrando used command /usedrug cocaine
[2025-07-26T22:44:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /o p
[2025-07-26T22:45:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 2 tp inn ulang aj biar engga ngdbug
[2025-07-26T22:45:23+0700] [Info] [CMD]: Aaron_Fang used command /pm 3 tpin kemana
[2025-07-26T22:46:06+0700] [Info] [CMD]: Aaron_Fang used command /s ANGKAT TANGAN
[2025-07-26T22:46:07+0700] [Info] [part] Kenzo_Wazoski has left the server (3:0)
[2025-07-26T22:46:13+0700] [Info] [CMD]: Aaron_Fang used command /S Angkat tangan
[2025-07-26T22:46:32+0700] [Info] [chat] [Mark_Olrando]: ooo
[2025-07-26T22:46:36+0700] [Info] [chat] [Mark_Olrando]: apa
[2025-07-26T22:46:42+0700] [Info] [CMD]: Max_Jacob used command /s angkat tangan
[2025-07-26T22:46:43+0700] [Info] [chat] [Aaron_Fang]: Serahin duit lu
[2025-07-26T22:46:44+0700] [Info] [chat] [Mark_Olrando]: aku gk sala
[2025-07-26T22:46:53+0700] [Info] [chat] [Aaron_Fang]: Gua robber
[2025-07-26T22:46:54+0700] [Info] [chat] [Mark_Olrando]: hh
[2025-07-26T22:46:56+0700] [Info] [chat] [Aaron_Fang]: Serahin duit lu
[2025-07-26T22:47:02+0700] [Info] [sv:dbg:network:connect] : connecting player (3) with address (**************) ...
[2025-07-26T22:47:02+0700] [Info] [sv:dbg:network:connect] : player (3) assigned key (93428a6e1278a4c5)
[2025-07-26T22:47:02+0700] [Info] [connection] incoming connection: **************:53095 id: 3
[2025-07-26T22:47:02+0700] [Info] [join] FanID has joined the server (3:**************)
[2025-07-26T22:47:05+0700] [Info] [part] Mark_Olrando has left the server (1:1)
[2025-07-26T22:47:06+0700] [Info] [sv:dbg:network:receive] : player (3) identified (port:60916)
[2025-07-26T22:47:26+0700] [Info] [chat] [Max_Jacob]: ada kenzo di dc
[2025-07-26T22:47:26+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-26T22:47:32+0700] [Info] [connection] incoming connection: **************:56289 id: 1
[2025-07-26T22:47:32+0700] [Info] [join] Aldo has joined the server (1:**************)
[2025-07-26T22:47:43+0700] [Info] [CMD]: Max_Jacob used command /stats
[2025-07-26T22:48:01+0700] [Info] [CMD]: Max_Jacob used command /reports
[2025-07-26T22:48:05+0700] [Info] [connection] incoming connection: 103.105.55.143:52322 id: 4
[2025-07-26T22:48:05+0700] [Info] [join] Qwerty has joined the server (4:103.105.55.143)
[2025-07-26T22:48:20+0700] [Info] [CMD]: Yuki_Haruto used command /goto 2
[2025-07-26T22:48:34+0700] [Info] [part] Qwerty has left the server (4:0)
[2025-07-26T22:48:45+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-26T22:49:04+0700] [Info] [CMD]: Max_Jacob used command /open
[2025-07-26T22:49:05+0700] [Info] [connection] incoming connection: 103.105.55.143:49390 id: 4
[2025-07-26T22:49:05+0700] [Info] [join] Qwerty has joined the server (4:103.105.55.143)
[2025-07-26T22:49:22+0700] [Info] [CMD]: Aaron_Fang used command /me take out deagle with right hands and ready to shoot
[2025-07-26T22:49:39+0700] [Info] [CMD]: Aaron_Fang used command /s Ini perampokan
[2025-07-26T22:49:42+0700] [Info] [CMD]: Aaron_Fang used command /s Angkat tangan
[2025-07-26T22:49:46+0700] [Warning] Insufficient specifiers given to `format`: "Update Character {00FFEE}%s" < 2
[2025-07-26T22:49:57+0700] [Info] [CMD]: Max_Jacob used command /finvite
[2025-07-26T22:49:58+0700] [Info] [CMD]: Aaron_Fang used command /s Angkat tangan
[2025-07-26T22:49:59+0700] [Info] [CMD]: Max_Jacob used command /finvite 4
[2025-07-26T22:50:05+0700] [Warning] Insufficient specifiers given to `format`: "Update Character {00FFEE}%s" < 2
[2025-07-26T22:50:09+0700] [Info] [CMD]: Yuki_Haruto used command /b anime nya apa tuh
[2025-07-26T22:50:15+0700] [Info] [CMD]: Aaron_Fang used command /b /handsup
[2025-07-26T22:50:19+0700] [Warning] Insufficient specifiers given to `format`: "Update Character {00FFEE}%s" < 2
[2025-07-26T22:50:21+0700] [Info] [CMD]: Mark_Olrando used command /usedrug cocaine
[2025-07-26T22:50:21+0700] [Info] [CMD]: Yuki_Haruto used command /handsup
[2025-07-26T22:50:23+0700] [Info] [CMD]: Max_Jacob used command /get
[2025-07-26T22:50:33+0700] [Info] [CMD]: Aaron_Fang used command /me save gun to his pocket
[2025-07-26T22:50:48+0700] [Info] [CMD]: Aaron_Fang used command /me risk everything in Yuki pocket with both hands
[2025-07-26T22:50:52+0700] [Info] [CMD]: Aaron_Fang used command /frisk 3
[2025-07-26T22:51:14+0700] [Info] [CMD]: Aaron_Fang used command /do Ada perlawan?
[2025-07-26T22:51:34+0700] [Info] [CMD]: Aaron_Fang used command /frisk 3
[2025-07-26T22:51:43+0700] [Info] [CMD]: Yuki_Haruto used command /aprove frisk
[2025-07-26T22:51:48+0700] [Info] [CMD]: Yuki_Haruto used command /approve frisk
[2025-07-26T22:52:00+0700] [Info] [chat] [Aaron_Fang]: Serahin uang lu 100$
[2025-07-26T22:52:06+0700] [Info] [chat] [Aaron_Fang]: Kalo gak lu mati
[2025-07-26T22:52:06+0700] [Info] [CMD]: Max_Jacob used command /s KELUAR
[2025-07-26T22:52:09+0700] [Info] [chat] [Yuki_Haruto]: miskin saya mass
[2025-07-26T22:52:18+0700] [Info] [chat] [Aaron_Fang]: Udah cepet serahin 100$
[2025-07-26T22:52:18+0700] [Info] [CMD]: Yuki_Haruto used command /handsup
[2025-07-26T22:52:49+0700] [Info] [CMD]: Max_Jacob used command /online
[2025-07-26T22:52:50+0700] [Info] [CMD]: Aaron_Fang used command /do /pay 2 100
[2025-07-26T22:52:57+0700] [Info] [CMD]: Yuki_Haruto used command /pay 2 100
[2025-07-26T22:52:57+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-26T22:53:04+0700] [Info] [chat] [Aaron_Fang]: Oke lu selamat kali ini
[2025-07-26T22:53:06+0700] [Info] [CMD]: Max_Jacob used command /tag
[2025-07-26T22:53:11+0700] [Info] [CMD]: Aaron_Fang used command /lock
[2025-07-26T22:53:15+0700] [Info] [CMD]: Max_Jacob used command /me saves his gun to back
[2025-07-26T22:53:21+0700] [Info] [CMD]: Max_Jacob used command /tag create
[2025-07-26T22:53:36+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T22:53:45+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-26T22:53:53+0700] [Info] [CMD]: Aaron_Fang used command /tag
[2025-07-26T22:54:00+0700] [Info] [CMD]: Aaron_Fang used command /tag create
[2025-07-26T22:54:21+0700] [Info] [CMD]: Yuki_Haruto used command /handsup
[2025-07-26T22:54:30+0700] [Info] [CMD]: Yuki_Haruto used command /b /handsup
[2025-07-26T22:54:43+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-26T22:54:49+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-26T22:55:04+0700] [Info] [CMD]: Max_Jacob used command /tag
[2025-07-26T22:55:11+0700] [Info] [CMD]: Max_Jacob used command /tag track
[2025-07-26T22:55:16+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-26T22:55:19+0700] [Info] [CMD]: Max_Jacob used command /tag
[2025-07-26T22:55:19+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-26T22:55:21+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-26T22:55:31+0700] [Info] [CMD]: Yuki_Haruto used command /sleep
[2025-07-26T22:55:34+0700] [Info] [CMD]: Yuki_Haruto used command /sleep 1
[2025-07-26T22:55:36+0700] [Info] [CMD]: Yuki_Haruto used command /sleep 1
[2025-07-26T22:55:36+0700] [Info] [CMD]: Max_Jacob used command /tag create
[2025-07-26T22:55:39+0700] [Info] [CMD]: Yuki_Haruto used command /sleep 2
[2025-07-26T22:55:46+0700] [Info] [CMD]: Yuki_Haruto used command /sleep 2
[2025-07-26T22:55:54+0700] [Info] [CMD]: Yuki_Haruto used command /sleep 2
[2025-07-26T22:55:55+0700] [Info] [CMD]: Mark_Olrando used command /drink water
[2025-07-26T22:55:59+0700] [Info] [CMD]: Mark_Olrando used command /drink water
[2025-07-26T22:56:01+0700] [Info] [CMD]: Yuki_Haruto used command /sleep 2
[2025-07-26T22:56:11+0700] [Info] [CMD]: Mark_Olrando used command /piss
[2025-07-26T22:56:16+0700] [Info] [CMD]: Yuki_Haruto used command /sleep 1
[2025-07-26T22:56:22+0700] [Info] [CMD]: Aaron_Fang used command /mask
[2025-07-26T22:56:25+0700] [Info] [sv:dbg:network:connect] : disconnecting player (3) ...
[2025-07-26T22:56:25+0700] [Info] [part] Yuki_Haruto has left the server (3:1)
[2025-07-26T22:56:25+0700] [Info] [CMD]: Max_Jacob used command /mask
[2025-07-26T22:56:50+0700] [Info] [CMD]: Samuel_Hoover used command /calimreward
[2025-07-26T22:56:59+0700] [Info] [CMD]: Samuel_Hoover used command /claimreward
[2025-07-26T22:56:59+0700] [Info] [REWARD] Samuel_Hoover (ID: 4) has claimed their character reward: $1,000.00 + Shancez vehicle (ID: 15).
[2025-07-26T22:58:11+0700] [Info] [CMD]: Max_Jacob used command /finvite 4
[2025-07-26T22:58:12+0700] [Info] [CMD]: Aaron_Fang used command /healall
[2025-07-26T22:58:20+0700] [Info] [CMD]: Mark_Olrando used command /usedrug cocaine
[2025-07-26T22:58:23+0700] [Info] [CMD]: Samuel_Hoover used command /approve faction
[2025-07-26T22:58:24+0700] [Info] [CMD]: Aaron_Fang used command /arepair 15
[2025-07-26T22:58:26+0700] [Info] [CMD]: Max_Jacob used command /frank
[2025-07-26T22:58:29+0700] [Info] [CMD]: Max_Jacob used command /frank 4 9
[2025-07-26T22:58:37+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:38+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:38+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:39+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:39+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:39+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:39+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:39+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:39+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:39+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:39+0700] [Info] [CMD]: Samuel_Hoover used command /open
[2025-07-26T22:58:42+0700] [Info] [CMD]: Max_Jacob used command /open
[2025-07-26T22:58:42+0700] [Info] [CMD]: Max_Jacob used command /open
[2025-07-26T22:58:52+0700] [Info] [CMD]: Aaron_Fang used command /arepair
[2025-07-26T22:59:00+0700] [Info] [CMD]: Aaron_Fang used command /arepair
[2025-07-26T22:59:14+0700] [Info] [CMD]: Aaron_Fang used command /arepair
[2025-07-26T22:59:21+0700] [Info] [CMD]: Aaron_Fang used command /arepairw
[2025-07-26T22:59:32+0700] [Info] [kill] Max_Jacob killed Samuel_Hoover AK47
[2025-07-26T23:00:02+0700] [Info] [CMD]: Aaron_Fang used command /givewep 30 1000
[2025-07-26T23:00:06+0700] [Info] [CMD]: Aaron_Fang used command /givewep 2 30 1000
[2025-07-26T23:00:15+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-26T23:00:27+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-26T23:00:29+0700] [Info] [CMD]: Aaron_Fang used command /goto 
[2025-07-26T23:00:32+0700] [Info] [CMD]: Max_Jacob used command /tazer
[2025-07-26T23:00:33+0700] [Info] [CMD]: Aaron_Fang used command /goto 0
[2025-07-26T23:00:40+0700] [Info] [part] Aaron_Fang has left the server (2:1)
[2025-07-26T23:00:42+0700] [Info] [CMD]: Max_Jacob used command /tazer
[2025-07-26T23:00:58+0700] [Info] [CMD]: Max_Jacob used command /heal
[2025-07-26T23:00:59+0700] [Info] [CMD]: Max_Jacob used command /revive
[2025-07-26T23:01:02+0700] [Info] [CMD]: Max_Jacob used command /help
[2025-07-26T23:01:09+0700] [Info] [CMD]: Max_Jacob used command /heal
[2025-07-26T23:01:14+0700] [Info] [CMD]: Max_Jacob used command /bandage
[2025-07-26T23:01:15+0700] [Info] [CMD]: Max_Jacob used command /bandage 4
[2025-07-26T23:01:48+0700] [Info] [CMD]: Max_Jacob used command /bandage 4
[2025-07-26T23:01:50+0700] [Info] [connection] incoming connection: *************:57833 id: 2
[2025-07-26T23:01:50+0700] [Info] [join] dflx has joined the server (2:*************)
[2025-07-26T23:01:51+0700] [Info] [CMD]: Max_Jacob used command /heal
[2025-07-26T23:01:55+0700] [Info] [CMD]: Max_Jacob used command /revive
[2025-07-26T23:01:56+0700] [Info] [CMD]: Max_Jacob used command /revive 4
[2025-07-26T23:01:58+0700] [Info] [CMD]: Max_Jacob used command /arevive
[2025-07-26T23:02:00+0700] [Info] [CMD]: Max_Jacob used command /heal
[2025-07-26T23:02:02+0700] [Info] [CMD]: Max_Jacob used command /editcmd
[2025-07-26T23:02:09+0700] [Info] [CMD]: Max_Jacob used command /editmissions
[2025-07-26T23:02:12+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:02:12+0700] [Info] [CMD]: Max_Jacob used command /panel
[2025-07-26T23:02:21+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:02:21+0700] [Info] [CMD]: Aaron_Fang used command /refil 2
[2025-07-26T23:02:28+0700] [Info] [CMD]: Aaron_Fang used command /heal all
[2025-07-26T23:02:31+0700] [Info] [CMD]: Aaron_Fang used command /healall
[2025-07-26T23:02:34+0700] [Info] [CMD]: Aaron_Fang used command /fill 2
[2025-07-26T23:02:36+0700] [Info] [CMD]: Max_Jacob used command /drink water
[2025-07-26T23:02:36+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:02:41+0700] [Info] [CMD]: Aaron_Fang used command /fill 2
[2025-07-26T23:02:43+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:02:55+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:02:57+0700] [Info] [CMD]: Aaron_Fang used command /kill
[2025-07-26T23:03:00+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2
[2025-07-26T23:03:04+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 100
[2025-07-26T23:03:07+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:03:07+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 0
[2025-07-26T23:03:11+0700] [Info] [CMD]: Aaron_Fang used command /revive 4
[2025-07-26T23:03:14+0700] [Info] [CMD]: Aaron_Fang used command /revive 2
[2025-07-26T23:03:19+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (reason %d)." < 6456495
[2025-07-26T23:03:20+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:03:22+0700] [Info] [CMD]: Aaron_Fang used command /unfreeze 2
[2025-07-26T23:03:25+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:03:27+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has killed %s (%s)." < 6456495
[2025-07-26T23:03:30+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:03:35+0700] [Info] [CMD]: Aaron_Fang used command /healall
[2025-07-26T23:03:35+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:03:40+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:03:40+0700] [Info] [CMD]: Aaron_Fang used command /setarmour 2 100
[2025-07-26T23:03:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T23:03:45+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 10000
[2025-07-26T23:03:50+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 10000000
[2025-07-26T23:03:53+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 10000000000000
[2025-07-26T23:03:53+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:04:06+0700] [Info] [CMD]: Aaron_Fang used command /sethp 2 10000000000000
[2025-07-26T23:04:07+0700] [Info] [kill] Max_Jacob killed Aaron_Fang AK47
[2025-07-26T23:04:07+0700] [Info] [death] Aaron_Fang died 255
[2025-07-26T23:04:14+0700] [Info] [part] Max_Jacob has left the server (0:1)
[2025-07-26T23:04:24+0700] [Info] [part] Samuel_Hoover has left the server (4:1)
[2025-07-26T23:04:29+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:04:32+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:04:41+0700] [Info] [part] Aaron_Fang has left the server (2:1)
[2025-07-26T23:05:01+0700] [Info] [CMD]: Mark_Olrando used command /v engine
[2025-07-26T23:05:02+0700] [Info] [CMD]: Mark_Olrando used command /v engine
[2025-07-26T23:05:08+0700] [Info] [CMD]: Mark_Olrando used command /v engine
[2025-07-26T23:05:23+0700] [Info] [part] Mark_Olrando has left the server (1:1)
[2025-07-26T23:05:46+0700] [Info] [connection] incoming connection: **************:56001 id: 0
[2025-07-26T23:05:46+0700] [Info] [join] Aldo has joined the server (0:**************)
[2025-07-26T23:07:06+0700] [Info] [part] Mark_Olrando has left the server (0:1)
[2025-07-26T23:07:26+0700] [Info] [connection] incoming connection: **************:55192 id: 0
[2025-07-26T23:07:26+0700] [Info] [join] Aldo has joined the server (0:**************)
[2025-07-26T23:10:44+0700] [Info] [CMD]: Mark_Olrando used command /usedrug cocaine
[2025-07-26T23:10:54+0700] [Info] [part] Mark_Olrando has left the server (0:1)
[2025-07-26T23:11:12+0700] [Info] [connection] incoming connection: **************:55128 id: 0
[2025-07-26T23:11:12+0700] [Info] [join] Aldo has joined the server (0:**************)
[2025-07-26T23:11:50+0700] [Info] [CMD]: Mark_Olrando used command /usedrug cocaine
[2025-07-26T23:12:02+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:12:44+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:13:03+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:13:14+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:13:27+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:13:44+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:14:32+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:15:47+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:16:26+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:17:53+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:18:09+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:19:53+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:20:05+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:21:59+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:23:26+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:23:35+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:24:21+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:24:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T23:24:48+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:25:44+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:25:51+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:26:06+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:28:55+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:33:12+0700] [Info] [CMD]: Mark_Olrando used command /usedrug cocaine
[2025-07-26T23:33:33+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:33:36+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:34:15+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:34:28+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:34:50+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:34:54+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:35:17+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:35:21+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:35:36+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:35:56+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:36:03+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:36:11+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:36:20+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:36:25+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:36:37+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:37:36+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:37:39+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:37:41+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:37:46+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:37:48+0700] [Info] [CMD]: Mark_Olrando used command /buy
[2025-07-26T23:37:52+0700] [Info] [CMD]: Mark_Olrando used command /pis
[2025-07-26T23:37:54+0700] [Info] [CMD]: Mark_Olrando used command /piss
[2025-07-26T23:39:51+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:41:45+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:42:03+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:42:14+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:43:39+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:43:52+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:44:20+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:44:29+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:44:38+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:44:45+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:44:52+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:45:02+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:45:08+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:45:29+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:45:38+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:45:43+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-26T23:45:49+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:45:57+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:46:06+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:46:11+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:46:15+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:46:22+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:46:26+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:46:30+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:46:34+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:46:38+0700] [Info] [CMD]: Mark_Olrando used command /furniture
[2025-07-26T23:48:19+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:48:28+0700] [Info] [CMD]: Mark_Olrando used command /loc
[2025-07-26T23:48:30+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:49:08+0700] [Info] [connection] incoming connection: 114.5.251.114:49791 id: 1
[2025-07-26T23:49:08+0700] [Info] [join] Djaa has joined the server (1:114.5.251.114)
[2025-07-26T23:49:19+0700] [Info] [CMD]: Mark_Olrando used command /refuel
[2025-07-26T23:50:37+0700] [Info] [CMD]: Mark_Olrando used command /deposit
[2025-07-26T23:50:39+0700] [Info] [CMD]: Mark_Olrando used command /deposit
[2025-07-26T23:50:50+0700] [Info] [CMD]: Mark_Olrando used command /deposit 400
[2025-07-26T23:52:47+0700] [Info] [CMD]: Mark_Olrando used command /lock
[2025-07-26T23:55:45+0700] [Info] [part] Mark_Olrando has left the server (0:1)
[2025-07-26T23:57:26+0700] [Info] [CMD]: Daniel_Valhein used command /robject
[2025-07-26T23:59:46+0700] [Info] [part] Daniel_Valhein has left the server (1:1)
[2025-07-27T05:06:26+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (************) ...
[2025-07-27T05:06:26+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (c01d63297383d7ac)
[2025-07-27T05:06:26+0700] [Info] [connection] incoming connection: ************:56834 id: 0
[2025-07-27T05:06:26+0700] [Info] [join] ANES_BITI has joined the server (0:************)
[2025-07-27T05:06:31+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:36065)
[2025-07-27T05:07:15+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-27T05:07:15+0700] [Info] [part] ANES_BITI has left the server (0:0)
[2025-07-27T06:10:54+0700] [Info] [connection] incoming connection: ************:4201 id: 0
[2025-07-27T06:10:54+0700] [Info] [join] Djaa has joined the server (0:************)
[2025-07-27T06:11:09+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-27T06:17:13+0700] [Info] [AFK] Player Daniel_Valhein (ID: 0) masuk mode AFK
[2025-07-27T06:23:18+0700] [Info] [AFK] Player Daniel_Valhein (ID: 0) disconnect saat AFK - data telah di-restore
[2025-07-27T06:23:18+0700] [Info] [part] Daniel_Valhein has left the server (0:1)
[2025-07-27T08:57:09+0700] [Info] [connection] incoming connection: 112.215.230.15:1055 id: 0
[2025-07-27T08:57:09+0700] [Info] [join] nandogtg has joined the server (0:112.215.230.15)
[2025-07-27T08:57:56+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-27T08:59:36+0700] [Info] [connection] incoming connection: 112.215.230.15:49158 id: 0
[2025-07-27T08:59:36+0700] [Info] [join] nandogtg has joined the server (0:112.215.230.15)
[2025-07-27T09:02:15+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-27T09:02:26+0700] [Info] [connection] incoming connection: 112.215.230.15:42275 id: 0
[2025-07-27T09:02:26+0700] [Info] [join] nandogtg has joined the server (0:112.215.230.15)
[2025-07-27T09:04:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-27T09:04:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-27T09:05:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:05:16+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12632
[2025-07-27T09:05:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:05:32+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12607
[2025-07-27T09:05:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:05:50+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12582
[2025-07-27T09:06:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:06:08+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12557
[2025-07-27T09:06:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:06:28+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12532
[2025-07-27T09:06:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:06:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:06:40+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12507
[2025-07-27T09:06:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:06:53+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12482
[2025-07-27T09:07:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:07:06+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12457
[2025-07-27T09:07:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:07:39+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12432
[2025-07-27T09:07:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:08:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:08:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T09:08:09+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12407
[2025-07-27T09:09:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T09:09:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-27T09:09:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-27T09:10:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /mission
[2025-07-27T09:10:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-27T09:12:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:12:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:12:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:12:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:12:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:13:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:13:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:13:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:13:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:13:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T09:13:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:13:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:13:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:13:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:13:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:14:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:14:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:14:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:14:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:14:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:14:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T09:14:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T09:15:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-27T09:15:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-27T09:15:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-27T09:16:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-27T09:16:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /pisz
[2025-07-27T09:16:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-27T09:22:45+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-27T09:22:56+0700] [Info] [connection] incoming connection: 112.215.230.15:48588 id: 0
[2025-07-27T09:22:57+0700] [Info] [join] nandogtg has joined the server (0:112.215.230.15)
[2025-07-27T09:23:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-27T09:25:39+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-27T11:02:53+0700] [Info] [connection] incoming connection: 140.213.32.106:20578 id: 0
[2025-07-27T11:02:53+0700] [Info] [join] nandogtg has joined the server (0:140.213.32.106)
[2025-07-27T11:03:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-27T11:09:45+0700] [Warning] Insufficient specifiers given to `format`: "ERROR:{FFFFFF} You have damaged the crate during the process, go to the marker to {F3FF02}deliver the crate {FFFFFF}again." < 1
[2025-07-27T11:10:43+0700] [Warning] Insufficient specifiers given to `format`: "ERROR:{FFFFFF} You have damaged the crate during the process, go to the marker to {F3FF02}deliver the crate {FFFFFF}again." < 1
[2025-07-27T11:16:41+0700] [Info] [connection] incoming connection: **************:55910 id: 1
[2025-07-27T11:16:41+0700] [Info] [join] habibisd has joined the server (1:**************)
[2025-07-27T11:17:25+0700] [Info] [CMD]: William_Kazuki used command /stats
[2025-07-27T11:17:29+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:18:11+0700] [Warning] Insufficient specifiers given to `format`: "ERROR:{FFFFFF} You have damaged the crate during the process, go to the marker to {F3FF02}deliver the crate {FFFFFF}again." < 1
[2025-07-27T11:18:11+0700] [Info] [CMD]: William_Kazuki used command /sellplant potato
[2025-07-27T11:18:14+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12686
[2025-07-27T11:19:11+0700] [Info] [CMD]: William_Kazuki used command /sellplant 
[2025-07-27T11:19:16+0700] [Info] [CMD]: William_Kazuki used command /sellplant  wheat
[2025-07-27T11:19:25+0700] [Info] [connection] incoming connection: **************:49788 id: 2
[2025-07-27T11:19:25+0700] [Info] [join] workid0110 has joined the server (2:**************)
[2025-07-27T11:19:37+0700] [Info] [CMD]: William_Kazuki used command /delayjob
[2025-07-27T11:19:45+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:19:57+0700] [Info] [CMD]: Mike_Alisster used command /v lock
[2025-07-27T11:20:35+0700] [Info] [CMD]: Mike_Alisster used command /delas
[2025-07-27T11:20:39+0700] [Info] [CMD]: Mike_Alisster used command /delay
[2025-07-27T11:20:40+0700] [Info] [CMD]: Mike_Alisster used command /delays
[2025-07-27T11:20:43+0700] [Info] [CMD]: Mike_Alisster used command /delay job
[2025-07-27T11:20:46+0700] [Info] [CMD]: Mike_Alisster used command /delayjob
[2025-07-27T11:21:05+0700] [Info] [CMD]: Mike_Alisster used command /v lock
[2025-07-27T11:21:07+0700] [Info] [CMD]: Mike_Alisster used command /v lock
[2025-07-27T11:21:09+0700] [Info] [part] Kenzo_Wazoski has left the server (0:0)
[2025-07-27T11:21:18+0700] [Info] [CMD]: Mike_Alisster used command /stats
[2025-07-27T11:21:24+0700] [Info] [CMD]: Mike_Alisster used command /stats
[2025-07-27T11:21:36+0700] [Info] [CMD]: Mike_Alisster used command /mysalary
[2025-07-27T11:21:38+0700] [Info] [CMD]: William_Kazuki used command /paytoll
[2025-07-27T11:21:38+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:21:47+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:48+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:52+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:53+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:53+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:54+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:55+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:56+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:57+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:57+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:57+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:58+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:21:58+0700] [Info] [CMD]: Mike_Alisster used command /open
[2025-07-27T11:22:36+0700] [Info] [CMD]: Mike_Alisster used command /scrapveh
[2025-07-27T11:22:53+0700] [Info] [CMD]: Mike_Alisster used command /scrapveh confirm
[2025-07-27T11:22:58+0700] [Info] [CMD]: Mike_Alisster used command /quitjob
[2025-07-27T11:23:04+0700] [Info] [CMD]: Mike_Alisster used command /quitjob
[2025-07-27T11:23:11+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:23:20+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:23:39+0700] [Info] [CMD]: William_Kazuki used command /withdraw 10000
[2025-07-27T11:23:41+0700] [Info] [part] Mike_Alisster has left the server (2:1)
[2025-07-27T11:23:42+0700] [Info] [CMD]: William_Kazuki used command /withdraw 1000
[2025-07-27T11:23:53+0700] [Info] [CMD]: William_Kazuki used command /withdraw
[2025-07-27T11:24:01+0700] [Info] [CMD]: William_Kazuki used command /withdraw 1
[2025-07-27T11:24:09+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:15+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:20+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:25+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:29+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:34+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:38+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:42+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:46+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:51+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:55+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:24:59+0700] [Info] [CMD]: William_Kazuki used command /atm
[2025-07-27T11:25:11+0700] [Info] [CMD]: William_Kazuki used command /mysalary
[2025-07-27T11:30:18+0700] [Info] [AFK] Player William_Kazuki (ID: 1) masuk mode AFK
[2025-07-27T11:34:38+0700] [Info] [CMD]: William_Kazuki used command /afk yhz5hn
[2025-07-27T11:34:38+0700] [Info] [AFK] Player William_Kazuki (ID: 1) keluar dari mode AFK
[2025-07-27T11:34:50+0700] [Info] [CMD]: William_Kazuki used command /claimed reward
[2025-07-27T11:34:55+0700] [Info] [CMD]: William_Kazuki used command /claimed rewards
[2025-07-27T11:35:04+0700] [Info] [CMD]: William_Kazuki used command /claimed reward
[2025-07-27T11:35:07+0700] [Info] [CMD]: William_Kazuki used command /claimed rewards
[2025-07-27T11:35:10+0700] [Info] [CMD]: William_Kazuki used command /claimeds rewards
[2025-07-27T11:35:32+0700] [Info] [CMD]: William_Kazuki used command /o bagai mana cara nengok kode rede?
[2025-07-27T11:37:12+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:37:36+0700] [Info] [CMD]: William_Kazuki used command /gps
[2025-07-27T11:38:20+0700] [Info] [CMD]: William_Kazuki used command /vbuy
[2025-07-27T11:38:24+0700] [Info] [CMD]: William_Kazuki used command /buy
[2025-07-27T11:38:30+0700] [Info] [CMD]: William_Kazuki used command /buy
[2025-07-27T11:38:33+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has purchased a %s for %s." < 6456495
[2025-07-27T11:39:05+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:39:08+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:39:15+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:39:19+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:39:38+0700] [Info] [CMD]: William_Kazuki used command /stats
[2025-07-27T11:41:25+0700] [Info] [CMD]: William_Kazuki used command /paytoll
[2025-07-27T11:41:25+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:41:51+0700] [Info] [CMD]: William_Kazuki used command /endjob
[2025-07-27T11:41:54+0700] [Info] [CMD]: William_Kazuki used command /endjob
[2025-07-27T11:41:59+0700] [Info] [CMD]: William_Kazuki used command /endtakejob
[2025-07-27T11:42:04+0700] [Info] [CMD]: William_Kazuki used command /end
[2025-07-27T11:42:14+0700] [Info] [CMD]: William_Kazuki used command /endjob
[2025-07-27T11:42:20+0700] [Info] [CMD]: William_Kazuki used command /takejob
[2025-07-27T11:42:32+0700] [Info] [CMD]: William_Kazuki used command /quitjob
[2025-07-27T11:42:36+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:42:43+0700] [Info] [CMD]: William_Kazuki used command /stats
[2025-07-27T11:43:04+0700] [Info] [CMD]: William_Kazuki used command /paytoll
[2025-07-27T11:43:04+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:44:27+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:44:36+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:45:47+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:45:47+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:45:48+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:46:33+0700] [Info] [CMD]: William_Kazuki used command /takejob
[2025-07-27T11:46:39+0700] [Info] [CMD]: William_Kazuki used command /stats
[2025-07-27T11:47:17+0700] [Info] [CMD]: William_Kazuki used command /me memeriksa mobil 
[2025-07-27T11:47:29+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:47:40+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:41+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:42+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:43+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:43+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:44+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:44+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:44+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:45+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:45+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:45+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:45+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:45+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:46+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:46+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:47+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:47+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:47+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:47+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:47+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:48+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:48+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:48+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:49+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:49+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:49+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:50+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:50+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:50+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:50+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:51+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:53+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:53+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:53+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:54+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:54+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:54+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:55+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:55+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:55+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:55+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:56+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:56+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:57+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:57+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:57+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:58+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:58+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:58+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:58+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:58+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:47:59+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:48:00+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:48:00+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:48:39+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:48:52+0700] [Info] [CMD]: William_Kazuki used command /lock
[2025-07-27T11:49:00+0700] [Info] [CMD]: William_Kazuki used command /hoop
[2025-07-27T11:49:04+0700] [Info] [CMD]: William_Kazuki used command /hood
[2025-07-27T11:49:06+0700] [Info] [CMD]: William_Kazuki used command /hoo
[2025-07-27T11:50:08+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:50:11+0700] [Info] [CMD]: William_Kazuki used command /faq
[2025-07-27T11:50:22+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:50:33+0700] [Info] [CMD]: William_Kazuki used command /changepass
[2025-07-27T11:50:40+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:50:54+0700] [Info] [CMD]: William_Kazuki used command /acc
[2025-07-27T11:51:00+0700] [Info] [CMD]: William_Kazuki used command /efitcc
[2025-07-27T11:51:08+0700] [Info] [CMD]: William_Kazuki used command /editacc
[2025-07-27T11:51:27+0700] [Info] [CMD]: William_Kazuki used command /showlicense
[2025-07-27T11:51:37+0700] [Info] [CMD]: William_Kazuki used command /oreinfo
[2025-07-27T11:51:42+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:51:44+0700] [Info] [CMD]: William_Kazuki used command /joblist
[2025-07-27T11:51:58+0700] [Info] [CMD]: William_Kazuki used command /mm
[2025-07-27T11:52:01+0700] [Info] [CMD]: William_Kazuki used command /mm
[2025-07-27T11:52:07+0700] [Info] [CMD]: William_Kazuki used command /joinduty
[2025-07-27T11:52:12+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:52:13+0700] [Info] [CMD]: William_Kazuki used command /joblist
[2025-07-27T11:52:17+0700] [Info] [CMD]: William_Kazuki used command /joblist
[2025-07-27T11:52:22+0700] [Info] [CMD]: William_Kazuki used command /jobduty
[2025-07-27T11:52:26+0700] [Info] [CMD]: William_Kazuki used command /mm
[2025-07-27T11:52:34+0700] [Info] [CMD]: William_Kazuki used command /hood
[2025-07-27T11:52:47+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:52:55+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:53:10+0700] [Info] [CMD]: William_Kazuki used command /hood
[2025-07-27T11:53:14+0700] [Info] [CMD]: William_Kazuki used command /hood open
[2025-07-27T11:53:16+0700] [Info] [CMD]: William_Kazuki used command /hood open
[2025-07-27T11:53:22+0700] [Info] [CMD]: William_Kazuki used command /hoodopens
[2025-07-27T11:53:27+0700] [Info] [CMD]: William_Kazuki used command /opens
[2025-07-27T11:53:29+0700] [Info] [CMD]: William_Kazuki used command /open
[2025-07-27T11:53:32+0700] [Info] [CMD]: William_Kazuki used command /openhood
[2025-07-27T11:53:37+0700] [Info] [CMD]: William_Kazuki used command /mm
[2025-07-27T11:53:52+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:54:05+0700] [Info] [CMD]: William_Kazuki used command /properties
[2025-07-27T11:54:11+0700] [Info] [CMD]: William_Kazuki used command /bindo
[2025-07-27T11:54:14+0700] [Info] [CMD]: William_Kazuki used command /binfon
[2025-07-27T11:54:16+0700] [Info] [CMD]: William_Kazuki used command /binfo
[2025-07-27T11:54:25+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:54:39+0700] [Info] [CMD]: William_Kazuki used command /modshop
[2025-07-27T11:54:44+0700] [Info] [CMD]: William_Kazuki used command /modshop
[2025-07-27T11:54:50+0700] [Info] [CMD]: William_Kazuki used command /myov
[2025-07-27T11:55:05+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:55:14+0700] [Info] [CMD]: William_Kazuki used command /abandon 
[2025-07-27T11:55:24+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:55:24+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has abandoned their %s." < 6456495
[2025-07-27T11:55:31+0700] [Info] [CMD]: William_Kazuki used command /stats
[2025-07-27T11:55:41+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:55:48+0700] [Info] [CMD]: William_Kazuki used command /gps
[2025-07-27T11:56:35+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:56:38+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:56:53+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:56:55+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:56:56+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:56:58+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:57:00+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:57:02+0700] [Info] [CMD]: William_Kazuki used command /gps
[2025-07-27T11:57:12+0700] [Info] [CMD]: William_Kazuki used command /mycar
[2025-07-27T11:57:21+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:57:21+0700] [Info] [CMD]: William_Kazuki used command /helps
[2025-07-27T11:57:32+0700] [Info] [CMD]: William_Kazuki used command /myov
[2025-07-27T11:57:43+0700] [Info] [CMD]: William_Kazuki used command /stats
[2025-07-27T11:57:51+0700] [Info] [CMD]: William_Kazuki used command /help
[2025-07-27T11:58:04+0700] [Info] [CMD]: William_Kazuki used command /saveveh
[2025-07-27T11:58:11+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:58:14+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:58:15+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:58:17+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:58:19+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:58:20+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:58:34+0700] [Info] [CMD]: William_Kazuki used command /v lock
[2025-07-27T11:58:45+0700] [Info] [CMD]: William_Kazuki used command /statsa
[2025-07-27T11:58:47+0700] [Info] [CMD]: William_Kazuki used command /stats
[2025-07-27T11:58:59+0700] [Info] [CMD]: William_Kazuki used command /claimreward
[2025-07-27T11:59:07+0700] [Info] [CMD]: William_Kazuki used command /abandpn
[2025-07-27T11:59:16+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:59:18+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:59:20+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T11:59:24+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:59:26+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T11:59:28+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T12:00:10+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T12:00:12+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T12:00:17+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T12:00:18+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T12:00:18+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T12:00:18+0700] [Info] [CMD]: William_Kazuki used command /abandon
[2025-07-27T12:00:25+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T12:00:42+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T12:00:44+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T12:00:45+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T12:00:46+0700] [Info] [CMD]: William_Kazuki used command /abandon confirm
[2025-07-27T12:01:04+0700] [Info] [part] William_Kazuki has left the server (1:1)
[2025-07-27T13:16:45+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-27T13:16:45+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6eb3a814fd)
[2025-07-27T13:16:45+0700] [Info] [connection] incoming connection: **************:65316 id: 0
[2025-07-27T13:16:45+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-27T13:16:47+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:65317)
[2025-07-27T13:18:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-27T13:18:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-27T13:18:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /joblist
[2025-07-27T13:18:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /gsp
[2025-07-27T13:18:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-27T13:19:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T13:19:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T13:20:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-27T13:20:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-27T13:20:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /droptrash
[2025-07-27T13:21:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T13:21:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T13:22:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T13:22:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T13:23:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-27T13:24:44+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-27T13:32:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-27T13:32:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-27T13:33:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-27T13:33:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-27T13:33:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-27T13:35:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-27T13:35:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T13:36:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T13:36:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-27T13:36:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-27T13:36:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-27T13:36:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-27T13:36:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-27T13:36:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-27T13:37:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T13:39:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /showid 0
[2025-07-27T13:39:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /showid
[2025-07-27T13:39:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-27T13:40:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /showidcard 0
[2025-07-27T13:41:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T13:41:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T13:42:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /mission
[2025-07-27T13:42:16+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-27T13:42:19+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-27T13:42:23+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-27T13:42:24+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e24069cff)
[2025-07-27T13:42:24+0700] [Info] [connection] incoming connection: **************:56864 id: 0
[2025-07-27T13:42:24+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-27T13:42:28+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:56865)
[2025-07-27T13:43:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /death
[2025-07-27T13:43:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T13:44:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-27T13:44:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /lastlogged
[2025-07-27T13:44:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /lastlogged 0
[2025-07-27T13:44:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-27T13:45:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /abandon
[2025-07-27T13:45:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /abandon 
[2025-07-27T13:45:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /saveveh
[2025-07-27T13:45:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /v
[2025-07-27T13:45:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-27T13:45:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /animcmds
[2025-07-27T13:45:44+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-27T13:46:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /cry
[2025-07-27T13:46:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /walk
[2025-07-27T13:46:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /walk 1
[2025-07-27T13:46:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /workout
[2025-07-27T13:46:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /workout 7
[2025-07-27T13:46:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /despawn
[2025-07-27T13:46:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /despawn
[2025-07-27T13:46:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /despawn
[2025-07-27T13:46:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /despawn
[2025-07-27T13:46:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /despawn
[2025-07-27T13:46:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /despawn
[2025-07-27T13:46:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /despawn
[2025-07-27T13:47:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-27T13:47:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-27T13:47:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-27T13:47:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /gps
[2025-07-27T13:47:43+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-27T13:47:43+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-27T14:06:44+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-27T14:27:44+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-27T14:48:44+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-27T20:41:23+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-27T20:41:23+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e1cad28e0)
[2025-07-27T20:41:23+0700] [Info] [connection] incoming connection: **************:50567 id: 0
[2025-07-27T20:41:23+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-27T20:41:26+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:50568)
[2025-07-27T20:44:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T20:45:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-27T20:45:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T20:45:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /mission
[2025-07-27T20:45:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /mission
[2025-07-27T20:49:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:49:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:50:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:50:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:50:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:50:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:50:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:50:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:51:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:51:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:51:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:51:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:51:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:53:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:53:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:53:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:53:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:53:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:53:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:53:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:53:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:54:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:54:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:54:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T20:54:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T20:55:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T20:55:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T20:56:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /mv
[2025-07-27T20:56:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:56:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:56:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:57:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:57:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:57:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:57:24+0700] [Info] [chat] [Kenzo_Wazoski]: p
[2025-07-27T20:57:37+0700] [Info] [chat] [Kenzo_Wazoski]: p
[2025-07-27T20:57:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:57:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:58:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:58:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:58:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:58:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:58:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T20:59:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T20:59:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-27T20:59:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-27T20:59:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-27T21:01:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:01:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:01:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:01:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:01:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:01:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:01:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:02:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:02:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:02:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:02:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:02:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:02:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:02:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:03:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:04:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:04:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:04:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:04:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T21:04:25+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-27T21:04:30+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-27T21:04:33+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-27T21:04:37+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e78292d5e)
[2025-07-27T21:04:37+0700] [Info] [connection] incoming connection: **************:61314 id: 0
[2025-07-27T21:04:37+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-27T21:04:40+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:62909)
[2025-07-27T21:05:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T21:05:24+0700] [Info] [chat] [Kenzo_Wazoski]: p
[2025-07-27T21:05:26+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-27T21:08:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-27T21:08:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-27T21:08:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /startmine
[2025-07-27T21:10:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /oreneinfo
[2025-07-27T21:13:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-27T21:13:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-27T21:13:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T21:13:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T21:14:36+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-27T21:14:36+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-27T21:14:45+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-27T21:14:45+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6ec73386bd)
[2025-07-27T21:14:45+0700] [Info] [connection] incoming connection: **************:56887 id: 0
[2025-07-27T21:14:45+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-27T21:14:50+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:56888)
[2025-07-27T21:15:21+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-27T21:15:21+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-27T21:15:28+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-27T21:15:28+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e1ca88dad)
[2025-07-27T21:15:28+0700] [Info] [connection] incoming connection: **************:61447 id: 0
[2025-07-27T21:15:28+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-27T21:15:32+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:49315)
[2025-07-27T21:15:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T21:16:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T21:16:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:16:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:16:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:17:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:17:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:17:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:17:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:17:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:17:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:18:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:18:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:18:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:18:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:18:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:19:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T21:19:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-27T21:20:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-27T21:20:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:20:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:20:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:20:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:20:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:21:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:21:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:21:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:21:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:21:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:21:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /resctockcargo
[2025-07-27T21:21:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:21:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:22:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T21:24:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:24:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:24:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:24:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:24:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:24:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:24:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:24:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:25:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:25:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo'
[2025-07-27T21:25:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:25:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:25:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:25:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:25:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-27T21:27:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:20+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:27:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:27:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:28:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:28:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:28:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:28:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:28:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:28:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-27T21:28:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-27T21:28:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-27T21:30:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-27T21:30:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-27T21:30:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:30:36+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12661
[2025-07-27T21:31:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:31:11+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12636
[2025-07-27T21:31:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:31:23+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12611
[2025-07-27T21:31:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:31:34+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12586
[2025-07-27T21:31:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:31:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:31:44+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12561
[2025-07-27T21:31:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:32:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:32:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:32:23+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12536
[2025-07-27T21:32:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:32:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:32:33+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12511
[2025-07-27T21:32:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:32:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:32:43+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12486
[2025-07-27T21:32:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:32:55+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12461
[2025-07-27T21:33:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:33:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-27T21:33:05+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12436
[2025-07-27T21:33:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-27T21:33:23+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-27T21:33:23+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-27T22:08:58+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-27T22:08:58+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (11bedf667e9a0d4e)
[2025-07-27T22:08:58+0700] [Info] [connection] incoming connection: **************:6516 id: 0
[2025-07-27T22:08:58+0700] [Info] [join] AFRICA_LANDHEERE has joined the server (0:**************)
[2025-07-27T22:09:03+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:46120)
[2025-07-27T22:09:24+0700] [Info] [chat] [AFRICA_LANDHEERE]: b
[2025-07-27T22:10:03+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-27T22:10:03+0700] [Info] [part] AFRICA_LANDHEERE has left the server (0:0)
[2025-07-27T22:18:42+0700] [Info] [connection] incoming connection: *************:24022 id: 0
[2025-07-27T22:18:42+0700] [Info] [join] Djaa has joined the server (0:*************)
[2025-07-27T22:19:14+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-27T22:19:16+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-27T22:21:11+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-27T22:21:53+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-27T22:21:54+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-27T22:23:02+0700] [Info] [CMD]: Daniel_Valhein used command /paytoll
[2025-07-27T22:23:02+0700] [Info] [CMD]: Daniel_Valhein used command /open
[2025-07-27T22:23:59+0700] [Info] [CMD]: Daniel_Valhein used command /findtree
[2025-07-27T22:26:18+0700] [Info] [CMD]: Daniel_Valhein used command /drink water
[2025-07-27T22:31:16+0700] [Info] [CMD]: Daniel_Valhein used command /stopanim
[2025-07-27T22:31:18+0700] [Info] [CMD]: Daniel_Valhein used command /drop
[2025-07-27T22:31:33+0700] [Info] [CMD]: Daniel_Valhein used command /droplum
[2025-07-27T22:31:38+0700] [Info] [CMD]: Daniel_Valhein used command /help
[2025-07-27T22:31:41+0700] [Info] [CMD]: Daniel_Valhein used command /animcmds
[2025-07-27T22:31:43+0700] [Info] [CMD]: Daniel_Valhein used command /faq
[2025-07-27T22:31:52+0700] [Info] [CMD]: Daniel_Valhein used command /faq
[2025-07-27T22:31:59+0700] [Info] [CMD]: Daniel_Valhein used command /faq
[2025-07-27T22:32:02+0700] [Info] [CMD]: Daniel_Valhein used command /faq
[2025-07-27T22:32:12+0700] [Info] [part] Daniel_Valhein has left the server (0:1)
[2025-07-27T22:54:16+0700] [Info] [connection] incoming connection: 182.4.72.101:60162 id: 0
[2025-07-27T22:54:16+0700] [Info] [join] gorooo has joined the server (0:182.4.72.101)
[2025-07-27T22:54:38+0700] [Info] [CMD]: Goro_Lancias used command /aduty
[2025-07-27T22:56:18+0700] [Info] [chat] [gorooo]: 6
[2025-07-27T22:56:23+0700] [Info] [chat] [gorooo]: 6
[2025-07-27T22:57:20+0700] [Info] [CMD]: gorooo used command /veh nrg
[2025-07-27T22:57:48+0700] [Info] [CMD]: gorooo used command /gotomap
[2025-07-27T22:58:23+0700] [Info] [CMD]: gorooo used command /jetpack
[2025-07-27T23:01:23+0700] [Info] [CMD]: gorooo used command /jetpack
[2025-07-27T23:02:36+0700] [Info] [CMD]: gorooo used command /ahelp
[2025-07-27T23:02:47+0700] [Info] [part] gorooo has left the server (0:1)
[2025-07-28T02:03:30+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-28T02:03:30+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e62caf057)
[2025-07-28T02:03:30+0700] [Info] [connection] incoming connection: **************:56590 id: 0
[2025-07-28T02:03:30+0700] [Info] [join] FanID has joined the server (0:**************)
[2025-07-28T02:03:34+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:49375)
[2025-07-28T02:04:12+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 1
[2025-07-28T02:04:19+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-28T02:04:23+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-28T02:04:25+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-28T02:04:27+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-28T02:04:34+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-28T02:05:40+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-28T02:06:01+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-28T02:06:05+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-28T02:06:06+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-28T02:06:12+0700] [Info] [CMD]: Yuki_Haruto used command /switch
[2025-07-28T02:06:13+0700] [Info] [CMD]: Yuki_Haruto used command /switch
[2025-07-28T02:06:45+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-28T02:06:45+0700] [Info] [part] Yuki_Haruto has left the server (0:1)
[2025-07-28T13:59:21+0700] [Info] [connection] incoming connection: **************:50742 id: 0
[2025-07-28T13:59:21+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-28T14:00:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-28T14:00:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /vip
[2025-07-28T14:00:41+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-28T14:00:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-28T14:01:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /pass
[2025-07-28T14:01:03+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-28T14:01:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /changepass
[2025-07-28T14:01:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /help
[2025-07-28T14:01:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-28T14:01:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /buyvip
[2025-07-28T14:02:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /buyvip oocname
[2025-07-28T14:03:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /o a
[2025-07-28T14:03:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /o a
[2025-07-28T14:04:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-28T14:04:43+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-28T14:05:06+0700] [Info] [connection] incoming connection: **************:63717 id: 0
[2025-07-28T14:05:06+0700] [Info] [join] FanID has joined the server (0:**************)
[2025-07-28T14:05:28+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-28T14:05:46+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-28T14:05:55+0700] [Info] [CMD]: Yuki_Haruto used command /switch
[2025-07-28T14:06:36+0700] [Info] [part] Yuki_Haruto has left the server (0:1)
[2025-07-28T15:14:31+0700] [Info] [connection] incoming connection: **************:50339 id: 0
[2025-07-28T15:14:31+0700] [Info] [join] tata has joined the server (0:**************)
[2025-07-28T15:14:51+0700] [Info] [CMD]: Tata_Wazoski used command /drink water
[2025-07-28T15:14:58+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T15:15:23+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T15:17:25+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-28T15:18:22+0700] [Info] [CMD]: Tata_Wazoski used command /lay 3
[2025-07-28T15:19:58+0700] [Info] [CMD]: Tata_Wazoski used command /lay 3
[2025-07-28T15:20:47+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-28T15:20:47+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (93428a6eaee2c221)
[2025-07-28T15:20:47+0700] [Info] [connection] incoming connection: **************:51256 id: 1
[2025-07-28T15:20:47+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-28T15:20:54+0700] [Info] [chat] [Tata_Wazoski]: `/lock
[2025-07-28T15:20:56+0700] [Info] [chat] [Tata_Wazoski]: `/lock
[2025-07-28T15:21:00+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T15:21:05+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T15:21:32+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-28T15:21:32+0700] [Info] [part] nandogtg has left the server (1:0)
[2025-07-28T15:21:33+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-28T15:21:33+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (93428a6e949e08bb)
[2025-07-28T15:21:33+0700] [Info] [connection] incoming connection: **************:64656 id: 1
[2025-07-28T15:21:33+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-28T15:21:33+0700] [Warning] Insufficient specifiers given to `format`: "AdmWarn: {F3FF02}%s{FFFFFF} possible rejoin hacks." < 2
[2025-07-28T15:21:33+0700] [Warning] Insufficient specifiers given to `format`: "```
AdmWarn: %s possible rejoin hacks.
```" < 2
[2025-07-28T15:21:33+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-28T15:21:33+0700] [Info] [part] nandogtg has left the server (1:2)
[2025-07-28T15:21:55+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-28T15:21:55+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (93428a6edc361407)
[2025-07-28T15:21:55+0700] [Info] [connection] incoming connection: **************:64364 id: 1
[2025-07-28T15:21:55+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-28T15:22:08+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-28T15:22:09+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T15:22:13+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:50151)
[2025-07-28T15:22:24+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-28T15:22:33+0700] [Info] [part] nandogtg has left the server (1:1)
[2025-07-28T15:22:34+0700] [Warning] client exceeded 'ackslimit' **************:50339 (4635) Limit: 3000/sec
[2025-07-28T15:22:35+0700] [Info] [part] Tata_Wazoski has left the server (0:1)
[2025-07-28T15:26:41+0700] [Info] [connection] incoming connection: **************:55171 id: 0
[2025-07-28T15:26:41+0700] [Info] [join] FanID has joined the server (0:**************)
[2025-07-28T15:27:02+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-28T15:27:02+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (93428a6e2f54b271)
[2025-07-28T15:27:02+0700] [Info] [connection] incoming connection: **************:51310 id: 1
[2025-07-28T15:27:02+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-28T15:27:07+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:51311)
[2025-07-28T15:27:17+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T15:27:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /o o
[2025-07-28T15:27:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazert
[2025-07-28T15:27:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T15:27:39+0700] [Info] [chat] [Kenzo_Wazoski]: maaf
[2025-07-28T15:27:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T15:27:53+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 0
[2025-07-28T15:28:02+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-28T15:28:03+0700] [Info] [chat] [Kenzo_Wazoski]: ampun?
[2025-07-28T15:28:09+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T15:28:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:28:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T15:28:29+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 0
[2025-07-28T15:28:32+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T15:28:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T15:28:45+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-28T15:29:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /mission
[2025-07-28T15:29:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-28T15:29:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:29:42+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T15:29:44+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T15:29:46+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T15:29:53+0700] [Info] [CMD]: Yuki_Haruto used command /lay 3
[2025-07-28T15:30:03+0700] [Info] [chat] [Kenzo_Wazoski]: bebet ya?
[2025-07-28T15:30:03+0700] [Warning] Insufficient specifiers given to `format`: "[Vehicle] %s says: %s" < 30666469
[2025-07-28T15:30:36+0700] [Info] [CMD]: Yuki_Haruto used command /goto
[2025-07-28T15:30:50+0700] [Info] [CMD]: Yuki_Haruto used command /goto forklift
[2025-07-28T15:30:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-28T15:30:56+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:31:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:31:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:31:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:31:34+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-28T15:31:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:31:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:31:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:31:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:31:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:31:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:31:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:32:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:32:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:32:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:32:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:32:13+0700] [Info] [CMD]: Yuki_Haruto used command /b /wank 1
[2025-07-28T15:32:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:32:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /wank 1
[2025-07-28T15:32:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:32:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:32:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:32:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:32:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:32:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:32:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T15:32:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /restockcargo
[2025-07-28T15:32:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-28T15:32:45+0700] [Info] [CMD]: Yuki_Haruto used command /set
[2025-07-28T15:33:05+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:33:12+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:33:14+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:33:17+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:33:26+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:33:28+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:33:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /lay 2
[2025-07-28T15:34:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T15:34:18+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-28T15:34:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T15:34:29+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-28T15:34:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T15:34:46+0700] [Info] [CMD]: Yuki_Haruto used command /droptrash
[2025-07-28T15:35:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /droptrash
[2025-07-28T15:35:03+0700] [Info] [CMD]: Yuki_Haruto used command /goto bus
[2025-07-28T15:35:08+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:36:31+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:36:31+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:36:31+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:36:32+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:36:32+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:36:32+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:39:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /redeem STRIVERP2025
[2025-07-28T15:39:44+0700] [Info] [INFO] Updated capacity for 4 garbage bins
[2025-07-28T15:48:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:36+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:48:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-28T15:48:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:49:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /STATS
[2025-07-28T15:49:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /skillls
[2025-07-28T15:49:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /skills
[2025-07-28T15:49:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /levels
[2025-07-28T15:49:29+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:49:30+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:49:31+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:49:32+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:49:34+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:49:35+0700] [Info] [CMD]: Yuki_Haruto used command /paytoll
[2025-07-28T15:49:35+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:49:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:49:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:49:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:49:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-28T15:49:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:49:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T15:50:43+0700] [Info] [CMD]: Yuki_Haruto used command /paytoll
[2025-07-28T15:50:43+0700] [Info] [CMD]: Yuki_Haruto used command /open
[2025-07-28T15:51:25+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm Yuki kau PG kan tadi
[2025-07-28T15:51:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 0 miss
[2025-07-28T15:53:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-28T15:53:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /me menyetrum Yuki dengan pistol listrik
[2025-07-28T15:53:47+0700] [Info] [CMD]: Yuki_Haruto used command /goto miner
[2025-07-28T15:53:51+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T15:53:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /o mining
[2025-07-28T15:54:04+0700] [Info] [CMD]: Yuki_Haruto used command /startmine
[2025-07-28T15:54:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /o mining
[2025-07-28T15:54:14+0700] [Info] [CMD]: Yuki_Haruto used command /drink water
[2025-07-28T15:54:36+0700] [Info] [CMD]: Yuki_Haruto used command /sethp all 100
[2025-07-28T15:54:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-28T15:54:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-28T15:54:42+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T15:54:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-28T15:54:46+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 0 100
[2025-07-28T15:54:50+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-28T15:55:01+0700] [Info] [CMD]: Yuki_Haruto used command /o 
[2025-07-28T15:55:05+0700] [Info] [CMD]: Yuki_Haruto used command /o anjay
[2025-07-28T15:55:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /startmine
[2025-07-28T15:57:49+0700] [Info] [chat] [Yuki_Haruto]: mari kita bekerja
[2025-07-28T15:57:58+0700] [Info] [chat] [Kenzo_Wazoski]: untuk ap?
[2025-07-28T15:58:06+0700] [Info] [CMD]: Yuki_Haruto used command /s bedah rumah mereka
[2025-07-28T15:58:24+0700] [Info] [CMD]: Yuki_Haruto used command /s jadi kan lah istana
[2025-07-28T15:58:31+0700] [Info] [CMD]: Yuki_Haruto used command /s agar mereka bahagia 
[2025-07-28T15:59:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /s 
[2025-07-28T15:59:18+0700] [Info] [CMD]: Yuki_Haruto used command /mysalary
[2025-07-28T15:59:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /s ala batahun anak rantau
[2025-07-28T16:00:18+0700] [Info] [CMD]: Yuki_Haruto used command /dancing
[2025-07-28T16:00:21+0700] [Info] [CMD]: Yuki_Haruto used command /dancing 10
[2025-07-28T16:00:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /s bentar lagi Yuki engga di sini cokkkkk
[2025-07-28T16:00:40+0700] [Info] [CMD]: Yuki_Haruto used command /s bacot
[2025-07-28T16:00:44+0700] [Info] [INFO] Updated capacity for 4 garbage bins
[2025-07-28T16:00:57+0700] [Info] [CMD]: Yuki_Haruto used command /goto sweeper
[2025-07-28T16:01:02+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:01:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-28T16:01:16+0700] [Info] [CMD]: Yuki_Haruto used command /goto smd2
[2025-07-28T16:01:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /drink water
[2025-07-28T16:01:20+0700] [Info] [CMD]: Yuki_Haruto used command /goto
[2025-07-28T16:01:26+0700] [Info] [CMD]: Yuki_Haruto used command /goto smb 2
[2025-07-28T16:01:29+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:01:41+0700] [Info] [CMD]: Yuki_Haruto used command /bring smb
[2025-07-28T16:01:47+0700] [Info] [CMD]: Yuki_Haruto used command /goto smb
[2025-07-28T16:01:50+0700] [Info] [CMD]: Yuki_Haruto used command /goto smb
[2025-07-28T16:01:51+0700] [Info] [CMD]: Yuki_Haruto used command /goto smb2
[2025-07-28T16:01:55+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:02:01+0700] [Info] [CMD]: Yuki_Haruto used command /goto sweeper
[2025-07-28T16:02:03+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:03:06+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 1
[2025-07-28T16:03:14+0700] [Info] [CMD]: Yuki_Haruto used command /drink water
[2025-07-28T16:03:17+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 5
[2025-07-28T16:03:22+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 10
[2025-07-28T16:03:26+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 6
[2025-07-28T16:03:32+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-28T16:03:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-28T16:03:42+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 7
[2025-07-28T16:03:46+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 8
[2025-07-28T16:03:58+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 7
[2025-07-28T16:04:02+0700] [Info] [CMD]: Yuki_Haruto used command /goto business 8
[2025-07-28T16:04:08+0700] [Info] [CMD]: Yuki_Haruto used command /buy
[2025-07-28T16:04:51+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T16:04:53+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T16:04:56+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T16:05:09+0700] [Info] [CMD]: Yuki_Haruto used command /lay 3
[2025-07-28T16:05:41+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T16:05:43+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T16:05:45+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T16:05:49+0700] [Info] [death] Yuki_Haruto died 54
[2025-07-28T16:06:00+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 0 100
[2025-07-28T16:06:06+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T16:06:15+0700] [Info] [CMD]: Yuki_Haruto used command /goto trashmaster
[2025-07-28T16:06:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-28T16:06:21+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:06:53+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-28T16:06:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /collectrash
[2025-07-28T16:07:04+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-28T16:07:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /collectrash
[2025-07-28T16:07:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T16:07:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T16:07:24+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-28T16:07:55+0700] [Info] [CMD]: Yuki_Haruto used command /collecttrash
[2025-07-28T16:07:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T16:08:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /droptrash
[2025-07-28T16:08:32+0700] [Info] [CMD]: Yuki_Haruto used command /droptrash
[2025-07-28T16:08:39+0700] [Info] [CMD]: Yuki_Haruto used command /droptrash
[2025-07-28T16:08:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-28T16:08:45+0700] [Info] [CMD]: Yuki_Haruto used command /droptrash
[2025-07-28T16:11:16+0700] [Info] [CMD]: Yuki_Haruto used command /l uy
[2025-07-28T16:11:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /l Hah?
[2025-07-28T16:11:41+0700] [Info] [CMD]: Yuki_Haruto used command /l di sini ada jual sabu
[2025-07-28T16:11:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /l engga minat pak
[2025-07-28T16:11:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /l sini a
[2025-07-28T16:12:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /l sini la
[2025-07-28T16:12:22+0700] [Info] [CMD]: Yuki_Haruto used command /wank 1
[2025-07-28T16:12:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /l kamu jual apa tadi
[2025-07-28T16:12:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /cuff 0
[2025-07-28T16:12:32+0700] [Info] [chat] [Kenzo_Wazoski]: kamu jual apa tadi
[2025-07-28T16:12:37+0700] [Info] [CMD]: Yuki_Haruto used command /l aku cuma nanya 
[2025-07-28T16:12:41+0700] [Info] [kill] Yuki_Haruto killed Kenzo_Wazoski Fist
[2025-07-28T16:12:41+0700] [Info] [death] Kenzo_Wazoski died 255
[2025-07-28T16:12:41+0700] [Info] [chat] [Kenzo_Wazoski]: wkwkwkw
[2025-07-28T16:12:53+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:12:56+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:12:57+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:13:03+0700] [Info] [part] Yuki_Haruto has left the server (0:1)
[2025-07-28T16:13:14+0700] [Info] [chat] [Kenzo_Wazoski]: p
[2025-07-28T16:13:15+0700] [Info] [chat] [Kenzo_Wazoski]: dwwe
[2025-07-28T16:13:16+0700] [Info] [chat] [Kenzo_Wazoski]: wqw
[2025-07-28T16:13:23+0700] [Info] [connection] incoming connection: **************:59011 id: 0
[2025-07-28T16:13:23+0700] [Info] [join] FanID has joined the server (0:**************)
[2025-07-28T16:13:27+0700] [Info] [chat] [Kenzo_Wazoski]: p
[2025-07-28T16:13:29+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-28T16:13:29+0700] [Info] [part] Kenzo_Wazoski has left the server (1:1)
[2025-07-28T16:13:39+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-28T16:13:39+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (93428a6e613c7d7e)
[2025-07-28T16:13:39+0700] [Info] [connection] incoming connection: **************:57885 id: 1
[2025-07-28T16:13:39+0700] [Info] [join] nandogtg has joined the server (1:**************)
[2025-07-28T16:13:44+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:56389)
[2025-07-28T16:13:56+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:14:02+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:14:08+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:14:10+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:14:12+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:14:12+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:14:16+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 0
[2025-07-28T16:14:21+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 100
[2025-07-28T16:14:26+0700] [Info] [chat] [Kenzo_Wazoski]: `
[2025-07-28T16:14:29+0700] [Info] [chat] [Kenzo_Wazoski]: ;l;qw;wd
[2025-07-28T16:14:33+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 0
[2025-07-28T16:14:43+0700] [Info] [CMD]: Yuki_Haruto used command /sethp 1 1
[2025-07-28T16:14:48+0700] [Info] [CMD]: Yuki_Haruto used command /bring 
[2025-07-28T16:14:50+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:14:53+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:14:55+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:15:23+0700] [Info] [chat] [Kenzo_Wazoski]: A B C D E F G H I J K L M N O F Q R S T U P W X Y Z
[2025-07-28T16:17:04+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T16:17:10+0700] [Info] [CMD]: Yuki_Haruto used command /pay 1 50
[2025-07-28T16:17:10+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T16:17:11+0700] [Info] [part] Yuki_Haruto has left the server (0:1)
[2025-07-28T16:17:13+0700] [Info] [chat] [Kenzo_Wazoski]: ok makasi
[2025-07-28T16:21:44+0700] [Info] [INFO] Updated capacity for 7 garbage bins
[2025-07-28T16:24:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-28T16:25:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-28T16:25:11+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-28T16:25:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-28T16:25:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-28T16:26:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-28T16:27:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-28T16:27:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-28T16:27:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-28T16:27:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /piss
[2025-07-28T16:28:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /refuel
[2025-07-28T16:30:07+0700] [Info] [chat] [Kenzo_Wazoski]: p
[2025-07-28T16:30:08+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-28T16:30:08+0700] [Info] [part] Kenzo_Wazoski has left the server (1:1)
[2025-07-28T16:30:16+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-28T16:30:16+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e82a5f24e)
[2025-07-28T16:30:16+0700] [Info] [connection] incoming connection: **************:64203 id: 0
[2025-07-28T16:30:16+0700] [Info] [join] nandogtg has joined the server (0:**************)
[2025-07-28T16:30:21+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:64204)
[2025-07-28T16:30:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /deposit 820
[2025-07-28T16:30:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T16:31:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /deposit 8000
[2025-07-28T16:31:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /deposit 40
[2025-07-28T16:31:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-28T16:32:04+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-28T16:32:04+0700] [Info] [part] Kenzo_Wazoski has left the server (0:1)
[2025-07-28T16:42:44+0700] [Info] [INFO] Updated capacity for 7 garbage bins
[2025-07-28T17:03:44+0700] [Info] [INFO] Updated capacity for 7 garbage bins
[2025-07-28T17:24:44+0700] [Info] [INFO] Updated capacity for 6 garbage bins
[2025-07-28T17:45:44+0700] [Info] [INFO] Updated capacity for 5 garbage bins
[2025-07-28T21:12:32+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-28T21:12:32+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6edd2a14bd)
[2025-07-28T21:12:32+0700] [Info] [connection] incoming connection: **************:53635 id: 0
[2025-07-28T21:12:32+0700] [Info] [join] tata has joined the server (0:**************)
[2025-07-28T21:12:46+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:62473)
[2025-07-28T21:13:11+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:16:10+0700] [Info] [CMD]: Tata_Wazoski used command /v trunk
[2025-07-28T21:16:12+0700] [Info] [CMD]: Tata_Wazoski used command /v trunk
[2025-07-28T21:16:14+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-28T21:16:14+0700] [Info] [part] Tata_Wazoski has left the server (0:1)
[2025-07-28T21:16:24+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-28T21:16:24+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e756f1198)
[2025-07-28T21:16:24+0700] [Info] [connection] incoming connection: **************:63240 id: 0
[2025-07-28T21:16:24+0700] [Info] [join] tata has joined the server (0:**************)
[2025-07-28T21:16:28+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:63241)
[2025-07-28T21:16:47+0700] [Info] [CMD]: Tata_Wazoski used command /v trunk
[2025-07-28T21:16:53+0700] [Info] [CMD]: Tata_Wazoski used command /v trunk
[2025-07-28T21:17:41+0700] [Info] [CMD]: Tata_Wazoski used command /selllumber
[2025-07-28T21:18:15+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:18:21+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:20:15+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-28T21:20:16+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T21:20:16+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T21:21:00+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:21:38+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:22:12+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:22:46+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:23:19+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:23:55+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:24:31+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:25:03+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:25:41+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:25:46+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:25:54+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-28T21:26:02+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-28T21:26:07+0700] [Info] [CMD]: Tata_Wazoski used command /stats
[2025-07-28T21:26:19+0700] [Info] [CMD]: Tata_Wazoski used command /stats
[2025-07-28T21:26:22+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:27:12+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:27:46+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:28:22+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:29:28+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:29:32+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:30:22+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:30:55+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:31:06+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:31:06+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:31:06+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-28T21:31:07+0700] [Info] [part] Tata_Wazoski has left the server (0:1)
[2025-07-28T21:31:11+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-28T21:31:17+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e6d7498ab)
[2025-07-28T21:31:19+0700] [Info] [connection] incoming connection: **************:54290 id: 0
[2025-07-28T21:31:19+0700] [Info] [join] tata has joined the server (0:**************)
[2025-07-28T21:31:22+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:54291)
[2025-07-28T21:31:36+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:31:39+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:31:56+0700] [Info] [connection] incoming connection: **************:56075 id: 1
[2025-07-28T21:31:57+0700] [Info] [join] gibran has joined the server (1:**************)
[2025-07-28T21:32:08+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:32:11+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:32:12+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T21:32:21+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:32:26+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:32:36+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:33:18+0700] [Info] [CMD]: Wiliam_Hurly used command /PISS
[2025-07-28T21:33:47+0700] [Info] [CMD]: Wiliam_Hurly used command /BUY
[2025-07-28T21:33:55+0700] [Info] [CMD]: Wiliam_Hurly used command /BUY
[2025-07-28T21:34:00+0700] [Info] [CMD]: Wiliam_Hurly used command /BUY
[2025-07-28T21:34:21+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T21:34:25+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T21:34:58+0700] [Info] [CMD]: Wiliam_Hurly used command /GPS
[2025-07-28T21:35:08+0700] [Info] [CMD]: Wiliam_Hurly used command /GPS
[2025-07-28T21:35:38+0700] [Info] [CMD]: Tata_Wazoski used command /sellfish
[2025-07-28T21:35:39+0700] [Info] [FISH WAREHOUSE] Stock saved successfully: 48 units
[2025-07-28T21:36:05+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:37:05+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-28T21:37:08+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-28T21:37:36+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-28T21:37:52+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-28T21:38:04+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:38:07+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:38:16+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:40:41+0700] [Info] [sv:dbg:network:connect] : connecting player (2) with address (**************) ...
[2025-07-28T21:40:41+0700] [Info] [sv:dbg:network:connect] : player (2) assigned key (93428a6ef11b18fd)
[2025-07-28T21:40:41+0700] [Info] [connection] incoming connection: **************:60403 id: 2
[2025-07-28T21:40:41+0700] [Info] [join] nandogtg has joined the server (2:**************)
[2025-07-28T21:40:45+0700] [Info] [sv:dbg:network:receive] : player (2) identified (port:58481)
[2025-07-28T21:41:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-28T21:41:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /delayjob
[2025-07-28T21:41:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /mysalary
[2025-07-28T21:41:46+0700] [Info] [CMD]: Tata_Wazoski used command /startmine
[2025-07-28T21:42:12+0700] [Info] [CMD]: Wiliam_Hurly used command /starmine
[2025-07-28T21:42:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /mission
[2025-07-28T21:42:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-28T21:42:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T21:42:50+0700] [Info] [CMD]: Wiliam_Hurly used command /startmine
[2025-07-28T21:42:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-28T21:42:57+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12411
[2025-07-28T21:43:02+0700] [Info] [CMD]: Tata_Wazoski used command /drink water
[2025-07-28T21:43:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-28T21:43:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-28T21:43:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-28T21:43:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /getcargo
[2025-07-28T21:43:19+0700] [Info] [PLANT SYSTEM] Plant stock saved: 12386
[2025-07-28T21:43:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /paytoll
[2025-07-28T21:43:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T21:45:39+0700] [Info] [CMD]: Wiliam_Hurly used command /stats
[2025-07-28T21:45:55+0700] [Info] [CMD]: Tata_Wazoski used command /stats
[2025-07-28T21:46:11+0700] [Info] [CMD]: Wiliam_Hurly used command /endmine
[2025-07-28T21:46:21+0700] [Info] [CMD]: Wiliam_Hurly used command /lock
[2025-07-28T21:46:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /crapveh
[2025-07-28T21:46:39+0700] [Info] [CMD]: Kenzo_Wazoski used command /scrapveh
[2025-07-28T21:46:40+0700] [Info] [CMD]: Wiliam_Hurly used command /GPS
[2025-07-28T21:46:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /scrapveh confirm
[2025-07-28T21:47:16+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:47:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /quitjob
[2025-07-28T21:47:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /takejob
[2025-07-28T21:48:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /stats
[2025-07-28T21:48:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /HELP
[2025-07-28T21:48:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /faq
[2025-07-28T21:48:37+0700] [Info] [CMD]: Kenzo_Wazoski used command /HELP
[2025-07-28T21:48:40+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:48:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T21:48:56+0700] [Info] [CMD]: Kenzo_Wazoski used command /JOBDUTY
[2025-07-28T21:49:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T21:50:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /o p
[2025-07-28T21:50:26+0700] [Info] [CMD]: Wiliam_Hurly used command /mysalary
[2025-07-28T21:50:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /m sini
[2025-07-28T21:50:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /or ap
[2025-07-28T21:50:38+0700] [Warning] Insufficient specifiers given to `format`: "(( (%d) %s %s: %s ))" < 30666175
[2025-07-28T21:50:38+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s %s: %s" < 6456495
[2025-07-28T21:50:44+0700] [Info] [CMD]: Tata_Wazoski used command /atm
[2025-07-28T21:51:26+0700] [Info] [CMD]: Tata_Wazoski used command /pay 1 2
[2025-07-28T21:51:26+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T21:51:44+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:51:47+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:52:01+0700] [Info] [CMD]: Tata_Wazoski used command /pay 1 4
[2025-07-28T21:52:01+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T21:52:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 
[2025-07-28T21:52:08+0700] [Info] [CMD]: Tata_Wazoski used command /pay 1 500
[2025-07-28T21:52:08+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T21:52:14+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 0 kalok repair bisa ke Mechanic ya
[2025-07-28T21:52:15+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:52:18+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T21:52:33+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:52:34+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm kalok mau repair langsung bisa datang ke Mechanic?
[2025-07-28T21:52:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /pm 1 kalok mau repair langsung bisa datang ke Mechanic?
[2025-07-28T21:53:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /o kalok mau repair langsung bisa datang ke Mecanic?
[2025-07-28T21:53:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /b p
[2025-07-28T21:54:01+0700] [Info] [CMD]: Wiliam_Hurly used command /refuel
[2025-07-28T21:54:13+0700] [Info] [CMD]: Wiliam_Hurly used command /refuel
[2025-07-28T21:54:15+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T21:54:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T21:54:42+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T21:54:44+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T21:55:01+0700] [Info] [CMD]: Wiliam_Hurly used command /refuel
[2025-07-28T21:55:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T21:55:06+0700] [Info] [CMD]: Wiliam_Hurly used command /refuel
[2025-07-28T21:55:11+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T21:55:16+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T21:55:17+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T21:55:18+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T21:55:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T21:56:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T21:56:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T21:56:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T21:56:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T21:56:18+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T21:56:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /tazer
[2025-07-28T21:56:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /jobduty
[2025-07-28T21:56:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T21:56:50+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-28T21:56:57+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-28T21:56:58+0700] [Info] [CMD]: Wiliam_Hurly used command /day 1
[2025-07-28T21:57:00+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-28T21:57:04+0700] [Info] [chat] [Kenzo_Wazoski]: om 150$
[2025-07-28T21:57:10+0700] [Info] [CMD]: Wiliam_Hurly used command /piss
[2025-07-28T21:57:15+0700] [Info] [CMD]: Tata_Wazoski used command /pay 2 200
[2025-07-28T21:57:15+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T21:57:19+0700] [Info] [chat] [Kenzo_Wazoski]: ok makasi
[2025-07-28T21:57:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T21:57:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /anim engine
[2025-07-28T21:57:41+0700] [Info] [CMD]: Tata_Wazoski used command /pay 2 5
[2025-07-28T21:57:41+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T21:57:44+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-28T21:57:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T21:58:02+0700] [Info] [CMD]: Tata_Wazoski used command /delay job
[2025-07-28T21:58:05+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-28T21:58:24+0700] [Info] [CMD]: Wiliam_Hurly used command /pay 2 240
[2025-07-28T21:58:24+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T21:58:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T21:58:29+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T21:59:09+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:59:36+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T21:59:45+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T21:59:51+0700] [Info] [chat] [Kenzo_Wazoski]: ke ATM dulu ya bang?
[2025-07-28T22:00:09+0700] [Info] [CMD]: Wiliam_Hurly used command /takejob
[2025-07-28T22:00:28+0700] [Info] [CMD]: Wiliam_Hurly used command /takejob\
[2025-07-28T22:00:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /pay 0 100$
[2025-07-28T22:00:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /pay 0 100
[2025-07-28T22:00:33+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T22:00:33+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T22:00:38+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T22:00:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:00:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:00:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:00:55+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:00:58+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:00:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:01:02+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:01:03+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:01:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:01:08+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:01:09+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:01:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:01:19+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:01:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:01:19+0700] [Info] [CMD]: Wiliam_Hurly used command /mm
[2025-07-28T22:01:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:01:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:01:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /atm
[2025-07-28T22:02:35+0700] [Info] [CMD]: Tata_Wazoski used command /stats
[2025-07-28T22:02:47+0700] [Info] [CMD]: Kenzo_Wazoski used command /b bro engga masuk SAMD
[2025-07-28T22:02:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T22:02:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /b kebanyakan PD
[2025-07-28T22:03:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T22:03:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /b Medic nya kaga ada?
[2025-07-28T22:03:22+0700] [Info] [CMD]: Kenzo_Wazoski used command /buy
[2025-07-28T22:03:24+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:03:27+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s has purchased a %s for %s." < 6456495
[2025-07-28T22:03:34+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:03:51+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T22:03:53+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T22:03:55+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T22:03:59+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T22:05:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent 100
[2025-07-28T22:05:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T22:05:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-28T22:05:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T22:05:40+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent 
[2025-07-28T22:05:46+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent 
[2025-07-28T22:05:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent 
[2025-07-28T22:05:50+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent 
[2025-07-28T22:05:51+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent 
[2025-07-28T22:05:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T22:06:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T22:06:12+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent 
[2025-07-28T22:06:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent
[2025-07-28T22:06:21+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent
[2025-07-28T22:06:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent
[2025-07-28T22:06:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /buycomponent
[2025-07-28T22:07:54+0700] [Info] [AFK] Player Wiliam_Hurly (ID: 1) masuk mode AFK
[2025-07-28T22:08:06+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-28T22:08:06+0700] [Info] [part] Tata_Wazoski has left the server (0:1)
[2025-07-28T22:08:38+0700] [Info] [connection] incoming connection: **************:52120 id: 0
[2025-07-28T22:08:38+0700] [Info] [join] FanID has joined the server (0:**************)
[2025-07-28T22:08:38+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T22:08:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:08:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:08:58+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic
[2025-07-28T22:08:58+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:09:01+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:09:07+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:09:10+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-28T22:09:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:09:26+0700] [Info] [CMD]: Kenzo_Wazoski used command /jobduty
[2025-07-28T22:09:28+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:10:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:10:17+0700] [Info] [AFK] Player Wiliam_Hurly (ID: 1) disconnect saat AFK - data telah di-restore
[2025-07-28T22:10:17+0700] [Info] [part] Wiliam_Hurly has left the server (1:1)
[2025-07-28T22:10:43+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:10:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:10:57+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-28T22:11:17+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:11:19+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:11:24+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:11:40+0700] [Info] [CMD]: Yuki_Haruto used command /wank 2
[2025-07-28T22:11:49+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:11:52+0700] [Info] [CMD]: Kenzo_Wazoski used command /v hood
[2025-07-28T22:11:54+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:12:18+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:12:51+0700] [Info] [DEBUG] Database query: UPDATE `cars` SET `carNitro` = 3 WHERE `carID` = 0
[2025-07-28T22:12:53+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:13:00+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:13:27+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T22:13:31+0700] [Info] [CMD]: Kenzo_Wazoski used command /lock
[2025-07-28T22:13:33+0700] [Info] [CMD]: Kenzo_Wazoski used command /v trunk
[2025-07-28T22:13:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:14:07+0700] [Info] [CMD]: Yuki_Haruto used command /wank 2
[2025-07-28T22:14:11+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic
[2025-07-28T22:14:17+0700] [Info] [CMD]: Yuki_Haruto used command /wank 2
[2025-07-28T22:14:23+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:14:33+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic 2
[2025-07-28T22:14:34+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic 2
[2025-07-28T22:14:35+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic 2
[2025-07-28T22:14:38+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic 2
[2025-07-28T22:14:41+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic 
[2025-07-28T22:14:43+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic 
[2025-07-28T22:14:57+0700] [Info] [CMD]: Yuki_Haruto used command /wank 2
[2025-07-28T22:15:05+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:15:25+0700] [Info] [CMD]: Yuki_Haruto used command /wank 2
[2025-07-28T22:15:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:16:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:17:06+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:17:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /mm
[2025-07-28T22:18:44+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-28T22:19:02+0700] [Info] [CMD]: Kenzo_Wazoski used command /iventory
[2025-07-28T22:19:04+0700] [Info] [CMD]: Kenzo_Wazoski used command /iventori
[2025-07-28T22:19:13+0700] [Info] [CMD]: Kenzo_Wazoski used command /inventory
[2025-07-28T22:19:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T22:19:35+0700] [Info] [CMD]: Kenzo_Wazoski used command /open
[2025-07-28T22:20:30+0700] [Info] [CMD]: Kenzo_Wazoski used command /modshop
[2025-07-28T22:20:48+0700] [Info] [CMD]: Kenzo_Wazoski used command /modshop
[2025-07-28T22:20:59+0700] [Info] [CMD]: Kenzo_Wazoski used command /modshop
[2025-07-28T22:22:16+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T22:22:32+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T22:22:45+0700] [Info] [CMD]: Kenzo_Wazoski used command /collecttrash
[2025-07-28T22:23:08+0700] [Info] [CMD]: Kenzo_Wazoski used command /droptrash
[2025-07-28T22:23:16+0700] [Info] [sv:dbg:network:connect] : disconnecting player (2) ...
[2025-07-28T22:23:16+0700] [Info] [part] Kenzo_Wazoski has left the server (2:1)
[2025-07-28T22:24:43+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-28T22:24:47+0700] [Info] [sv:dbg:network:connect] : connecting player (1) with address (**************) ...
[2025-07-28T22:24:47+0700] [Info] [sv:dbg:network:connect] : player (1) assigned key (93428a6e44106860)
[2025-07-28T22:24:47+0700] [Info] [connection] incoming connection: **************:57225 id: 1
[2025-07-28T22:24:47+0700] [Info] [join] tata has joined the server (1:**************)
[2025-07-28T22:24:52+0700] [Info] [sv:dbg:network:receive] : player (1) identified (port:53909)
[2025-07-28T22:25:08+0700] [Info] [CMD]: Yuki_Haruto used command /lcok
[2025-07-28T22:25:10+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-28T22:25:23+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T22:25:30+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T22:26:14+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 min 
[2025-07-28T22:26:30+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 bagi uang 10k min buat beli sultan
[2025-07-28T22:27:50+0700] [Info] [connection] incoming connection: *************:42419 id: 2
[2025-07-28T22:27:50+0700] [Info] [join] Djaa has joined the server (2:*************)
[2025-07-28T22:28:14+0700] [Info] [CMD]: Daniel_Valhein used command //stats
[2025-07-28T22:28:16+0700] [Info] [CMD]: Daniel_Valhein used command /stats
[2025-07-28T22:28:24+0700] [Info] [CMD]: Daniel_Valhein used command /v trunk
[2025-07-28T22:28:29+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-28T22:28:47+0700] [Info] [part] Daniel_Valhein has left the server (2:0)
[2025-07-28T22:28:48+0700] [Info] [CMD]: Yuki_Haruto used command /pm ngator
[2025-07-28T22:28:52+0700] [Info] [CMD]: Yuki_Haruto used command /pm 1 ngator
[2025-07-28T22:28:57+0700] [Info] [connection] incoming connection: *************:42421 id: 2
[2025-07-28T22:28:57+0700] [Info] [join] Djaa has joined the server (2:*************)
[2025-07-28T22:29:06+0700] [Info] [CMD]: Yuki_Haruto used command /pm 1 ngocok dulu 
[2025-07-28T22:29:34+0700] [Info] [CMD]: Yuki_Haruto used command /drink water
[2025-07-28T22:29:37+0700] [Info] [CMD]: Yuki_Haruto used command /piss
[2025-07-28T22:31:04+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-28T22:31:06+0700] [Info] [CMD]: Yuki_Haruto used command /lock
[2025-07-28T22:31:11+0700] [Info] [CMD]: Yuki_Haruto used command /switch
[2025-07-28T22:31:14+0700] [Info] [CMD]: Yuki_Haruto used command /o 
[2025-07-28T22:31:18+0700] [Info] [CMD]: Yuki_Haruto used command /o anjay
[2025-07-28T22:31:24+0700] [Info] [CMD]: Yuki_Haruto used command /pm 1 woy
[2025-07-28T22:31:24+0700] [Info] [CMD]: Tata_Wazoski used command /pm 0 :)
[2025-07-28T22:32:04+0700] [Info] [CMD]: Yuki_Haruto used command /delayjob
[2025-07-28T22:33:02+0700] [Info] [CMD]: Daniel_Valhein used command /lock
[2025-07-28T22:34:45+0700] [Info] [CMD]: Daniel_Valhein used command /o tiba tiba ada neon gini wkwk
[2025-07-28T22:34:56+0700] [Info] [CMD]: Daniel_Valhein used command /pm 0 bug muncul neon tiba tiba masih ada bng
[2025-07-28T22:35:37+0700] [Info] [CMD]: Daniel_Valhein used command /selllumber
[2025-07-28T22:35:42+0700] [Info] [CMD]: Daniel_Valhein used command /selllumber
[2025-07-28T22:35:57+0700] [Info] [CMD]: Daniel_Valhein used command /drink water
[2025-07-28T22:36:01+0700] [Info] [CMD]: Daniel_Valhein used command /drink water
[2025-07-28T22:36:33+0700] [Info] [part] Daniel_Valhein has left the server (2:0)
[2025-07-28T22:36:41+0700] [Info] [connection] incoming connection: 114.10.14.98:65008 id: 2
[2025-07-28T22:36:41+0700] [Info] [join] Daniel_Valhein has joined the server (2:114.10.14.98)
[2025-07-28T22:36:53+0700] [Info] [part] Djaa has left the server (2:2)
[2025-07-28T22:36:59+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-28T22:36:59+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T22:37:43+0700] [Info] [AFK] Player Yuki_Haruto (ID: 0) masuk mode AFK
[2025-07-28T22:37:56+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T22:37:56+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T22:37:57+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T22:37:57+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T22:37:57+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T22:38:00+0700] [Info] [CMD]: Tata_Wazoski used command /paytoll
[2025-07-28T22:38:00+0700] [Info] [CMD]: Tata_Wazoski used command /open
[2025-07-28T22:39:45+0700] [Info] [INFO] Updated capacity for 4 garbage bins
[2025-07-28T22:40:34+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-28T22:41:19+0700] [Info] [CMD]: Yuki_Haruto used command /afk RQTA63
[2025-07-28T22:41:19+0700] [Info] [AFK] Player Yuki_Haruto (ID: 0) keluar dari mode AFK
[2025-07-28T22:41:25+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T22:41:38+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:41:42+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:41:44+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-28T22:41:45+0700] [Info] [CMD]: Yuki_Haruto used command /me menangis
[2025-07-28T22:41:48+0700] [Info] [CMD]: Yuki_Haruto used command /cry
[2025-07-28T22:42:09+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T22:42:12+0700] [Info] [CMD]: Yuki_Haruto used command /inventary
[2025-07-28T22:42:16+0700] [Info] [CMD]: Yuki_Haruto used command /inventary
[2025-07-28T22:42:22+0700] [Info] [CMD]: Yuki_Haruto used command /inventary
[2025-07-28T22:42:50+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-28T22:42:53+0700] [Info] [CMD]: Yuki_Haruto used command /goto smb2
[2025-07-28T22:42:57+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:43:15+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T22:43:16+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:43:21+0700] [Info] [CMD]: Yuki_Haruto used command /fishes
[2025-07-28T22:43:35+0700] [Info] [CMD]: Yuki_Haruto used command /goto
[2025-07-28T22:43:47+0700] [Info] [CMD]: Yuki_Haruto used command /goto ff
[2025-07-28T22:44:31+0700] [Info] [CMD]: Yuki_Haruto used command /sellfish
[2025-07-28T22:44:38+0700] [Info] [FISH WAREHOUSE] Stock saved successfully: 62 units
[2025-07-28T22:44:50+0700] [Info] [CMD]: Yuki_Haruto used command /drink water
[2025-07-28T22:45:15+0700] [Info] [CMD]: Yuki_Haruto used command /goto 1
[2025-07-28T22:45:20+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:45:25+0700] [Info] [CMD]: Yuki_Haruto used command /delayjob
[2025-07-28T22:47:43+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:48:13+0700] [Info] [AFK] Player Tata_Wazoski (ID: 1) masuk mode AFK
[2025-07-28T22:48:16+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:48:52+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:49:14+0700] [Info] [CMD]: Yuki_Haruto used command /acc
[2025-07-28T22:49:27+0700] [Info] [CMD]: Yuki_Haruto used command /acc
[2025-07-28T22:51:41+0700] [Info] [CMD]: Yuki_Haruto used command /acc
[2025-07-28T22:52:03+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:54:29+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:54:55+0700] [Info] [CMD]: Tata_Wazoski used command /afk W2IKSJ
[2025-07-28T22:54:55+0700] [Info] [AFK] Player Tata_Wazoski (ID: 1) keluar dari mode AFK
[2025-07-28T22:54:57+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T22:55:16+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-28T22:55:26+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-28T22:55:32+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T22:55:38+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:56:16+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T22:56:50+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T22:57:29+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T22:57:31+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:57:34+0700] [Info] [CMD]: Yuki_Haruto used command /fishes
[2025-07-28T22:57:56+0700] [Info] [CMD]: Yuki_Haruto used command /pay 1 0.01
[2025-07-28T22:57:56+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T22:58:04+0700] [Info] [CMD]: Tata_Wazoski used command /PM 0 6
[2025-07-28T22:58:13+0700] [Info] [CMD]: Tata_Wazoski used command /pay 0 6
[2025-07-28T22:58:13+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T22:58:16+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T22:58:33+0700] [Info] [CMD]: Yuki_Haruto used command /pay 1 50
[2025-07-28T22:58:33+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s (%s) has paid %s to %s (%s)." < 6456495
[2025-07-28T22:58:41+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-28T22:58:45+0700] [Info] [CMD]: Yuki_Haruto used command /fish
[2025-07-28T22:59:10+0700] [Info] [CMD]: Yuki_Haruto used command /fishes
[2025-07-28T22:59:15+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:17+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:19+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:20+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:21+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:21+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:22+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:23+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:24+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:25+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:26+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:28+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:29+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:31+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:33+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:34+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:35+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:35+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:36+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:38+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:40+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T22:59:44+0700] [Info] [CMD]: Tata_Wazoski used command /delayjob
[2025-07-28T22:59:54+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T23:00:03+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-28T23:00:45+0700] [Info] [INFO] Updated capacity for 4 garbage bins
[2025-07-28T23:00:47+0700] [Info] [part] Yuki_Haruto has left the server (0:0)
[2025-07-28T23:01:01+0700] [Info] [connection] incoming connection: **************:61248 id: 0
[2025-07-28T23:01:01+0700] [Info] [join] FanID has joined the server (0:**************)
[2025-07-28T23:01:19+0700] [Info] [CMD]: Yuki_Haruto used command /goto bank
[2025-07-28T23:01:21+0700] [Info] [CMD]: Yuki_Haruto used command /bring 1
[2025-07-28T23:01:41+0700] [Info] [CMD]: Yuki_Haruto used command /mysalary
[2025-07-28T23:01:47+0700] [Info] [CMD]: Tata_Wazoski used command /atm
[2025-07-28T23:01:58+0700] [Info] [CMD]: Yuki_Haruto used command /atm
[2025-07-28T23:02:03+0700] [Info] [CMD]: Tata_Wazoski used command /deposit 3000
[2025-07-28T23:02:20+0700] [Info] [CMD]: Yuki_Haruto used command /deposit 1500
[2025-07-28T23:02:25+0700] [Info] [sv:dbg:network:connect] : disconnecting player (1) ...
[2025-07-28T23:02:25+0700] [Info] [part] Tata_Wazoski has left the server (1:1)
[2025-07-28T23:02:37+0700] [Info] [CMD]: Yuki_Haruto used command /goto mechanic
[2025-07-28T23:02:49+0700] [Info] [part] Yuki_Haruto has left the server (0:1)
[2025-07-28T23:21:45+0700] [Info] [INFO] Updated capacity for 4 garbage bins
[2025-07-28T23:42:45+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-29T00:03:45+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-29T16:25:11+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-29T16:25:11+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6e7cd19b96)
[2025-07-29T16:25:11+0700] [Info] [connection] incoming connection: **************:51939 id: 0
[2025-07-29T16:25:11+0700] [Info] [join] FanID has joined the server (0:**************)
[2025-07-29T16:25:13+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:51940)
[2025-07-29T16:25:42+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-29T16:25:46+0700] [Info] [CMD]: Yuki_Haruto used command /jackpack
[2025-07-29T16:25:51+0700] [Info] [CMD]: Yuki_Haruto used command /ahelp
[2025-07-29T16:26:00+0700] [Info] [CMD]: Yuki_Haruto used command /jetpack
[2025-07-29T16:26:29+0700] [Info] [CMD]: Yuki_Haruto used command /goto house 25
[2025-07-29T16:26:43+0700] [Info] [CMD]: Yuki_Haruto used command /stats
[2025-07-29T16:27:00+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-29T16:27:00+0700] [Info] [part] Yuki_Haruto has left the server (0:1)
[2025-07-29T17:38:41+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[2025-07-29T17:38:41+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (93428a6ead1ffc33)
[2025-07-29T17:38:41+0700] [Info] [connection] incoming connection: **************:60076 id: 0
[2025-07-29T17:38:41+0700] [Info] [join] tata has joined the server (0:**************)
[2025-07-29T17:38:47+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:60077)
[2025-07-29T17:39:04+0700] [Info] [CMD]: Tata_Wazoski used command /stats
[2025-07-29T17:39:13+0700] [Info] [CMD]: Tata_Wazoski used command /mysalary
[2025-07-29T17:39:21+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-29T17:39:27+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-29T17:40:39+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-29T17:40:46+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-29T17:40:53+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-29T17:41:18+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-29T17:43:49+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-29T17:44:18+0700] [Info] [CMD]: Tata_Wazoski used command /collecttrash
[2025-07-29T17:44:37+0700] [Info] [CMD]: Tata_Wazoski used command /droptrash
[2025-07-29T17:44:49+0700] [Info] [CMD]: Tata_Wazoski used command /lock
[2025-07-29T17:44:56+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-29T17:48:57+0700] [Info] [CMD]: Tata_Wazoski used command /piss
[2025-07-29T17:49:18+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-29T17:49:28+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-29T17:50:20+0700] [Info] [CMD]: Tata_Wazoski used command /atm
[2025-07-29T17:50:23+0700] [Info] [CMD]: Tata_Wazoski used command /atm
[2025-07-29T17:51:45+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-29T17:52:28+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-29T17:53:13+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-29T17:53:23+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-29T17:53:25+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-29T17:53:25+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-29T17:53:55+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-29T17:53:56+0700] [Info] [CMD]: Tata_Wazoski used command /fish
[2025-07-29T17:54:23+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-29T17:54:45+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-29T17:55:54+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-29T17:55:57+0700] [Info] [CMD]: Tata_Wazoski used command /buy
[2025-07-29T17:56:56+0700] [Info] [CMD]: Tata_Wazoski used command /sellfish
[2025-07-29T17:56:57+0700] [Info] [FISH WAREHOUSE] Stock saved successfully: 68 units
[2025-07-29T17:57:07+0700] [Info] [CMD]: Tata_Wazoski used command /gps
[2025-07-29T17:59:40+0700] [Info] [CMD]: Tata_Wazoski used command /startmine
[2025-07-29T18:01:34+0700] [Info] [CMD]: Tata_Wazoski used command /drink soda
[2025-07-29T18:04:02+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-29T18:04:02+0700] [Info] [part] Tata_Wazoski has left the server (0:1)
[2025-07-29T18:15:45+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-29T18:36:45+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-29T18:57:45+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-29T19:03:17+0700] [Info] [sv:dbg:network:connect] : connecting player (0) with address (*********) ...
[2025-07-29T19:03:17+0700] [Info] [sv:dbg:network:connect] : player (0) assigned key (40662292f7fc200)
[2025-07-29T19:03:17+0700] [Info] [connection] incoming connection: *********:34882 id: 0
[2025-07-29T19:03:17+0700] [Info] [join] ANES_BITI has joined the server (0:*********)
[2025-07-29T19:03:23+0700] [Info] [sv:dbg:network:receive] : player (0) identified (port:47096)
[2025-07-29T19:04:18+0700] [Info] [sv:dbg:network:connect] : disconnecting player (0) ...
[2025-07-29T19:04:18+0700] [Info] [part] ANES_BITI has left the server (0:0)
[2025-07-29T19:18:45+0700] [Info] [INFO] Updated capacity for 2 garbage bins
[2025-07-29T19:31:37+0700] [Info] [connection] incoming connection: **************:65402 id: 0
[2025-07-29T19:31:45+0700] [Info] [join] IordanVg has joined the server (0:**************)
[2025-07-29T19:32:08+0700] [Info] [part] IordanVg has left the server (0:1)
[2025-07-29T19:58:44+0700] [Info] [connection] incoming connection: **************:21141 id: 0
[2025-07-29T19:58:44+0700] [Info] [join] mouhand has joined the server (0:**************)
[2025-07-29T19:59:29+0700] [Info] [part] mouhand has left the server (0:0)
[2025-07-29T23:49:18+0700] [Info] [connection] incoming connection: 156.209.45.253:20539 id: 0
[2025-07-29T23:49:18+0700] [Info] [join] Youssef has joined the server (0:156.209.45.253)
[2025-07-29T23:49:57+0700] [Info] [part] Youssef has left the server (0:0)
[2025-07-30T22:20:53+0700] [Info]  -------------------------------------------
[2025-07-30T22:20:53+0700] [Info]            SampVoice unloading...           
[2025-07-30T22:20:53+0700] [Info]  -------------------------------------------
[2025-07-30T22:20:53+0700] [Info] [sv:dbg:network:free] : module releasing...
[2025-07-30T22:20:53+0700] [Info] [dbg:raknet:free] : module releasing...
[2025-07-30T22:20:53+0700] [Info] [dbg:raknet:free] : module released
[2025-07-30T22:20:53+0700] [Info] [sv:dbg:network:free] : module released
[2025-07-30T22:20:53+0700] [Info] [Pawn.RakNet] plugin unloaded
[2025-07-30T22:20:53+0700] [Info] [EVENT SYSTEM] Complete Event system with Race End Countdown shut down successfully!
[2025-07-30T22:20:53+0700] [Info] [MINING] Destroyed all ore objects
[2025-07-30T22:20:53+0700] [Info] [ADM] Info: Unload EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[2025-07-30T22:20:53+0700] [Info] 

*** Streamer Plugin v2.9.6 by Incognito unloaded ***

[2025-07-30T22:20:53+0700] [Info]  PawnPlus v1.5.1 unloaded
[2025-07-30T22:20:53+0700] [Info] 
[2025-07-30T22:20:53+0700] [Info]  =================================
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |    textdraw-streamer v2.0.3   |
[2025-07-30T22:20:53+0700] [Info]  |           Unloaded            |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |  Coding:                      |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |  Burak (Nexor)                |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |  Compiled:                    |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |  29.09.2023, 16:49:07         |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |  Github:                      |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |  github.com/nexquery          |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |  Discord:                     |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  |  benburakya - Nexor#4730      |
[2025-07-30T22:20:53+0700] [Info]  |                               |
[2025-07-30T22:20:53+0700] [Info]  =================================
[2025-07-30T22:20:53+0700] [Info] 
[2025-07-30T22:20:53+0700] [Info] plugin.mysql: Unloading plugin...
Starting open.mp server (1.4.0.2783) from commit 601de2e1c8da86ff7979821a82bce20568d547dc
Loading component Actors.dll
	Successfully loaded component Actors (1.4.0.2783) with UID c81ca021eae2ad5c
Loading component Checkpoints.dll
	Successfully loaded component Checkpoints (1.4.0.2783) with UID 44a937350d611dde
Loading component Classes.dll
	Successfully loaded component Classes (1.4.0.2783) with UID 8cfb3183976da208
Loading component Console.dll
	Successfully loaded component Console (1.4.0.2783) with UID bfa24e49d0c95ee4
Loading component CustomModels.dll
	Successfully loaded component CustomModels (1.4.0.2783) with UID 15e3cb1e7c77ffff
Loading component Databases.dll
	Successfully loaded component Databases (1.4.0.2783) with UID 80092e7eb5821a96
Loading component Dialogs.dll
	Successfully loaded component Dialogs (1.4.0.2783) with UID 44a111350d611dde
Loading component GangZones.dll
	Successfully loaded component GangZones (1.4.0.2783) with UID b3351d11ee8d8056
Loading component LegacyConfig.dll
	Successfully loaded component LegacyConfig (1.4.0.2783) with UID 24ef6216838f9ffc
Loading component LegacyNetwork.dll
	Successfully loaded component RakNetLegacyNetwork (1.4.0.2783) with UID ea9799fd79cf8442
Loading component Menus.dll
	Successfully loaded component Menus (1.4.0.2783) with UID 621e219eb97ee0b2
Loading component Objects.dll
	Successfully loaded component Objects (1.4.0.2783) with UID 59f8415f72da6160
Loading component Pawn.dll
	Successfully loaded component Pawn (1.4.0.2783) with UID 78906cd9f19c36a6
Loading component pawnraknet.dll
	Successfully loaded component Pawn.RakNet (0.1.6.0) with UID 4a8b15c16d23e42f
Loading component Pickups.dll
	Successfully loaded component Pickups (1.4.0.2783) with UID cf304faa363dd971
Loading component Recordings.dll
	Successfully loaded component Recordings (1.4.0.2783) with UID 871144d399f5f613
Loading component sampvoice.dll
	Successfully loaded component sampvoice open.mp port (0.0.0.1) with UID 6f7d8cbde58c9ce9
Loading component sscanf.dll
	Successfully loaded component sscanf (2.13.8.0) with UID a1e7c01e55caa1f2
Loading component TextDraws.dll
	Successfully loaded component TextDraws (1.4.0.2783) with UID 9b5dc2b1d15c992a
Loading component TextLabels.dll
	Successfully loaded component TextLabels (1.4.0.2783) with UID a0c57ea80a009742
Loading component Timers.dll
	Successfully loaded component Timers (1.4.0.2783) with UID 2ad8124c5ea257a3
Loading component Variables.dll
	Successfully loaded component Variables (1.4.0.2783) with UID 75e121848bc01fa2
Loading component Vehicles.dll
	Successfully loaded component Vehicles (1.4.0.2783) with UID 3f1f62ee9e22ab19
Loaded 23 component(s) from C:\Script SAMP\StriveGM\components
Parsing unknown legacy option long_call_time
[2025-08-08T06:54:47+0700] [Info] 
[2025-08-08T06:54:47+0700] [Info]  ===============================
[2025-08-08T06:54:47+0700] [Info] 
[2025-08-08T06:54:47+0700] [Info]      sscanf component loaded.   
[2025-08-08T06:54:47+0700] [Info] 
[2025-08-08T06:54:47+0700] [Info]          Version: 2.13.8
[2025-08-08T06:54:47+0700] [Info] 
[2025-08-08T06:54:47+0700] [Info]    (c) 2022 Alex "Y_Less" Cole  
[2025-08-08T06:54:47+0700] [Info] 
[2025-08-08T06:54:47+0700] [Info]  ===============================
[2025-08-08T06:54:47+0700] [Info] 
[2025-08-08T06:54:47+0700] [Info] [Pawn.RakNet] plugin v1.6.0 loading...
[2025-08-08T06:54:47+0700] [Info] [Pawn.RakNet] 

    | Pawn.RakNet 1.6.0 | open.mp | 2016 - 2023
    |--------------------------------------------
    | Author and maintainer: katursis


    | Compiled: Feb 12 2023 at 16:51:14
    |--------------------------------------------------------------
    | Repository: https://github.com/katursis/Pawn.RakNet/tree/omp
    |--------------------------------------------------------------
    | Wiki: https://github.com/katursis/Pawn.RakNet/wiki

[2025-08-08T06:54:47+0700] [Info] [sv:dbg:network:init] : module initializing...
[2025-08-08T06:54:47+0700] [Info] [dbg:raknet:init] : module initializing...
[2025-08-08T06:54:47+0700] [Info] [dbg:raknet:init] : module initialized
[2025-08-08T06:54:47+0700] [Info] [sv:dbg:network:init] : module initialized
[2025-08-08T06:54:47+0700] [Info] [sv:dbg:main:Load] : creating 3 work threads...
[2025-08-08T06:54:47+0700] [Info]  -------------------------------------------    
[2025-08-08T06:54:47+0700] [Info]    ___                __   __    _              
[2025-08-08T06:54:47+0700] [Info]   / __| __ _ _ __  _ _\ \ / /__ (_) __ ___    
[2025-08-08T06:54:47+0700] [Info]   \__ \/ _` | '  \| '_ \   / _ \| |/ _/ -_)
[2025-08-08T06:54:47+0700] [Info]   |___/\__,_|_|_|_| .__/\_/\___/|_|\__\___|
[2025-08-08T06:54:47+0700] [Info]                   |_|                           
[2025-08-08T06:54:47+0700] [Info]  -------------------------------------------    
[2025-08-08T06:54:47+0700] [Info]                 SampVoice by MOR                
[2025-08-08T06:54:47+0700] [Info]     Ported to open.mp by AmyrAhmady (iAmir)     
[2025-08-08T06:54:47+0700] [Info]  -------------------------------------------    
[2025-08-08T06:54:47+0700] [Info] Loading plugin: crashdetect.dll
[2025-08-08T06:54:47+0700] [Info]   CrashDetect plugin 4.22-1-gcd608b6
[2025-08-08T06:54:47+0700] [Info] Loading plugin: streamer.dll
[2025-08-08T06:54:47+0700] [Info] 

*** Streamer Plugin v2.9.6 by Incognito loaded ***

[2025-08-08T06:54:47+0700] [Info] Loading plugin: mysql.dll
[2025-08-08T06:54:47+0700] [Info]  >> plugin.mysql: R41-4 successfully loaded.
[2025-08-08T06:54:47+0700] [Info] Loading plugin: samp_bcrypt.dll
[2025-08-08T06:54:47+0700] [Info] [SampBcrypt] [info]: Version: 0.4.1
[2025-08-08T06:54:47+0700] [Info] Loading plugin: textdraw-streamer.dll
[2025-08-08T06:54:47+0700] [Info] 
[2025-08-08T06:54:47+0700] [Info]  =================================
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |    textdraw-streamer v2.0.3   |
[2025-08-08T06:54:47+0700] [Info]  |            Loaded             |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |  Coding:                      |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |  Burak (Nexor)                |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |  Compiled:                    |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |  29.09.2023, 16:49:07         |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |  Github:                      |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |  github.com/nexquery          |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |  Discord:                     |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  |  benburakya - Nexor#4730      |
[2025-08-08T06:54:47+0700] [Info]  |                               |
[2025-08-08T06:54:47+0700] [Info]  =================================
[2025-08-08T06:54:47+0700] [Info] 
[2025-08-08T06:54:47+0700] [Info] Loading plugin: PawnPlus.dll
[2025-08-08T06:54:47+0700] [Info]  PawnPlus v1.5.1 loaded
[2025-08-08T06:54:47+0700] [Info]  Created by IS4
[2025-08-08T06:54:47+0700] [Info] [sv:dbg:network:bind] : voice server running on port 7778
[2025-08-08T06:54:47+0700] [Info]  
[2025-08-08T06:54:47+0700] [Info]  
[2025-08-08T06:54:47+0700] [Info]          ==============================================================
[2025-08-08T06:54:47+0700] [Info]          |                                                            |
[2025-08-08T06:54:47+0700] [Info]          |                                                            |
[2025-08-08T06:54:47+0700] [Info]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[2025-08-08T06:54:47+0700] [Info]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[2025-08-08T06:54:47+0700] [Info]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[2025-08-08T06:54:47+0700] [Info]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[2025-08-08T06:54:47+0700] [Info]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[2025-08-08T06:54:47+0700] [Info]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[2025-08-08T06:54:47+0700] [Info]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[2025-08-08T06:54:47+0700] [Info]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[2025-08-08T06:54:47+0700] [Info]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[2025-08-08T06:54:47+0700] [Info]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[2025-08-08T06:54:47+0700] [Info]          |           Y:::::Y                    S:::::S   I::::I      |
[2025-08-08T06:54:47+0700] [Info]          |           Y:::::Y                    S:::::S   I::::I      |
[2025-08-08T06:54:47+0700] [Info]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[2025-08-08T06:54:47+0700] [Info]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[2025-08-08T06:54:47+0700] [Info]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[2025-08-08T06:54:47+0700] [Info]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[2025-08-08T06:54:47+0700] [Info]          |                                                            |
[2025-08-08T06:54:47+0700] [Info]          |                                                            |
[2025-08-08T06:54:47+0700] [Info]          |                      (c) 2021 MPL v1.1                     |
[2025-08-08T06:54:47+0700] [Info]          |            Alex "Y_Less" Cole and contributors.            |
[2025-08-08T06:54:47+0700] [Info]          |                                                            |
[2025-08-08T06:54:47+0700] [Info]          |                                                            |
[2025-08-08T06:54:48+0700] [Info]          ==============================================================
[2025-08-08T06:54:48+0700] [Info]  
[2025-08-08T06:54:48+0700] [Info]  
[2025-08-08T06:54:48+0700] [Info]  ========================================== 
[2025-08-08T06:54:48+0700] [Info]  |                                        | 
[2025-08-08T06:54:48+0700] [Info]  |   Generating code, this may take a     | 
[2025-08-08T06:54:48+0700] [Info]  |  little bit of time.  Note that this   | 
[2025-08-08T06:54:48+0700] [Info]  |  code generation works best with the   | 
[2025-08-08T06:54:48+0700] [Info]  |     JIT plugin, which you are not      | 
[2025-08-08T06:54:48+0700] [Info]  |     currently using.  Get it here:     | 
[2025-08-08T06:54:48+0700] [Info]  |                                        | 
[2025-08-08T06:54:48+0700] [Info]  |       https://git.io/jit-plugin        | 
[2025-08-08T06:54:48+0700] [Info]  |                                        | 
[2025-08-08T06:54:48+0700] [Info]  |             Please wait...             | 
[2025-08-08T06:54:49+0700] [Info]  |                                        | 
[2025-08-08T06:54:49+0700] [Info]  |             Done in 1479ms!            | 
[2025-08-08T06:54:49+0700] [Info]  |                                        | 
[2025-08-08T06:54:49+0700] [Info]  ========================================== 
[2025-08-08T06:54:49+0700] [Info] *** YSI Info: Script ID: 1
[2025-08-08T06:54:49+0700] [Info] *** YSI Error: Commands must be in lower-case in your source code.
[2025-08-08T06:54:49+0700] [Info]  |                                        | 
[2025-08-08T06:54:49+0700] [Info]  | Server:      open.mp 1.4.0.2783 (W)    | 
[2025-08-08T06:54:49+0700] [Info]  | Started:     08 Aug 2025 - 06:54:47    | 
[2025-08-08T06:54:49+0700] [Info]  |                                        | 
[2025-08-08T06:54:49+0700] [Info]  | Compiler:    3.10.11 (Windows)         | 
[2025-08-08T06:54:49+0700] [Info]  | Includes:    open.mp v037030           | 
[2025-08-08T06:54:49+0700] [Info]  | Codepage:    <none>                    | 
[2025-08-08T06:54:49+0700] [Info]  | Built:       22 Jul 2025 - 15:52:04    | 
[2025-08-08T06:54:49+0700] [Info]  |                                        | 
[2025-08-08T06:54:49+0700] [Info]  | YSI:         v05.10.0006               | 
[2025-08-08T06:54:49+0700] [Info]  | Master:      1                         | 
[2025-08-08T06:54:49+0700] [Info]  |                                        | 
[2025-08-08T06:54:49+0700] [Info]  | JIT:         <none>                    | 
[2025-08-08T06:54:49+0700] [Info]  | Crashdetect: <found>                   | 
[2025-08-08T06:54:49+0700] [Info]  |                                        | 
[2025-08-08T06:54:49+0700] [Info]  ========================================== 
[2025-08-08T06:54:49+0700] [Info]  
[2025-08-08T06:54:49+0700] [Info]  
[2025-08-08T06:54:49+0700] [Info] [SQL] Connection to database passed!
[2025-08-08T06:54:49+0700] [Info] [VOICE] Voice features initialized
[2025-08-08T06:54:49+0700] [Info] [Loaded] Driving Test DMV with Car ID: 1
[2025-08-08T06:54:49+0700] [Info] [Loaded] Anti-AFK System Loaded!
[2025-08-08T06:54:49+0700] [Info] [Loaded] Complete Event System loaded successfully!
[2025-08-08T06:54:49+0700] [Info] [FACTION] Faction vehicle system initialized
[2025-08-08T06:54:49+0700] [Info] [Loaded] Successfully load created 38/38 ore objects!
[2025-08-08T06:54:49+0700] [Info] [ADM] Info: Load EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[2025-08-08T06:54:49+0700] [Info] Legacy Network started on port 7777
[2025-08-08T06:54:49+0700] [Info] [Loaded] 11 Houses
[2025-08-08T06:54:49+0700] [Info] [Loaded] 12 Businesses
[2025-08-08T06:54:49+0700] [Info] [Loaded] 3 Entrance Doors
[2025-08-08T06:54:49+0700] [Info] [Loaded] 3 Jobs
[2025-08-08T06:54:49+0700] [Info] [Loaded] 1 Factions
[2025-08-08T06:54:49+0700] [Info] [Loaded] 6 Crates
[2025-08-08T06:54:49+0700] [Info] [Loaded] 6 Actors
[2025-08-08T06:54:49+0700] [Info] [Loaded] 1 Insurance Lots
[2025-08-08T06:54:49+0700] [Info] [Loaded] 10 Gates
[2025-08-08T06:54:49+0700] [Info] [Lumber System] No lumber trees found in database.
[2025-08-08T06:54:49+0700] [Info] [Loaded] 1 VIP Dealerships
[2025-08-08T06:54:49+0700] [Info] [Loaded] 10 Garbage
[2025-08-08T06:54:49+0700] [Info] [Loaded] 12 Map Icons
[2025-08-08T06:54:49+0700] [Info] ** [MySQL]: Table 'samp.faction_vehicles' doesn't exist
[2025-08-08T06:54:49+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s: %s" < 6456495
[2025-08-08T06:54:49+0700] [Info] [Loaded] 224 Fish Stock
[2025-08-08T06:54:49+0700] [Info] [Loaded] 3000 Component Stock
[2025-08-08T06:54:49+0700] [Info] [Loaded] 4146 Plant Stock
[2025-08-08T06:54:49+0700] [Info] [Loaded] 7 Atm
[2025-08-08T06:54:49+0700] [Info] ** [MySQL]: Table 'samp.motd' doesn't exist
[2025-08-08T06:54:49+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s: %s" < 6456495
[2025-08-08T06:55:00+0700] [Warning] Couldn't announce legacy network to open.mp list.
[2025-08-08T06:55:00+0700] [Warning] 	 Status: 406
[2025-08-08T06:55:00+0700] [Warning] 	 Message: {"error":"failed to query server: socket read timed out"}
[2025-08-08T06:55:00+0700] [Warning] This won't affect the server's behaviour.
[2025-08-08T06:55:37+0700] [Info]  -------------------------------------------
[2025-08-08T06:55:37+0700] [Info]            SampVoice unloading...           
[2025-08-08T06:55:37+0700] [Info]  -------------------------------------------
[2025-08-08T06:55:37+0700] [Info] [sv:dbg:network:free] : module releasing...
[2025-08-08T06:55:37+0700] [Info] [dbg:raknet:free] : module releasing...
[2025-08-08T06:55:37+0700] [Info] [dbg:raknet:free] : module released
[2025-08-08T06:55:37+0700] [Info] [sv:dbg:network:free] : module released
[2025-08-08T06:55:37+0700] [Info] [Pawn.RakNet] plugin unloaded
[2025-08-08T06:55:37+0700] [Info] [EVENT SYSTEM] Complete Event system with Race End Countdown shut down successfully!
[2025-08-08T06:55:37+0700] [Info] [MINING] Destroyed all ore objects
[2025-08-08T06:55:37+0700] [Info] [ADM] Info: Unload EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[2025-08-08T06:55:37+0700] [Info] 

*** Streamer Plugin v2.9.6 by Incognito unloaded ***

[2025-08-08T06:55:37+0700] [Info]  PawnPlus v1.5.1 unloaded
[2025-08-08T06:55:37+0700] [Info] 
[2025-08-08T06:55:37+0700] [Info]  =================================
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |    textdraw-streamer v2.0.3   |
[2025-08-08T06:55:37+0700] [Info]  |           Unloaded            |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |  Coding:                      |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |  Burak (Nexor)                |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |  Compiled:                    |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |  29.09.2023, 16:49:07         |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |  Github:                      |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |  github.com/nexquery          |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |  Discord:                     |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  |  benburakya - Nexor#4730      |
[2025-08-08T06:55:37+0700] [Info]  |                               |
[2025-08-08T06:55:37+0700] [Info]  =================================
[2025-08-08T06:55:37+0700] [Info] 
[2025-08-08T06:55:37+0700] [Info] plugin.mysql: Unloading plugin...
Starting open.mp server (1.4.0.2783) from commit 601de2e1c8da86ff7979821a82bce20568d547dc
Loading component Actors.dll
	Successfully loaded component Actors (1.4.0.2783) with UID c81ca021eae2ad5c
Loading component Checkpoints.dll
	Successfully loaded component Checkpoints (1.4.0.2783) with UID 44a937350d611dde
Loading component Classes.dll
	Successfully loaded component Classes (1.4.0.2783) with UID 8cfb3183976da208
Loading component Console.dll
	Successfully loaded component Console (1.4.0.2783) with UID bfa24e49d0c95ee4
Loading component CustomModels.dll
	Successfully loaded component CustomModels (1.4.0.2783) with UID 15e3cb1e7c77ffff
Loading component Databases.dll
	Successfully loaded component Databases (1.4.0.2783) with UID 80092e7eb5821a96
Loading component Dialogs.dll
	Successfully loaded component Dialogs (1.4.0.2783) with UID 44a111350d611dde
Loading component GangZones.dll
	Successfully loaded component GangZones (1.4.0.2783) with UID b3351d11ee8d8056
Loading component LegacyConfig.dll
	Successfully loaded component LegacyConfig (1.4.0.2783) with UID 24ef6216838f9ffc
Loading component LegacyNetwork.dll
	Successfully loaded component RakNetLegacyNetwork (1.4.0.2783) with UID ea9799fd79cf8442
Loading component Menus.dll
	Successfully loaded component Menus (1.4.0.2783) with UID 621e219eb97ee0b2
Loading component Objects.dll
	Successfully loaded component Objects (1.4.0.2783) with UID 59f8415f72da6160
Loading component Pawn.dll
	Successfully loaded component Pawn (1.4.0.2783) with UID 78906cd9f19c36a6
Loading component pawnraknet.dll
	Successfully loaded component Pawn.RakNet (0.1.6.0) with UID 4a8b15c16d23e42f
Loading component Pickups.dll
	Successfully loaded component Pickups (1.4.0.2783) with UID cf304faa363dd971
Loading component Recordings.dll
	Successfully loaded component Recordings (1.4.0.2783) with UID 871144d399f5f613
Loading component sampvoice.dll
	Successfully loaded component sampvoice open.mp port (0.0.0.1) with UID 6f7d8cbde58c9ce9
Loading component sscanf.dll
	Successfully loaded component sscanf (2.13.8.0) with UID a1e7c01e55caa1f2
Loading component TextDraws.dll
	Successfully loaded component TextDraws (1.4.0.2783) with UID 9b5dc2b1d15c992a
Loading component TextLabels.dll
	Successfully loaded component TextLabels (1.4.0.2783) with UID a0c57ea80a009742
Loading component Timers.dll
	Successfully loaded component Timers (1.4.0.2783) with UID 2ad8124c5ea257a3
Loading component Variables.dll
	Successfully loaded component Variables (1.4.0.2783) with UID 75e121848bc01fa2
Loading component Vehicles.dll
	Successfully loaded component Vehicles (1.4.0.2783) with UID 3f1f62ee9e22ab19
Loaded 23 component(s) from C:\Script SAMP\StriveGM\components
Parsing unknown legacy option long_call_time
[2025-08-08T06:55:48+0700] [Info] 
[2025-08-08T06:55:48+0700] [Info]  ===============================
[2025-08-08T06:55:48+0700] [Info] 
[2025-08-08T06:55:48+0700] [Info]      sscanf component loaded.   
[2025-08-08T06:55:48+0700] [Info] 
[2025-08-08T06:55:48+0700] [Info]          Version: 2.13.8
[2025-08-08T06:55:48+0700] [Info] 
[2025-08-08T06:55:48+0700] [Info]    (c) 2022 Alex "Y_Less" Cole  
[2025-08-08T06:55:48+0700] [Info] 
[2025-08-08T06:55:48+0700] [Info]  ===============================
[2025-08-08T06:55:48+0700] [Info] 
[2025-08-08T06:55:48+0700] [Info] [Pawn.RakNet] plugin v1.6.0 loading...
[2025-08-08T06:55:48+0700] [Info] [Pawn.RakNet] 

    | Pawn.RakNet 1.6.0 | open.mp | 2016 - 2023
    |--------------------------------------------
    | Author and maintainer: katursis


    | Compiled: Feb 12 2023 at 16:51:14
    |--------------------------------------------------------------
    | Repository: https://github.com/katursis/Pawn.RakNet/tree/omp
    |--------------------------------------------------------------
    | Wiki: https://github.com/katursis/Pawn.RakNet/wiki

[2025-08-08T06:55:48+0700] [Info] [sv:dbg:network:init] : module initializing...
[2025-08-08T06:55:48+0700] [Info] [dbg:raknet:init] : module initializing...
[2025-08-08T06:55:48+0700] [Info] [dbg:raknet:init] : module initialized
[2025-08-08T06:55:48+0700] [Info] [sv:dbg:network:init] : module initialized
[2025-08-08T06:55:48+0700] [Info] [sv:dbg:main:Load] : creating 3 work threads...
[2025-08-08T06:55:48+0700] [Info]  -------------------------------------------    
[2025-08-08T06:55:48+0700] [Info]    ___                __   __    _              
[2025-08-08T06:55:48+0700] [Info]   / __| __ _ _ __  _ _\ \ / /__ (_) __ ___    
[2025-08-08T06:55:48+0700] [Info]   \__ \/ _` | '  \| '_ \   / _ \| |/ _/ -_)
[2025-08-08T06:55:48+0700] [Info]   |___/\__,_|_|_|_| .__/\_/\___/|_|\__\___|
[2025-08-08T06:55:48+0700] [Info]                   |_|                           
[2025-08-08T06:55:48+0700] [Info]  -------------------------------------------    
[2025-08-08T06:55:48+0700] [Info]                 SampVoice by MOR                
[2025-08-08T06:55:48+0700] [Info]     Ported to open.mp by AmyrAhmady (iAmir)     
[2025-08-08T06:55:48+0700] [Info]  -------------------------------------------    
[2025-08-08T06:55:48+0700] [Info] Loading plugin: crashdetect.dll
[2025-08-08T06:55:48+0700] [Info]   CrashDetect plugin 4.22-1-gcd608b6
[2025-08-08T06:55:48+0700] [Info] Loading plugin: streamer.dll
[2025-08-08T06:55:48+0700] [Info] 

*** Streamer Plugin v2.9.6 by Incognito loaded ***

[2025-08-08T06:55:48+0700] [Info] Loading plugin: mysql.dll
[2025-08-08T06:55:48+0700] [Info]  >> plugin.mysql: R41-4 successfully loaded.
[2025-08-08T06:55:48+0700] [Info] Loading plugin: samp_bcrypt.dll
[2025-08-08T06:55:48+0700] [Info] [SampBcrypt] [info]: Version: 0.4.1
[2025-08-08T06:55:48+0700] [Info] Loading plugin: textdraw-streamer.dll
[2025-08-08T06:55:48+0700] [Info] 
[2025-08-08T06:55:48+0700] [Info]  =================================
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |    textdraw-streamer v2.0.3   |
[2025-08-08T06:55:48+0700] [Info]  |            Loaded             |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |  Coding:                      |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |  Burak (Nexor)                |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |  Compiled:                    |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |  29.09.2023, 16:49:07         |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |  Github:                      |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |  github.com/nexquery          |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |  Discord:                     |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  |  benburakya - Nexor#4730      |
[2025-08-08T06:55:48+0700] [Info]  |                               |
[2025-08-08T06:55:48+0700] [Info]  =================================
[2025-08-08T06:55:48+0700] [Info] 
[2025-08-08T06:55:48+0700] [Info] Loading plugin: PawnPlus.dll
[2025-08-08T06:55:48+0700] [Info]  PawnPlus v1.5.1 loaded
[2025-08-08T06:55:48+0700] [Info]  Created by IS4
[2025-08-08T06:55:48+0700] [Info] [sv:dbg:network:bind] : voice server running on port 7778
[2025-08-08T06:55:48+0700] [Info]  
[2025-08-08T06:55:48+0700] [Info]  
[2025-08-08T06:55:48+0700] [Info]          ==============================================================
[2025-08-08T06:55:48+0700] [Info]          |                                                            |
[2025-08-08T06:55:48+0700] [Info]          |                                                            |
[2025-08-08T06:55:48+0700] [Info]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[2025-08-08T06:55:48+0700] [Info]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[2025-08-08T06:55:48+0700] [Info]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[2025-08-08T06:55:48+0700] [Info]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[2025-08-08T06:55:48+0700] [Info]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[2025-08-08T06:55:48+0700] [Info]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[2025-08-08T06:55:48+0700] [Info]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[2025-08-08T06:55:48+0700] [Info]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[2025-08-08T06:55:48+0700] [Info]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[2025-08-08T06:55:48+0700] [Info]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[2025-08-08T06:55:48+0700] [Info]          |           Y:::::Y                    S:::::S   I::::I      |
[2025-08-08T06:55:48+0700] [Info]          |           Y:::::Y                    S:::::S   I::::I      |
[2025-08-08T06:55:48+0700] [Info]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[2025-08-08T06:55:48+0700] [Info]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[2025-08-08T06:55:48+0700] [Info]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[2025-08-08T06:55:48+0700] [Info]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[2025-08-08T06:55:48+0700] [Info]          |                                                            |
[2025-08-08T06:55:48+0700] [Info]          |                                                            |
[2025-08-08T06:55:48+0700] [Info]          |                      (c) 2021 MPL v1.1                     |
[2025-08-08T06:55:48+0700] [Info]          |            Alex "Y_Less" Cole and contributors.            |
[2025-08-08T06:55:48+0700] [Info]          |                                                            |
[2025-08-08T06:55:48+0700] [Info]          |                                                            |
[2025-08-08T06:55:48+0700] [Info]          ==============================================================
[2025-08-08T06:55:48+0700] [Info]  
[2025-08-08T06:55:48+0700] [Info]  
[2025-08-08T06:55:48+0700] [Info]  ========================================== 
[2025-08-08T06:55:48+0700] [Info]  |                                        | 
[2025-08-08T06:55:48+0700] [Info]  |   Generating code, this may take a     | 
[2025-08-08T06:55:48+0700] [Info]  |  little bit of time.  Note that this   | 
[2025-08-08T06:55:48+0700] [Info]  |  code generation works best with the   | 
[2025-08-08T06:55:48+0700] [Info]  |     JIT plugin, which you are not      | 
[2025-08-08T06:55:48+0700] [Info]  |     currently using.  Get it here:     | 
[2025-08-08T06:55:48+0700] [Info]  |                                        | 
[2025-08-08T06:55:48+0700] [Info]  |       https://git.io/jit-plugin        | 
[2025-08-08T06:55:48+0700] [Info]  |                                        | 
[2025-08-08T06:55:48+0700] [Info]  |             Please wait...             | 
[2025-08-08T06:55:50+0700] [Info]  |                                        | 
[2025-08-08T06:55:50+0700] [Info]  |             Done in 1455ms!            | 
[2025-08-08T06:55:50+0700] [Info]  |                                        | 
[2025-08-08T06:55:50+0700] [Info]  ========================================== 
[2025-08-08T06:55:50+0700] [Info] *** YSI Info: Script ID: 1
[2025-08-08T06:55:50+0700] [Info] *** YSI Error: Commands must be in lower-case in your source code.
[2025-08-08T06:55:50+0700] [Info]  |                                        | 
[2025-08-08T06:55:50+0700] [Info]  | Server:      open.mp 1.4.0.2783 (W)    | 
[2025-08-08T06:55:50+0700] [Info]  | Started:     08 Aug 2025 - 06:55:48    | 
[2025-08-08T06:55:50+0700] [Info]  |                                        | 
[2025-08-08T06:55:50+0700] [Info]  | Compiler:    3.10.11 (Windows)         | 
[2025-08-08T06:55:50+0700] [Info]  | Includes:    open.mp v037030           | 
[2025-08-08T06:55:50+0700] [Info]  | Codepage:    <none>                    | 
[2025-08-08T06:55:50+0700] [Info]  | Built:       22 Jul 2025 - 15:52:04    | 
[2025-08-08T06:55:50+0700] [Info]  |                                        | 
[2025-08-08T06:55:50+0700] [Info]  | YSI:         v05.10.0006               | 
[2025-08-08T06:55:50+0700] [Info]  | Master:      1                         | 
[2025-08-08T06:55:50+0700] [Info]  |                                        | 
[2025-08-08T06:55:50+0700] [Info]  | JIT:         <none>                    | 
[2025-08-08T06:55:50+0700] [Info]  | Crashdetect: <found>                   | 
[2025-08-08T06:55:50+0700] [Info]  |                                        | 
[2025-08-08T06:55:50+0700] [Info]  ========================================== 
[2025-08-08T06:55:50+0700] [Info]  
[2025-08-08T06:55:50+0700] [Info]  
[2025-08-08T06:55:50+0700] [Info] [SQL] Connection to database passed!
[2025-08-08T06:55:50+0700] [Info] [VOICE] Voice features initialized
[2025-08-08T06:55:50+0700] [Info] [Loaded] Driving Test DMV with Car ID: 1
[2025-08-08T06:55:50+0700] [Info] [Loaded] Anti-AFK System Loaded!
[2025-08-08T06:55:50+0700] [Info] [Loaded] Complete Event System loaded successfully!
[2025-08-08T06:55:50+0700] [Info] [FACTION] Faction vehicle system initialized
[2025-08-08T06:55:50+0700] [Info] [Loaded] Successfully load created 38/38 ore objects!
[2025-08-08T06:55:50+0700] [Info] [ADM] Info: Load EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[2025-08-08T06:55:50+0700] [Info] Legacy Network started on port 7777
[2025-08-08T06:55:50+0700] [Info] [Loaded] 11 Houses
[2025-08-08T06:55:50+0700] [Info] [Loaded] 12 Businesses
[2025-08-08T06:55:50+0700] [Info] [Loaded] 3 Entrance Doors
[2025-08-08T06:55:50+0700] [Info] [Loaded] 3 Jobs
[2025-08-08T06:55:50+0700] [Info] [Loaded] 1 Factions
[2025-08-08T06:55:50+0700] [Info] [Loaded] 6 Crates
[2025-08-08T06:55:50+0700] [Info] [Loaded] 6 Actors
[2025-08-08T06:55:50+0700] [Info] [Loaded] 1 Insurance Lots
[2025-08-08T06:55:50+0700] [Info] [Loaded] 10 Gates
[2025-08-08T06:55:50+0700] [Info] [Lumber System] No lumber trees found in database.
[2025-08-08T06:55:50+0700] [Info] [Loaded] 1 VIP Dealerships
[2025-08-08T06:55:50+0700] [Info] [Loaded] 10 Garbage
[2025-08-08T06:55:50+0700] [Info] [Loaded] 12 Map Icons
[2025-08-08T06:55:50+0700] [Info] ** [MySQL]: Table 'samp.faction_vehicles' doesn't exist
[2025-08-08T06:55:50+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s: %s" < 6456495
[2025-08-08T06:55:50+0700] [Info] [Loaded] 224 Fish Stock
[2025-08-08T06:55:50+0700] [Info] [Loaded] 3000 Component Stock
[2025-08-08T06:55:50+0700] [Info] [Loaded] 4146 Plant Stock
[2025-08-08T06:55:50+0700] [Info] [Loaded] 7 Atm
[2025-08-08T06:55:50+0700] [Info] ** [MySQL]: Table 'samp.motd' doesn't exist
[2025-08-08T06:55:50+0700] [Warning] Insufficient specifiers given to `format`: "[%s] %s: %s" < 6456495
[2025-08-08T06:56:01+0700] [Warning] Couldn't announce legacy network to open.mp list.
[2025-08-08T06:56:01+0700] [Warning] 	 Status: 406
[2025-08-08T06:56:01+0700] [Warning] 	 Message: {"error":"failed to query server: socket read timed out"}
[2025-08-08T06:56:01+0700] [Warning] This won't affect the server's behaviour.
[2025-08-08T06:56:02+0700] [Info]  -------------------------------------------
[2025-08-08T06:56:02+0700] [Info]            SampVoice unloading...           
[2025-08-08T06:56:02+0700] [Info]  -------------------------------------------
[2025-08-08T06:56:02+0700] [Info] [sv:dbg:network:free] : module releasing...
[2025-08-08T06:56:02+0700] [Info] [dbg:raknet:free] : module releasing...
[2025-08-08T06:56:02+0700] [Info] [dbg:raknet:free] : module released
[2025-08-08T06:56:02+0700] [Info] [sv:dbg:network:free] : module released
[2025-08-08T06:56:02+0700] [Info] [Pawn.RakNet] plugin unloaded
[2025-08-08T06:56:02+0700] [Info] [EVENT SYSTEM] Complete Event system with Race End Countdown shut down successfully!
[2025-08-08T06:56:02+0700] [Info] [MINING] Destroyed all ore objects
[2025-08-08T06:56:02+0700] [Info] [ADM] Info: Unload EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[2025-08-08T06:56:02+0700] [Info] 

*** Streamer Plugin v2.9.6 by Incognito unloaded ***

[2025-08-08T06:56:02+0700] [Info]  PawnPlus v1.5.1 unloaded
[2025-08-08T06:56:02+0700] [Info] 
[2025-08-08T06:56:02+0700] [Info]  =================================
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |    textdraw-streamer v2.0.3   |
[2025-08-08T06:56:02+0700] [Info]  |           Unloaded            |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |  Coding:                      |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |  Burak (Nexor)                |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |  Compiled:                    |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |  29.09.2023, 16:49:07         |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |  Github:                      |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |  github.com/nexquery          |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |  Discord:                     |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  |  benburakya - Nexor#4730      |
[2025-08-08T06:56:02+0700] [Info]  |                               |
[2025-08-08T06:56:02+0700] [Info]  =================================
[2025-08-08T06:56:02+0700] [Info] 
[2025-08-08T06:56:02+0700] [Info] plugin.mysql: Unloading plugin...
