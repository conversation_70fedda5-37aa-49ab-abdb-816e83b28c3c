"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = readyHandler;
const discord_js_1 = require("discord.js");
const CommandDeployer_1 = require("../client/CommandDeployer");
const sampQuery_1 = require("../utils/sampQuery");
/**
 * Ready event handler
 */
function readyHandler(client) {
    client.once('ready', async () => {
        if (!client.user) {
            client.logger.error('Client user is null after ready event');
            return;
        }
        client.logger.info(`Bot is ready! Logged in as ${client.user.tag}`, {
            userId: client.user.id,
            guildCount: client.guilds.cache.size,
            userCount: client.users.cache.size,
        });
        try {
            const deployer = new CommandDeployer_1.CommandDeployer(client, client.logger);
            await deployer.deployGuildCommands();
            await updateBotStatus(client);
            startStatusUpdater(client);
            client.logger.info('Bot initialization completed successfully');
        }
        catch (error) {
            client.logger.error('Error during bot initialization', {
                error: error.message,
            });
            await client.errorHandler.handleError(error, {
                source: 'ready_event',
            });
        }
    });
}
/**
 * Update bot status based on SAMP server status
 */
async function updateBotStatus(client) {
    const serverConfig = client.config;
    const sampQuery = new sampQuery_1.SAMPQuery(client.logger);
    try {
        const result = await sampQuery.queryServerInfo(serverConfig.SAMP_SERVER_IP, serverConfig.SAMP_SERVER_PORT, 3000);
        let status;
        let activityType = discord_js_1.ActivityType.Watching;
        if (!result.success || !result.info) {
            status = 'Strive Roleplay';
            client.logger.debug('SAMP server query failed', { error: result.error });
            client.monitoring.recordServerQueryFailed();
        }
        else {
            const info = result.info;
            status = `RP ${info.players}/${info.maxPlayers} Online!`;
            client.logger.debug('SAMP server query successful', {
                players: info.players,
                maxPlayers: info.maxPlayers,
                hostname: info.hostname,
                gamemode: info.gamemode,
            });
            client.monitoring.recordServerQuerySuccess();
        }
        if (client.user) {
            client.user.setActivity(status, { type: activityType });
            client.user.setStatus('online');
        }
    }
    catch (error) {
        client.logger.error('Failed to update bot status', {
            error: error.message,
        });
        // Fallback status
        if (client.user) {
            client.user.setActivity('Strive Roleplay', { type: discord_js_1.ActivityType.Watching });
            client.user.setStatus('online');
        }
    }
}
/**
 * Start periodic status updates
 */
function startStatusUpdater(client) {
    const statusInterval = setInterval(async () => {
        if (!client.isReady()) {
            clearInterval(statusInterval);
            return;
        }
        await updateBotStatus(client);
    }, 10000);
    client.once('destroy', () => {
        clearInterval(statusInterval);
        client.logger.debug('Status updater stopped');
    });
    client.logger.debug('Status updater started');
}
//# sourceMappingURL=ready.js.map