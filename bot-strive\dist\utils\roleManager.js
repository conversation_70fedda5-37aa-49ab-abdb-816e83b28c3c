"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleManager = void 0;
/**
 * Utility class for managing Discord roles
 */
class RoleManager {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Assign a role to a guild member
     */
    async assignRole(guild, userId, roleId, context) {
        try {
            const member = await guild.members.fetch(userId);
            if (!member) {
                return {
                    success: false,
                    error: 'Member not found in guild'
                };
            }
            if (member.roles.cache.has(roleId)) {
                this.logger.info('Member already has the role', {
                    userId,
                    roleId,
                    context
                });
                return {
                    success: true,
                    roleAssigned: false,
                    error: 'Member already has this role'
                };
            }
            const role = await guild.roles.fetch(roleId);
            if (!role) {
                return {
                    success: false,
                    error: `Role with ID ${roleId} not found`
                };
            }
            const botMember = await guild.members.fetchMe();
            if (!botMember.permissions.has('ManageRoles')) {
                return {
                    success: false,
                    error: '<PERSON><PERSON> does not have ManageRoles permission'
                };
            }
            if (role.position >= botMember.roles.highest.position) {
                return {
                    success: false,
                    error: '<PERSON><PERSON> cannot assign this role due to role hierarchy'
                };
            }
            await member.roles.add(role);
            this.logger.info('Role assigned successfully', {
                userId,
                username: member.user.username,
                roleId,
                roleName: role.name,
                context
            });
            return {
                success: true,
                roleAssigned: true,
                roleName: role.name
            };
        }
        catch (error) {
            this.logger.error('Failed to assign role', {
                userId,
                roleId,
                context,
                error: error.message,
                stack: error.stack
            });
            return {
                success: false,
                error: error.message
            };
        }
    }
    /**
     * Remove a role from a guild member
     */
    async removeRole(guild, userId, roleId, context) {
        try {
            const member = await guild.members.fetch(userId);
            if (!member) {
                return {
                    success: false,
                    error: 'Member not found in guild'
                };
            }
            if (!member.roles.cache.has(roleId)) {
                this.logger.info('Member does not have the role', {
                    userId,
                    roleId,
                    context
                });
                return {
                    success: true,
                    roleAssigned: false,
                    error: 'Member does not have this role'
                };
            }
            const role = await guild.roles.fetch(roleId);
            if (!role) {
                return {
                    success: false,
                    error: `Role with ID ${roleId} not found`
                };
            }
            const botMember = await guild.members.fetchMe();
            if (!botMember.permissions.has('ManageRoles')) {
                return {
                    success: false,
                    error: 'Bot does not have ManageRoles permission'
                };
            }
            if (role.position >= botMember.roles.highest.position) {
                return {
                    success: false,
                    error: 'Bot cannot remove this role due to role hierarchy'
                };
            }
            await member.roles.remove(role);
            this.logger.info('Role removed successfully', {
                userId,
                username: member.user.username,
                roleId,
                roleName: role.name,
                context
            });
            return {
                success: true,
                roleAssigned: false,
                roleName: role.name
            };
        }
        catch (error) {
            this.logger.error('Failed to remove role', {
                userId,
                roleId,
                context,
                error: error.message,
                stack: error.stack
            });
            return {
                success: false,
                error: error.message
            };
        }
    }
    /**
     * Check if a member has a specific role
     */
    async hasRole(guild, userId, roleId) {
        try {
            const member = await guild.members.fetch(userId);
            return member ? member.roles.cache.has(roleId) : false;
        }
        catch (error) {
            this.logger.error('Failed to check role', {
                userId,
                roleId,
                error: error.message
            });
            return false;
        }
    }
    /**
     * Get all roles of a member
     */
    async getMemberRoles(guild, userId) {
        try {
            const member = await guild.members.fetch(userId);
            return member ? Array.from(member.roles.cache.values()) : [];
        }
        catch (error) {
            this.logger.error('Failed to get member roles', {
                userId,
                error: error.message
            });
            return [];
        }
    }
}
exports.RoleManager = RoleManager;
//# sourceMappingURL=roleManager.js.map