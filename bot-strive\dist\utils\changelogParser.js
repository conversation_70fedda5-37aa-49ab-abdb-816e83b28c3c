"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangelogParser = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const discord_js_1 = require("discord.js");
const discord_1 = require("../types/discord");
/**
 * Utility class for parsing and formatting changelog
 */
class ChangelogParser {
    changelogPath;
    constructor(changelogPath) {
        this.changelogPath = changelogPath || path_1.default.join(process.cwd(), 'CHANGELOG.md');
    }
    /**
     * Parse CHANGELOG.md file
     */
    parseChangelog() {
        try {
            if (!fs_1.default.existsSync(this.changelogPath)) {
                return {
                    success: false,
                    versions: [],
                    error: 'CHANGELOG.md file not found'
                };
            }
            const content = fs_1.default.readFileSync(this.changelogPath, 'utf-8');
            const versions = this.parseVersions(content);
            return {
                success: true,
                versions
            };
        }
        catch (error) {
            return {
                success: false,
                versions: [],
                error: error.message
            };
        }
    }
    /**
     * Parse versions from changelog content
     */
    parseVersions(content) {
        const lines = content.split('\n');
        const versions = [];
        let currentVersion = null;
        let currentSection = null;
        for (const line of lines) {
            const trimmedLine = line.trim();
            const versionMatch = trimmedLine.match(/^##\s*\[([^\]]+)\]\s*-\s*(.+)$/);
            if (versionMatch) {
                if (currentVersion) {
                    versions.push(currentVersion);
                }
                currentVersion = {
                    version: versionMatch[1] || '',
                    date: versionMatch[2] || '',
                    sections: []
                };
                currentSection = null;
                continue;
            }
            const sectionMatch = trimmedLine.match(/^###\s*(.+)$/);
            if (sectionMatch && currentVersion) {
                currentSection = {
                    title: sectionMatch[1] || '',
                    items: []
                };
                currentVersion.sections.push(currentSection);
                continue;
            }
            const itemMatch = trimmedLine.match(/^-\s*(.+)$/);
            if (itemMatch && currentSection && itemMatch[1]) {
                currentSection.items.push(itemMatch[1]);
                continue;
            }
        }
        if (currentVersion) {
            versions.push(currentVersion);
        }
        return versions;
    }
    /**
     * Get latest version from changelog
     */
    getLatestVersion() {
        const result = this.parseChangelog();
        if (!result.success || result.versions.length === 0) {
            return null;
        }
        return result.versions[0] || null;
    }
    /**
     * Get specific version from changelog
     */
    getVersion(versionNumber) {
        const result = this.parseChangelog();
        if (!result.success) {
            return null;
        }
        return result.versions.find(v => v.version === versionNumber) || null;
    }
    /**
     * Create Discord embed for a version
     */
    createVersionEmbed(version) {
        const embed = new discord_js_1.EmbedBuilder()
            .setTitle(`📋 Changelog - Version ${version.version}`)
            .setDescription(`**Release Date:** ${version.date}`)
            .setColor(discord_1.EmbedColors.INFO)
            .setTimestamp()
            .setFooter({ text: 'Strive Roleplay - Changelog' });
        for (const section of version.sections) {
            if (section.items.length > 0) {
                const items = section.items.slice(0, 10);
                const itemText = items.map(item => `• ${item}`).join('\n');
                const truncatedText = itemText.length > 1024
                    ? itemText.substring(0, 1021) + '...'
                    : itemText;
                embed.addFields([{
                        name: section.title,
                        value: truncatedText || 'No items',
                        inline: false
                    }]);
            }
        }
        return embed;
    }
    /**
     * Create multiple embeds if content is too large
     */
    createVersionEmbeds(version) {
        const embeds = [];
        const maxFieldsPerEmbed = 5;
        const mainEmbed = new discord_js_1.EmbedBuilder()
            .setTitle(`📋 Changelog - Version ${version.version}`)
            .setDescription(`**Release Date:** ${version.date}`)
            .setColor(discord_1.EmbedColors.INFO)
            .setTimestamp()
            .setFooter({ text: 'Strive Roleplay - Changelog' });
        let currentEmbed = mainEmbed;
        let fieldCount = 0;
        for (const section of version.sections) {
            if (section.items.length > 0) {
                if (fieldCount >= maxFieldsPerEmbed) {
                    embeds.push(currentEmbed);
                    currentEmbed = new discord_js_1.EmbedBuilder()
                        .setTitle(`📋 Changelog - Version ${version.version} (continued)`)
                        .setColor(discord_1.EmbedColors.INFO)
                        .setTimestamp();
                    fieldCount = 0;
                }
                const items = section.items.slice(0, 15);
                const itemText = items.map(item => `• ${item}`).join('\n');
                const truncatedText = itemText.length > 1024
                    ? itemText.substring(0, 1021) + '...'
                    : itemText;
                currentEmbed.addFields([{
                        name: section.title,
                        value: truncatedText || 'No items',
                        inline: false
                    }]);
                fieldCount++;
            }
        }
        embeds.push(currentEmbed);
        return embeds;
    }
    /**
     * Get all available versions
     */
    getAllVersions() {
        const result = this.parseChangelog();
        if (!result.success) {
            return [];
        }
        return result.versions.map(v => v.version);
    }
}
exports.ChangelogParser = ChangelogParser;
//# sourceMappingURL=changelogParser.js.map