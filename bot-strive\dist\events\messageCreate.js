"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = messageCreateHandler;
/**
 * Message create event handler
 */
function messageCreateHandler(client) {
    client.on('messageCreate', async (message) => {
        try {
            if (message.author.bot)
                return;
            if (!message.content)
                return;
            if (client.config.NODE_ENV === 'development') {
                client.logger.debug('Message received', {
                    userId: message.author.id,
                    guildId: message.guildId,
                    channelId: message.channelId,
                    content: message.content.substring(0, 100), // Limit content length in logs
                });
            }
            if (message.content.startsWith(client.config.BOT_PREFIX)) {
                client.logger.debug('Prefix command detected', {
                    userId: message.author.id,
                    content: message.content,
                });
            }
        }
        catch (error) {
            client.logger.error('Error handling message', {
                messageId: message.id,
                userId: message.author.id,
                guildId: message.guildId,
                error: error.message,
            });
            await client.errorHandler.handleError(error, {
                source: 'message_handler',
                messageId: message.id,
                userId: message.author.id,
            });
        }
    });
}
//# sourceMappingURL=messageCreate.js.map