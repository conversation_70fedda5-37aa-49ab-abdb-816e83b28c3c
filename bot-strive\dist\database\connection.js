"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = void 0;
const promise_1 = __importDefault(require("mysql2/promise"));
const database_1 = require("../types/database");
/**
 * Database connection manager with connection pooling
 */
class DatabaseManager {
    pool;
    logger;
    constructor(config, logger) {
        this.logger = logger;
        this.pool = promise_1.default.createPool({
            host: config.host,
            port: config.port,
            user: config.user,
            password: config.password,
            database: config.database,
            connectionLimit: config.connectionLimit || 10,
            charset: 'utf8mb4',
            timezone: '+00:00',
            supportBigNumbers: true,
            bigNumberStrings: true,
            queueLimit: 0,
        });
        this.setupEventHandlers();
    }
    /**
     * Set up event handlers for the connection pool
     */
    setupEventHandlers() {
        this.pool.on('connection', (connection) => {
            this.logger.debug(`New database connection established as id ${connection.threadId}`);
        });
        this.pool.on('release', (connection) => {
            this.logger.debug(`Connection ${connection.threadId} released`);
        });
    }
    /**
     * Execute a query with automatic error handling
     */
    async query(sql, values) {
        try {
            this.logger.debug('Executing query', { sql, values });
            let result;
            if (values && Array.isArray(values) && values.length > 0) {
                result = await this.pool.query(sql, values);
            }
            else {
                result = await this.pool.query(sql);
            }
            const [rows] = result;
            if (Array.isArray(rows)) {
                return {
                    success: true,
                    data: rows,
                    affectedRows: rows.length,
                };
            }
            else {
                const resultHeader = rows;
                return {
                    success: true,
                    data: rows,
                    affectedRows: resultHeader.affectedRows,
                    insertId: resultHeader.insertId,
                };
            }
        }
        catch (error) {
            this.logger.error('Database query failed', {
                sql,
                values,
                error: error.message,
                stack: error.stack
            });
            return this.handleDatabaseError(error, sql);
        }
    }
    /**
     * Execute a query (alias for query method)
     */
    async execute(sql, values) {
        return this.query(sql, values);
    }
    /**
     * Get a connection from the pool for transactions
     */
    async getConnection() {
        try {
            const connection = await this.pool.getConnection();
            return new ManagedConnection(connection, this.logger);
        }
        catch (error) {
            throw new database_1.DatabaseError('Failed to get database connection', database_1.DatabaseErrorType.CONNECTION_ERROR, error);
        }
    }
    /**
     * Escape a value for SQL queries (temporary fix for MySQL2 execute issue)
     */
    escape(value) {
        return promise_1.default.escape(value);
    }
    /**
     * Close all connections in the pool
     */
    async end() {
        try {
            await this.pool.end();
            this.logger.info('Database connection pool closed');
        }
        catch (error) {
            this.logger.error('Error closing database pool', { error: error.message });
            throw error;
        }
    }
    /**
     * Handle database errors and convert them to standardized format
     */
    handleDatabaseError(error, sql) {
        this.logger.error('Database query error', {
            error: error.message,
            code: error.code,
            sql,
            stack: error.stack,
        });
        let errorType = database_1.DatabaseErrorType.QUERY_ERROR;
        let message = 'Database query failed';
        switch (error.code) {
            case 'ER_DUP_ENTRY':
                errorType = database_1.DatabaseErrorType.DUPLICATE_ENTRY;
                message = 'Duplicate entry found';
                break;
            case 'ECONNREFUSED':
            case 'ENOTFOUND':
            case 'ETIMEDOUT':
                errorType = database_1.DatabaseErrorType.CONNECTION_ERROR;
                message = 'Database connection failed';
                break;
            case 'ER_NO_SUCH_TABLE':
                errorType = database_1.DatabaseErrorType.NOT_FOUND;
                message = 'Table not found';
                break;
        }
        return {
            success: false,
            error: new database_1.DatabaseError(message, errorType, error).message,
        };
    }
}
exports.DatabaseManager = DatabaseManager;
/**
 * Managed database connection for transactions
 */
class ManagedConnection {
    connection;
    logger;
    constructor(connection, logger) {
        this.connection = connection;
        this.logger = logger;
    }
    async query(sql, values) {
        try {
            this.logger.debug('Executing query on connection', { sql, values });
            const [rows] = await this.connection.execute(sql, values || []);
            if (Array.isArray(rows)) {
                return {
                    success: true,
                    data: rows,
                    affectedRows: rows.length,
                };
            }
            else {
                const resultHeader = rows;
                return {
                    success: true,
                    data: rows,
                    affectedRows: resultHeader.affectedRows,
                    insertId: resultHeader.insertId,
                };
            }
        }
        catch (error) {
            this.logger.error('Connection query error', { error: error.message, sql });
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async execute(sql, values) {
        return this.query(sql, values);
    }
    async beginTransaction() {
        await this.connection.beginTransaction();
        this.logger.debug('Transaction started');
    }
    async commit() {
        await this.connection.commit();
        this.logger.debug('Transaction committed');
    }
    async rollback() {
        await this.connection.rollback();
        this.logger.debug('Transaction rolled back');
    }
    release() {
        this.connection.release();
        this.logger.debug('Connection released');
    }
}
//# sourceMappingURL=connection.js.map