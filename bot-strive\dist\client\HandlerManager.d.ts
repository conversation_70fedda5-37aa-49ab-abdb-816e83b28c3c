import { BotClient } from './BotClient';
import { Logger } from '../types/common';
/**
 * Handler manager for loading commands, buttons, modals, and events
 */
export declare class HandlerManager {
    private client;
    private logger;
    constructor(client: BotClient, logger: Logger);
    /**
     * Load all handlers (commands, buttons, modals, events)
     */
    loadAll(): Promise<void>;
    /**
     * Load slash commands from the commands directory
     */
    loadCommands(): Promise<void>;
    /**
     * Load button handlers from the interactions/buttons directory
     */
    loadButtons(): Promise<void>;
    /**
     * Load modal handlers from the interactions/modals directory
     */
    loadModals(): Promise<void>;
    /**
     * Load event handlers from the events directory
     */
    loadEvents(): Promise<void>;
    /**
     * Validate command structure
     */
    private isValidCommand;
    /**
     * Validate button structure
     */
    private isValidButton;
    /**
     * Validate modal structure
     */
    private isValidModal;
}
//# sourceMappingURL=HandlerManager.d.ts.map