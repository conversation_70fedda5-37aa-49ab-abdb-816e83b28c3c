"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountRepository = void 0;
/**
 * Repository for account-related database operations
 */
class AccountRepository {
    db;
    logger;
    constructor(db, logger) {
        this.db = db;
        this.logger = logger;
    }
    /**
     * Find an account by Discord ID
     */
    async findByDiscordId(discordId) {
        try {
            const result = await this.db.query('SELECT * FROM accounts WHERE discord_id = ? LIMIT 1', [discordId]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Query failed') };
            }
            const accounts = result.data || [];
            const account = accounts.length > 0 ? accounts[0] : null;
            return { success: true, data: account };
        }
        catch (error) {
            this.logger.error('Error finding account by Discord ID', { discordId, error });
            return { success: false, error: error };
        }
    }
    /**
     * Find an account by username
     */
    async findByUsername(username) {
        try {
            const result = await this.db.query('SELECT * FROM accounts WHERE username = ? LIMIT 1', [username]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Query failed') };
            }
            const accounts = result.data || [];
            const account = accounts.length > 0 ? accounts[0] : null;
            return { success: true, data: account };
        }
        catch (error) {
            this.logger.error('Error finding account by username', { username, error });
            return { success: false, error: error };
        }
    }
    /**
     * Create a new account
     */
    async create(accountData) {
        try {
            const existingByUsername = await this.findByUsername(accountData.username);
            if (!existingByUsername.success) {
                return existingByUsername;
            }
            if (existingByUsername.data) {
                return {
                    success: false,
                    error: new Error('Username already exists')
                };
            }
            const existingByDiscord = await this.findByDiscordId(accountData.discord_id);
            if (!existingByDiscord.success) {
                return existingByDiscord;
            }
            if (existingByDiscord.data) {
                return {
                    success: false,
                    error: new Error('Discord ID already registered')
                };
            }
            // Get current timestamp in SAMP format
            const currentDate = new Date();
            const sampDate = currentDate.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            }).replace(/\//g, '/') + ', ' + currentDate.toLocaleTimeString('en-GB', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            const result = await this.db.query('INSERT INTO accounts (username, discord_id, discord_code, password, email, createdAt, LoginDate, IP, Admin, AdminHide, VIP, VIPTime, VIPCoin, isVerified, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                accountData.username,
                accountData.discord_id,
                accountData.discord_code,
                accountData.password || '',
                'Empty', // email default
                sampDate, // createdAt
                sampDate, // LoginDate
                'n/a', // IP default
                0, // Admin default
                0, // AdminHide default
                0, // VIP default
                0, // VIPTime default
                0, // VIPCoin default
                0, // isVerified = 1 (verified through Discord)
                '0000-00-00 00:00:00' // updatedAt default
            ]);
            if (!result.success || !result.insertId) {
                return { success: false, error: new Error(result.error || 'Failed to create account') };
            }
            const createdAccount = await this.findById(result.insertId);
            if (!createdAccount.success || !createdAccount.data) {
                return { success: false, error: new Error('Failed to fetch created account') };
            }
            this.logger.info('Account created successfully', {
                id: result.insertId,
                username: accountData.username,
                discordId: accountData.discord_id,
                discordCode: accountData.discord_code,
                isVerified: 0,
                createdAt: sampDate
            });
            return { success: true, data: createdAccount.data };
        }
        catch (error) {
            this.logger.error('Error creating account', { accountData, error });
            return { success: false, error: error };
        }
    }
    /**
     * Update an account
     */
    async update(id, updateData) {
        try {
            const setParts = [];
            const values = [];
            if (updateData.username !== undefined) {
                setParts.push('username = ?');
                values.push(updateData.username);
            }
            if (updateData.discord_code !== undefined) {
                setParts.push('discord_code = ?');
                values.push(updateData.discord_code);
            }
            if (updateData.password !== undefined) {
                setParts.push('password = ?');
                values.push(updateData.password);
            }
            if (setParts.length === 0) {
                return { success: false, error: new Error('No fields to update') };
            }
            values.push(id);
            const result = await this.db.query(`UPDATE accounts SET ${setParts.join(', ')} WHERE id = ?`, values);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Update failed') };
            }
            const updatedAccount = await this.findById(id);
            if (!updatedAccount.success || !updatedAccount.data) {
                return { success: false, error: new Error('Failed to fetch updated account') };
            }
            this.logger.info('Account updated successfully', { id, updateData });
            return { success: true, data: updatedAccount.data };
        }
        catch (error) {
            this.logger.error('Error updating account', { id, updateData, error });
            return { success: false, error: error };
        }
    }
    /**
     * Delete an account completely
     */
    async delete(id) {
        try {
            // First get account info for logging
            const accountInfo = await this.findById(id);
            const result = await this.db.query('DELETE FROM accounts WHERE id = ?', [id]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Delete failed') };
            }
            if (result.affectedRows === 0) {
                return { success: false, error: new Error('No account found with the specified ID') };
            }
            this.logger.info('Account deleted successfully', {
                id,
                affectedRows: result.affectedRows,
                accountInfo: accountInfo.success ? accountInfo.data : null
            });
            return { success: true, data: true };
        }
        catch (error) {
            this.logger.error('Error deleting account', { id, error });
            return { success: false, error: error };
        }
    }
    /**
     * Update account password
     */
    async updatePassword(id, hashedPassword) {
        try {
            const result = await this.db.query('UPDATE accounts SET password = ? WHERE id = ?', [hashedPassword, id]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Update failed') };
            }
            if (result.affectedRows === 0) {
                return { success: false, error: new Error('No account found with the specified ID') };
            }
            this.logger.info('Account password updated successfully', {
                id,
                affectedRows: result.affectedRows
            });
            return { success: true, data: true };
        }
        catch (error) {
            this.logger.error('Error updating account password', { id, error });
            return { success: false, error: error };
        }
    }
    /**
     * Check account verification status for debugging
     */
    async checkAccountStatus(username) {
        try {
            const result = await this.db.query('SELECT id, username, discord_id, discord_code, isVerified, createdAt FROM accounts WHERE username = ? LIMIT 1', [username]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Query failed') };
            }
            const account = result.data && result.data.length > 0 ? result.data[0] : null;
            return { success: true, data: account };
        }
        catch (error) {
            this.logger.error('Error checking account status', { username, error });
            return { success: false, error: error };
        }
    }
    /**
     * Find an account by ID
     */
    async findById(id) {
        try {
            const result = await this.db.query('SELECT * FROM accounts WHERE id = ? LIMIT 1', [id]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Query failed') };
            }
            const accounts = result.data || [];
            const account = accounts.length > 0 ? accounts[0] : null;
            return { success: true, data: account };
        }
        catch (error) {
            this.logger.error('Error finding account by ID', { id, error });
            return { success: false, error: error };
        }
    }
}
exports.AccountRepository = AccountRepository;
//# sourceMappingURL=AccountRepository.js.map