{"version": 3, "file": "guildMemberAdd.js", "sourceRoot": "", "sources": ["../../src/events/guildMemberAdd.ts"], "names": [], "mappings": ";;AASA,4BAiEC;AA1ED,2CAAoE;AACpE,8CAA+C;AAC/C,sDAAmD;AAGnD;;;GAGG;AACH,mBAAwB,MAAiB;IACvC,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,KAAK,EAAE,MAAmB,EAAE,EAAE;QACxD,IAAI,CAAC;YACH,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACpB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE;oBAC1E,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;oBACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;iBAC/B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;gBAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;oBACzD,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;oBACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;oBAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;oBACxB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,eAAe;iBACtC,CAAC,CAAC;gBAEH,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAEnD,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,UAAU,CAC7C,MAAM,CAAC,KAAK,EACZ,MAAM,CAAC,IAAI,CAAC,EAAE,EACd,MAAM,CAAC,MAAM,CAAC,eAAe,EAC7B,aAAa,CACd,CAAC;gBAEF,IAAI,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;oBAClD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE;wBAC3D,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;wBACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;wBAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;wBACxB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,eAAe;wBACrC,QAAQ,EAAE,UAAU,CAAC,QAAQ;qBAC9B,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;oBAC5B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,EAAE;wBACnE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;wBACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;wBAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;wBACxB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,eAAe;wBACrC,KAAK,EAAE,UAAU,CAAC,KAAK;qBACxB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,+DAA+D,EAAE;oBAClF,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;oBACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;oBAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;iBACzB,CAAC,CAAC;YACL,CAAC;YAED,MAAM,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACnD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,KAAK,EAAG,KAAe,CAAC,KAAK;gBAC7B,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;gBAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;aACzB,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAAC,MAAmB,EAAE,MAAiB;IACtE,IAAI,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,EAAE;gBACxE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,6DAA6D,EAAE;gBAChF,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAE3F,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,EAAE,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE;gBAChE,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB;gBAC3C,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,yBAAY,EAAE;aACpC,QAAQ,CAAC,iBAAiB,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC;aAC/C,cAAc,CACb,MAAM,CAAC,MAAM,CAAC,eAAe;YAC7B,WAAW,MAAM,CAAC,IAAI,CAAC,QAAQ,oCAAoC;gBACnE,mEAAmE;gBACnE,8DAA8D,CAC/D;aACA,QAAQ,CAAC,qBAAW,CAAC,OAAO,CAAC;aAC7B,SAAS,CAAC;YACT;gBACE,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE;oBACL,aAAa,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACnC,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC7B,aAAa,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI;oBAC/B,kBAAkB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,eAAgB,GAAG,IAAI,CAAC,KAAK;iBAClE,CAAC,IAAI,CAAC,IAAI,CAAC;gBACZ,MAAM,EAAE,IAAI;aACb;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,KAAK,EAAE;oBACL,sBAAsB,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE;oBAChD,wBAAwB,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE;oBAClD,0BAA0B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK;iBAChF,CAAC,IAAI,CAAC,IAAI,CAAC;gBACZ,MAAM,EAAE,IAAI;aACb;SACF,CAAC;aACD,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;aAC5C,YAAY,EAAE;aACd,YAAY,EAAE,CAAC;QAElB,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,SAAS,CAAC;gBACrB,IAAI,EAAE,cAAc,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;gBACvC,OAAO,EAAE,SAAS;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,SAAS,CAAC;gBACrB,IAAI,EAAE,cAAc,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;aACxC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YACrC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAC1D,CAAC;QAED,MAAO,cAA8B,CAAC,IAAI,CAAC;YACzC,OAAO,EAAE,GAAG,MAAM,CAAC,IAAI,4BAA4B;YACnD,MAAM,EAAE,CAAC,YAAY,CAAC;SACvB,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;YACtD,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;YACtB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;YAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YACxB,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB;SAC5C,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;YACnD,KAAK,EAAG,KAAe,CAAC,OAAO;YAC/B,KAAK,EAAG,KAAe,CAAC,KAAK;YAC7B,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;YACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YACxB,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB;SAC5C,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}