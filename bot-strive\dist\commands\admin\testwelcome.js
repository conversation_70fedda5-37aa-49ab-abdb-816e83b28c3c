"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const embeds_1 = require("../../utils/embeds");
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('testwelcome')
        .setDescription('Test welcome/leave message system (admin only)')
        .addStringOption(option => option.setName('type')
        .setDescription('Type of test to run')
        .setRequired(true)
        .addChoices({ name: 'Welcome Message', value: 'welcome' }, { name: 'Leave Message', value: 'leave' }, { name: 'Config Check', value: 'config' }))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });
        try {
            const botClient = client || interaction.client;
            const testType = interaction.options.getString('type', true);
            if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Permission Denied', '❌ You need Administrator permission to use this command.', true);
                return;
            }
            if (testType === 'config') {
                const configInfo = [
                    `**WELCOME_ENABLED:** ${botClient.config.WELCOME_ENABLED}`,
                    `**WELCOME_CHANNEL_ID:** ${botClient.config.WELCOME_CHANNEL_ID || 'Not set'}`,
                    `**WELCOME_MESSAGE:** ${botClient.config.WELCOME_MESSAGE ? 'Set' : 'Not set'}`,
                    `**WELCOME_BANNER_URL:** ${botClient.config.WELCOME_BANNER_URL ? 'Set' : 'Not set'}`,
                    `**LEAVE_ENABLED:** ${botClient.config.LEAVE_ENABLED}`,
                    `**ROLE_UNVERIFIED:** ${botClient.config.ROLE_UNVERIFIED || 'Not set'}`
                ].join('\n');
                await embeds_1.InteractionUtils.replyInfo(interaction, 'Configuration Check', `Current welcome/leave system configuration:\n\n${configInfo}`, true);
                return;
            }
            if (testType === 'welcome') {
                if (!interaction.guild) {
                    await embeds_1.InteractionUtils.replyError(interaction, 'Test Failed', '❌ Could not get guild information.', true);
                    return;
                }
                botClient.logger.info('Testing welcome message', {
                    userId: interaction.user.id,
                    username: interaction.user.username,
                    guildId: interaction.guild.id
                });
                try {
                    const guildMember = await interaction.guild.members.fetch(interaction.user.id);
                    botClient.emit('guildMemberAdd', guildMember);
                    await embeds_1.InteractionUtils.replySuccess(interaction, 'Welcome Test', '✅ Welcome message test initiated. Check the welcome channel and logs.', true);
                    botClient.logger.info('Welcome message test completed', {
                        userId: interaction.user.id,
                        testType: 'welcome'
                    });
                }
                catch (error) {
                    await embeds_1.InteractionUtils.replyError(interaction, 'Test Failed', `❌ Welcome message test failed: ${error.message}`, true);
                }
                return;
            }
            if (testType === 'leave') {
                if (!interaction.guild) {
                    await embeds_1.InteractionUtils.replyError(interaction, 'Test Failed', '❌ Could not get guild information.', true);
                    return;
                }
                try {
                    const guildMember = await interaction.guild.members.fetch(interaction.user.id);
                    botClient.emit('guildMemberRemove', guildMember);
                    await embeds_1.InteractionUtils.replySuccess(interaction, 'Leave Test', '✅ Leave message test initiated. Check the welcome channel and logs.', true);
                    botClient.logger.info('Leave message test completed', {
                        userId: interaction.user.id,
                        testType: 'leave'
                    });
                }
                catch (error) {
                    await embeds_1.InteractionUtils.replyError(interaction, 'Test Failed', `❌ Leave message test failed: ${error.message}`, true);
                }
                return;
            }
        }
        catch (error) {
            const botClient = client || interaction.client;
            botClient.logger.error('Error in testwelcome command', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
            await embeds_1.InteractionUtils.replyError(interaction, 'Command Error', 'An unexpected error occurred while testing the welcome system.', true);
        }
    },
};
//# sourceMappingURL=testwelcome.js.map