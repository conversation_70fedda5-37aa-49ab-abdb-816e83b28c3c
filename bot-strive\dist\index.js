"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const BotClient_1 = require("./client/BotClient");
const HandlerManager_1 = require("./client/HandlerManager");
const connection_1 = require("./database/connection");
const config_1 = require("./config");
const utils_1 = require("./utils");
let globalClient = null;
let globalDatabase = null;
let globalLogger = null;
/**
 * Main application entry point
 */
async function main() {
    const logger = (0, utils_1.getLogger)('info', process.env['NODE_ENV'] === 'development');
    globalLogger = logger;
    try {
        logger.info('Starting Discord Bot...');
        const configManager = (0, config_1.getConfigManager)(logger);
        const config = configManager.getConfig();
        configManager.validateRequiredConfig();
        logger.info('Configuration loaded and validated successfully', {
            nodeEnv: config.NODE_ENV,
            logLevel: config.LOG_LEVEL
        });
        const databaseConfig = configManager.getDatabaseConfig();
        const database = new connection_1.DatabaseManager(databaseConfig, logger);
        globalDatabase = database;
        const testQuery = await database.query('SELECT 1 as test');
        if (!testQuery.success) {
            throw new Error(`Database connection failed: ${testQuery.error}`);
        }
        logger.info('Database connection established successfully');
        // Test accounts table and validate schema
        const tableTest = await database.query('SELECT COUNT(*) as count FROM accounts');
        if (tableTest.success) {
            logger.info('Accounts table accessible', { result: tableTest.data });
            // Validate table schema
            const schemaTest = await database.query('DESCRIBE accounts');
            if (schemaTest.success) {
                const columns = schemaTest.data.map(col => col.Field);
                const requiredColumns = ['id', 'username', 'discord_id', 'discord_code'];
                const missingColumns = requiredColumns.filter(col => !columns.includes(col));
                if (missingColumns.length > 0) {
                    logger.warn('Database schema missing required columns', {
                        missingColumns,
                        availableColumns: columns
                    });
                }
                else {
                    logger.info('Database schema validation passed');
                }
            }
        }
        else {
            logger.warn('Accounts table test failed', { error: tableTest.error });
        }
        const webhookConfig = configManager.getWebhookConfig();
        const errorHandler = new utils_1.ErrorHandler(logger, webhookConfig || undefined);
        const client = new BotClient_1.BotClient(config, logger, errorHandler);
        globalClient = client;
        Object.defineProperty(client, 'db', {
            value: database,
            writable: false,
            enumerable: true,
            configurable: true // Make it configurable to allow cache clearing
        });
        const handlerManager = new HandlerManager_1.HandlerManager(client, logger);
        await handlerManager.loadAll();
        await client.start();
        const gracefulShutdown = async (signal) => {
            logger.info(`Received ${signal}, shutting down gracefully...`);
            try {
                // Stop monitoring first to prevent new metrics
                if (client.monitoring) {
                    client.monitoring.stop();
                    logger.info('Monitoring system stopped');
                }
                // Stop the Discord client to prevent new interactions
                if (client) {
                    await client.stop();
                    logger.info('Discord client stopped');
                }
                // Close database connections last
                if (database) {
                    await database.end();
                    logger.info('Database connections closed');
                }
                // Close logger if it has a close method
                if (logger && typeof logger.close === 'function') {
                    logger.close();
                }
                logger.info('Graceful shutdown completed');
                process.exit(0);
            }
            catch (error) {
                logger.error('Error during shutdown', {
                    error: error.message,
                    stack: error.stack
                });
                process.exit(1);
            }
        };
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        logger.info('Bot started successfully and is ready to serve!');
    }
    catch (error) {
        logger.error('Failed to start bot', {
            error: error.message,
            stack: error.stack,
        });
        process.exit(1);
    }
}
process.on('unhandledRejection', async (reason, promise) => {
    const logger = globalLogger || console;
    logger.error('Unhandled Rejection detected', {
        reason: reason instanceof Error ? reason.message : String(reason),
        stack: reason instanceof Error ? reason.stack : undefined,
        promise: promise.toString()
    });
    await performEmergencyCleanup();
    process.exit(1);
});
process.on('uncaughtException', async (error) => {
    const logger = globalLogger || console;
    logger.error('Uncaught Exception detected', {
        error: error.message,
        stack: error.stack
    });
    await performEmergencyCleanup();
    process.exit(1);
});
async function performEmergencyCleanup() {
    try {
        // Stop monitoring first
        if (globalClient?.monitoring) {
            globalClient.monitoring.stop();
            console.log('Emergency: Monitoring system stopped');
        }
        // Stop Discord client
        if (globalClient) {
            await globalClient.stop();
            console.log('Emergency: Discord client stopped');
        }
        // Close database connections last
        if (globalDatabase) {
            await globalDatabase.end();
            console.log('Emergency: Database connections closed');
        }
    }
    catch (cleanupError) {
        console.error('Error during emergency cleanup:', cleanupError);
    }
}
main().catch((error) => {
    console.error('Fatal error during startup:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map