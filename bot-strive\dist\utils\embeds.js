"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionUtils = exports.EmbedUtils = void 0;
const discord_js_1 = require("discord.js");
const discord_1 = require("../types/discord");
/**
 * Utility class for creating standardized embeds
 */
class EmbedUtils {
    /**
     * Create a success embed
     */
    static success(title, description) {
        return new discord_js_1.EmbedBuilder()
            .setTitle(title)
            .setDescription(description)
            .setColor(discord_1.EmbedColors.SUCCESS)
            .setTimestamp();
    }
    /**
     * Create an error embed
     */
    static error(title, description) {
        return new discord_js_1.EmbedBuilder()
            .setTitle(title)
            .setDescription(description)
            .setColor(discord_1.EmbedColors.ERROR)
            .setTimestamp();
    }
    /**
     * Create a warning embed
     */
    static warning(title, description) {
        return new discord_js_1.EmbedBuilder()
            .setTitle(title)
            .setDescription(description)
            .setColor(discord_1.EmbedColors.WARNING)
            .setTimestamp();
    }
    /**
     * Create an info embed
     */
    static info(title, description) {
        return new discord_js_1.EmbedBuilder()
            .setTitle(title)
            .setDescription(description)
            .setColor(discord_1.EmbedColors.INFO)
            .setTimestamp();
    }
    /**
     * Create a primary embed
     */
    static primary(title, description) {
        return new discord_js_1.EmbedBuilder()
            .setTitle(title)
            .setDescription(description)
            .setColor(discord_1.EmbedColors.PRIMARY)
            .setTimestamp();
    }
}
exports.EmbedUtils = EmbedUtils;
/**
 * Interaction response utilities
 */
class InteractionUtils {
    /**
     * Reply with a success message
     */
    static async replySuccess(interaction, title, description, ephemeral = true) {
        const embed = EmbedUtils.success(title, description);
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed] });
        }
        else {
            await interaction.reply({ embeds: [embed], flags: ephemeral ? 64 : undefined });
        }
    }
    /**
     * Reply with an error message
     */
    static async replyError(interaction, title, description, ephemeral = true) {
        const embed = EmbedUtils.error(title, description);
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed] });
        }
        else {
            await interaction.reply({ embeds: [embed], flags: ephemeral ? 64 : undefined });
        }
    }
    /**
     * Reply with a warning message
     */
    static async replyWarning(interaction, title, description, ephemeral = true) {
        const embed = EmbedUtils.warning(title, description);
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed] });
        }
        else {
            await interaction.reply({ embeds: [embed], flags: ephemeral ? 64 : undefined });
        }
    }
    /**
     * Reply with an info message
     */
    static async replyInfo(interaction, title, description, ephemeral = true) {
        const embed = EmbedUtils.info(title, description);
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed] });
        }
        else {
            await interaction.reply({ embeds: [embed], flags: ephemeral ? 64 : undefined });
        }
    }
    /**
     * Defer reply if not already deferred or replied
     */
    static async deferReply(interaction, ephemeral = true) {
        if (!interaction.replied && !interaction.deferred) {
            await interaction.deferReply({ flags: ephemeral ? 64 : undefined });
        }
    }
    /**
     * Edit reply with info message
     */
    static async editReply(interaction, title, description) {
        const embed = EmbedUtils.info(title, description);
        await interaction.editReply({ embeds: [embed] });
    }
    /**
     * Check if interaction can be replied to
     */
    static canReply(interaction) {
        return !interaction.replied && !interaction.deferred;
    }
}
exports.InteractionUtils = InteractionUtils;
//# sourceMappingURL=embeds.js.map