"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const embeds_1 = require("../../utils/embeds");
/**
 * Ping command to test bot responsiveness
 */
const pingCommand = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('ping')
        .setDescription('Check the bot\'s latency and response time'),
    async execute(interaction, _client) {
        // Use InteractionUtils for consistent flag handling
        await embeds_1.InteractionUtils.replyInfo(interaction, 'Pinging...', 'Calculating latency...', true);
        const sent = await interaction.fetchReply();
        const latency = sent.createdTimestamp - interaction.createdTimestamp;
        const apiLatency = Math.round(interaction.client.ws.ping);
        await embeds_1.InteractionUtils.editReply(interaction, 'Pong! 🏓', `**Bot Latency:** ${latency}ms\n**API Latency:** ${apiLatency}ms`);
    },
    cooldown: 5,
};
exports.default = pingCommand;
//# sourceMappingURL=ping.js.map