{"version": 3, "file": "resetpassword.js", "sourceRoot": "", "sources": ["../../../src/commands/admin/resetpassword.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAiH;AAEjH,iDAAgE;AAChE,qFAAkF;AAClF,+CAAsD;AACtD,+CAAiC;AACjC,+CAAiC;AAEpB,QAAA,OAAO,GAAiB;IACnC,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC5B,OAAO,CAAC,eAAe,CAAC;SACxB,cAAc,CAAC,6DAA6D,CAAC;SAC7E,aAAa,CAAC,MAAM,CAAC,EAAE,CACtB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;SACnB,cAAc,CAAC,8BAA8B,CAAC;SAC9C,WAAW,CAAC,IAAI,CAAC,CACrB;SACA,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAwB;IAExF,KAAK,CAAC,OAAO,CAAC,WAAwC,EAAE,MAAkB;QACxE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,OAAO,EAAE,sBAAsB;gBAC/B,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC3E,MAAM,UAAU,GAAG,IAAI,yBAAY,EAAE;iBAClC,QAAQ,CAAC,eAAe,CAAC;iBACzB,cAAc,CAAC,yDAAyD,CAAC;iBACzE,QAAQ,CAAC,qBAAW,CAAC,KAAK,CAAC;iBAC3B,YAAY,EAAE,CAAC;YAElB,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE7D,MAAM,yBAAgB,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAI,MAAc,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YACvD,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,qCAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAEnE,MAAM,aAAa,GAAG,MAAM,WAAW,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACvE,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,mBAAmB,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBACxB,MAAM,aAAa,GAAG,IAAI,yBAAY,EAAE;qBACrC,QAAQ,CAAC,gBAAgB,CAAC;qBAC1B,cAAc,CAAC,QAAQ,UAAU,CAAC,QAAQ,mCAAmC,CAAC;qBAC9E,QAAQ,CAAC,qBAAW,CAAC,OAAO,CAAC;qBAC7B,SAAS,CAAC;oBACT;wBACE,IAAI,EAAE,mBAAmB;wBACzB,KAAK,EAAE,wDAAwD;wBAC/D,MAAM,EAAE,KAAK;qBACd;iBACF,CAAC;qBACD,YAAY,EAAE,CAAC;gBAElB,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;gBACzD,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC;YAEnC,wCAAwC;YACxC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACtB,MAAM,eAAe,GAAG,IAAI,yBAAY,EAAE;qBACvC,QAAQ,CAAC,kBAAkB,CAAC;qBAC5B,cAAc,CAAC,QAAQ,UAAU,CAAC,QAAQ,qCAAqC,CAAC;qBAChF,QAAQ,CAAC,qBAAW,CAAC,OAAO,CAAC;qBAC7B,SAAS,CAAC;oBACT;wBACE,IAAI,EAAE,gBAAgB;wBACtB,KAAK,EAAE;4BACL,iBAAiB,OAAO,CAAC,QAAQ,EAAE;4BACnC,4BAA4B;4BAC5B,qDAAqD;yBACtD,CAAC,IAAI,CAAC,IAAI,CAAC;wBACZ,MAAM,EAAE,KAAK;qBACd;oBACD;wBACE,IAAI,EAAE,YAAY;wBAClB,KAAK,EAAE,4EAA4E;wBACnF,MAAM,EAAE,KAAK;qBACd;iBACF,CAAC;qBACD,YAAY,EAAE,CAAC;gBAElB,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;gBAC3D,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAG,sBAAsB,EAAE,CAAC;YAE9C,MAAM,UAAU,GAAG,EAAE,CAAC;YACtB,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAEnE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,KAAK,CACvC,kCAAkC,eAAe,eAAe,SAAS,EAAE,CAC5E,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,8BAA8B,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,yBAAY,EAAE;qBAC/B,QAAQ,CAAC,kCAAkC,CAAC;qBAC5C,cAAc,CAAC,2DAA2D,CAAC;qBAC3E,QAAQ,CAAC,qBAAW,CAAC,IAAI,CAAC;qBAC1B,SAAS,CAAC;oBACT;wBACE,IAAI,EAAE,wBAAwB;wBAC9B,KAAK,EAAE,KAAK,YAAY,IAAI;wBAC5B,MAAM,EAAE,KAAK;qBACd;oBACD;wBACE,IAAI,EAAE,0BAA0B;wBAChC,KAAK,EAAE;4BACL,qDAAqD;4BACrD,0CAA0C;4BAC1C,oDAAoD;4BACpD,kDAAkD;yBACnD,CAAC,IAAI,CAAC,IAAI,CAAC;wBACZ,MAAM,EAAE,KAAK;qBACd;oBACD;wBACE,IAAI,EAAE,oBAAoB;wBAC1B,KAAK,EAAE,WAAW,MAAM,CAAC,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;wBAClF,MAAM,EAAE,KAAK;qBACd;iBACF,CAAC;qBACD,YAAY,EAAE,CAAA;gBAEjB,IAAI,MAAM,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,CAAC;oBACpC,OAAO,CAAC,SAAS,CAAC;wBAChB,IAAI,EAAE,yCAAyC;wBAC/C,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;qBACxC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,SAAS,CAAC;wBAChB,IAAI,EAAE,yCAAyC;qBAChD,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC7C,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;YAAC,OAAO,OAAO,EAAE,CAAC;gBACjB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;oBAC9C,MAAM,EAAE,UAAU,CAAC,EAAE;oBACrB,KAAK,EAAG,OAAiB,CAAC,OAAO;iBAClC,CAAC,CAAC;YACL,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,yBAAY,EAAE;iBACpC,QAAQ,CAAC,2BAA2B,CAAC;iBACrC,cAAc,CAAC,oCAAoC,UAAU,CAAC,QAAQ,EAAE,CAAC;iBACzE,QAAQ,CAAC,qBAAW,CAAC,OAAO,CAAC;iBAC7B,SAAS,CAAC;gBACT;oBACE,IAAI,EAAE,kBAAkB;oBACxB,KAAK,EAAE;wBACL,gBAAgB,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE,GAAG;wBACxD,qBAAqB,OAAO,CAAC,QAAQ,EAAE;wBACvC,mBAAmB,OAAO,CAAC,EAAE,EAAE;qBAChC,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,kBAAkB;oBACxB,KAAK,EAAE;wBACL,+BAA+B,UAAU,UAAU;wBACnD,wBAAwB,YAAY,CAAC,MAAM,aAAa;wBACxD,kBAAkB,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,gBAAgB,EAAE;wBACnE,oCAAoC;qBACrC,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,MAAM;wBACX,CAAC,CAAC,sDAAsD;wBACxD,CAAC,CAAC,mCAAmC,YAAY,qFAAqF;oBACxI,MAAM,EAAE,KAAK;iBACd;aACF,CAAC;iBACD,YAAY,EAAE;iBACd,SAAS,CAAC;gBACT,IAAI,EAAE,YAAY,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC7C,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE;aAC7C,CAAC,CAAC;YAEL,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBAC7C,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC5B,aAAa,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;gBACxC,YAAY,EAAE,UAAU,CAAC,EAAE;gBAC3B,cAAc,EAAE,UAAU,CAAC,QAAQ;gBACnC,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,YAAY,EAAE,OAAO,CAAC,QAAQ;gBAC9B,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,MAAM;gBACN,aAAa,EAAE,QAAQ;gBACvB,UAAU;gBACV,cAAc,EAAE,YAAY,CAAC,MAAM;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACrD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,KAAK,EAAG,KAAe,CAAC,KAAK;gBAC7B,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC5B,YAAY,EAAE,UAAU,CAAC,EAAE;gBAC3B,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,yBAAY,EAAE;iBAClC,QAAQ,CAAC,uBAAuB,CAAC;iBACjC,cAAc,CAAC,iDAAiD,CAAC;iBACjE,SAAS,CAAC;gBACT;oBACE,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,SAAU,KAAe,CAAC,OAAO,QAAQ;oBAChD,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,iBAAiB;oBACvB,KAAK,EAAE;wBACL,6CAA6C;wBAC7C,wCAAwC;wBACxC,+BAA+B;wBAC/B,8CAA8C;qBAC/C,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;aACF,CAAC;iBACD,QAAQ,CAAC,qBAAW,CAAC,KAAK,CAAC;iBAC3B,YAAY,EAAE,CAAC;YAElB,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAC;AAEF;;;GAGG;AACH,SAAS,sBAAsB;IAC7B,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,OAAO,GAAG,0DAA0D,CAAC;IAC3E,IAAI,QAAQ,GAAG,EAAE,CAAC;IAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAChC,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACxD,QAAQ,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC"}