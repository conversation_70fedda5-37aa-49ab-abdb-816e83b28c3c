import { ValidationResult } from '../types/common';
/**
 * Validation utilities for user input
 */
export declare class ValidationUtils {
    /**
     * Validate username according to UCP rules (for account registration)
     */
    static validateUsername(username: string): ValidationResult;
    /**
     * Validate Discord ID format
     */
    static validateDiscordId(discordId: string): ValidationResult;
    /**
     * Validate verification code
     */
    static validateVerificationCode(code: number): ValidationResult;
    /**
     * Sanitize user input to prevent injection attacks
     */
    static sanitizeInput(input: string): string;
    /**
     * Check if a string is empty or only whitespace
     */
    static isEmpty(value: string): boolean;
    /**
     * Validate email format (if needed for future features)
     */
    static validateEmail(email: string): ValidationResult;
    /**
     * Validate URL format
     */
    static validateUrl(url: string): ValidationResult;
    /**
     * Combine multiple validation results
     */
    static combineValidationResults(...results: ValidationResult[]): ValidationResult;
    /**
     * Create a validation result from a boolean and error message
     */
    static createResult(isValid: boolean, errorMessage?: string): ValidationResult;
}
//# sourceMappingURL=validation.d.ts.map