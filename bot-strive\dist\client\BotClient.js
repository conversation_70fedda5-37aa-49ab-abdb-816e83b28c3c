"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BotClient = void 0;
const discord_js_1 = require("discord.js");
const monitoring_1 = require("../utils/monitoring");
const CommandDeployer_1 = require("./CommandDeployer");
/**
 * Extended Discord client with custom functionality
 */
class BotClient extends discord_js_1.Client {
    commands;
    buttons;
    modals;
    config;
    logger;
    errorHandler;
    monitoring;
    commandDeployer;
    constructor(config, logger, errorHandler) {
        super({
            intents: [
                discord_js_1.GatewayIntentBits.Guilds,
                discord_js_1.GatewayIntentBits.GuildMessages,
                discord_js_1.GatewayIntentBits.GuildMembers,
                discord_js_1.GatewayIntentBits.MessageContent,
                discord_js_1.GatewayIntentBits.DirectMessages,
            ],
            partials: [
                discord_js_1.Partials.Channel,
                discord_js_1.Partials.Message,
                discord_js_1.Partials.User,
                discord_js_1.Partials.GuildMember,
            ],
        });
        this.config = config;
        this.logger = logger;
        this.errorHandler = errorHandler;
        this.monitoring = new monitoring_1.MonitoringSystem(this, logger);
        this.commandDeployer = new CommandDeployer_1.CommandDeployer(this, logger);
        this.commands = new discord_js_1.Collection();
        this.buttons = new discord_js_1.Collection();
        this.modals = new discord_js_1.Collection();
        this.setupErrorHandlers();
    }
    /**
     * Set up global error handlers
     */
    setupErrorHandlers() {
        process.on('unhandledRejection', async (reason, promise) => {
            await this.errorHandler.handleUnhandledRejection(reason, promise);
        });
        process.on('uncaughtException', async (error) => {
            await this.errorHandler.handleUncaughtException(error);
        });
        this.on('error', async (error) => {
            await this.errorHandler.handleError(error, { source: 'discord_client' });
        });
        this.on('warn', (warning) => {
            this.logger.warn('Discord client warning', { warning });
        });
        if (this.config.NODE_ENV === 'development') {
            this.on('debug', (info) => {
                this.logger.debug('Discord client debug', { info });
            });
        }
    }
    /**
     * Start the bot
     */
    async start() {
        try {
            this.logger.info('Starting bot...');
            await this.login(this.config.DISCORD_TOKEN);
            this.monitoring.start();
            this.logger.info('Bot started successfully');
        }
        catch (error) {
            this.logger.error('Failed to start bot', { error: error.message });
            await this.errorHandler.handleError(error, { source: 'bot_startup' });
            throw error;
        }
    }
    /**
     * Stop the bot gracefully
     */
    async stop() {
        try {
            this.logger.info('Stopping bot...');
            this.destroy();
            this.errorHandler.destroy();
            this.logger.info('Bot stopped successfully');
        }
        catch (error) {
            this.logger.error('Error stopping bot', { error: error.message });
            throw error;
        }
    }
    /**
     * Register a slash command
     */
    registerCommand(command) {
        if (!command.data.name) {
            throw new Error('Command must have a name');
        }
        this.commands.set(command.data.name, command);
        this.logger.debug('Registered command', { name: command.data.name });
    }
    /**
     * Register a button handler
     */
    registerButton(button) {
        if (!button.customId) {
            throw new Error('Button must have a customId');
        }
        this.buttons.set(button.customId, button);
        this.logger.debug('Registered button', { customId: button.customId });
    }
    /**
     * Register a modal handler
     */
    registerModal(modal) {
        if (!modal.customId) {
            throw new Error('Modal must have a customId');
        }
        this.modals.set(modal.customId, modal);
        this.logger.debug('Registered modal', { customId: modal.customId });
    }
    /**
     * Get bot statistics
     */
    getStats() {
        return {
            guilds: this.guilds.cache.size,
            users: this.users.cache.size,
            commands: this.commands.size,
            buttons: this.buttons.size,
            modals: this.modals.size,
            uptime: this.uptime || 0,
        };
    }
    /**
     * Check if the bot is ready
     */
    isReady() {
        return this.readyAt !== null;
    }
    /**
     * Get the bot's latency
     */
    getLatency() {
        return this.ws.ping;
    }
}
exports.BotClient = BotClient;
//# sourceMappingURL=BotClient.js.map