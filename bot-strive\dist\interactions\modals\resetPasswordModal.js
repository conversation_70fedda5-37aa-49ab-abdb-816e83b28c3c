"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const AccountRepository_1 = require("../../database/repositories/AccountRepository");
const embeds_1 = require("../../utils/embeds");
const bcrypt_1 = __importDefault(require("bcrypt"));
/**
 * Reset password modal handler - processes password reset requests
 */
const resetPasswordModal = {
    customId: 'reset_password_modal',
    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });
        const client = interaction.client;
        const accountRepo = new AccountRepository_1.AccountRepository(client.db, client.logger);
        try {
            const currentPassword = interaction.fields.getTextInputValue('current_password');
            const newPassword = interaction.fields.getTextInputValue('new_password');
            const confirmPassword = interaction.fields.getTextInputValue('confirm_password');
            if (newPassword !== confirmPassword) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Password Mismatch', '❌ New password and confirmation password do not match. Please try again.', true);
                return;
            }
            const existingAccount = await accountRepo.findByDiscordId(interaction.user.id);
            if (!existingAccount.success || !existingAccount.data) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Account Not Found', '❌ Your account was not found. Please register first.', true);
                return;
            }
            const account = existingAccount.data;
            if (!account.password) {
                await embeds_1.InteractionUtils.replyError(interaction, 'No Password Set', '❌ You don\'t have a password set yet. Please set your password in-game first.', true);
                return;
            }
            const isCurrentPasswordValid = await bcrypt_1.default.compare(currentPassword, account.password);
            if (!isCurrentPasswordValid) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Invalid Password', '❌ Current password is incorrect. Please try again.', true);
                client.logger.warn('Failed password reset attempt - invalid current password', {
                    userId: interaction.user.id,
                    username: interaction.user.username,
                    accountId: account.id,
                    accountUsername: account.username
                });
                return;
            }
            const saltRounds = 12;
            const hashedNewPassword = await bcrypt_1.default.hash(newPassword, saltRounds);
            const updateResult = await accountRepo.updatePassword(account.id, hashedNewPassword);
            if (!updateResult.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Update Failed', 'Failed to update your password. Please try again later.', true);
                return;
            }
            const successEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('🔐 Password Reset Successful!')
                .setDescription('Your password has been updated successfully.')
                .setColor(discord_1.EmbedColors.SUCCESS)
                .addFields([
                {
                    name: '👤 Account Information',
                    value: [
                        `**Username:** \`${account.username}\``,
                        `**Account ID:** \`${account.id}\``,
                        `**Updated:** <t:${Math.floor(Date.now() / 1000)}:F>`
                    ].join('\n'),
                    inline: false
                },
                {
                    name: '🎮 Next Steps',
                    value: [
                        '1. Your new password is now active',
                        '2. You can use it to login in-game',
                        '3. Keep your password secure and don\'t share it',
                        '4. Contact admin if you have any issues'
                    ].join('\n'),
                    inline: false
                }
            ])
                .setTimestamp()
                .setFooter({ text: 'Strive Roleplay - Password Reset' });
            await interaction.editReply({ embeds: [successEmbed] });
            client.logger.info('User password reset successful', {
                userId: interaction.user.id,
                username: interaction.user.username,
                accountId: account.id,
                accountUsername: account.username,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            client.logger.error('Error in reset password modal', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
            await embeds_1.InteractionUtils.replyError(interaction, 'Reset Error', 'An unexpected error occurred while resetting your password.', true);
        }
    },
};
exports.default = resetPasswordModal;
//# sourceMappingURL=resetPasswordModal.js.map