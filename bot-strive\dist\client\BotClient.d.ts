import { Client, Collection } from 'discord.js';
import { ExtendedClient, Slash<PERSON>ommand, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalHandler } from '../types/discord';
import { Config } from '../types/config';
import { Logger } from '../types/common';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../utils/errorHandler';
import { MonitoringSystem } from '../utils/monitoring';
import { CommandDeployer } from './CommandDeployer';
/**
 * Extended Discord client with custom functionality
 */
export declare class BotClient extends Client implements ExtendedClient {
    commands: Collection<string, SlashCommand>;
    buttons: Collection<string, ButtonHandler>;
    modals: Collection<string, ModalHandler>;
    config: Config;
    logger: Logger;
    errorHandler: ErrorHandler;
    monitoring: MonitoringSystem;
    commandDeployer: CommandDeployer;
    constructor(config: Config, logger: Logger, errorHandler: ErrorHandler);
    /**
     * Set up global error handlers
     */
    private setupErrorHandlers;
    /**
     * Start the bot
     */
    start(): Promise<void>;
    /**
     * Stop the bot gracefully
     */
    stop(): Promise<void>;
    /**
     * Register a slash command
     */
    registerCommand(command: SlashCommand): void;
    /**
     * Register a button handler
     */
    registerButton(button: ButtonHandler): void;
    /**
     * Register a modal handler
     */
    registerModal(modal: ModalHandler): void;
    /**
     * Get bot statistics
     */
    getStats(): {
        guilds: number;
        users: number;
        commands: number;
        buttons: number;
        modals: number;
        uptime: number;
    };
    /**
     * Check if the bot is ready
     */
    isReady(): this is Client<true>;
    /**
     * Get the bot's latency
     */
    getLatency(): number;
}
//# sourceMappingURL=BotClient.d.ts.map