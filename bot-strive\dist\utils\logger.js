"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLogger = exports.BotLogger = void 0;
const winston_1 = __importDefault(require("winston"));
/**
 * Custom logger implementation using Winston
 */
class BotLogger {
    winston;
    constructor(level = 'info', isDevelopment = false) {
        const formats = [
            winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
            winston_1.default.format.errors({ stack: true }),
            winston_1.default.format.json(),
        ];
        if (isDevelopment) {
            formats.push(winston_1.default.format.colorize({ all: true }), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
                const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
                return `${timestamp} [${level}]: ${message}${metaStr}`;
            }));
        }
        this.winston = winston_1.default.createLogger({
            level,
            format: winston_1.default.format.combine(...formats),
            transports: [
                new winston_1.default.transports.Console({
                    handleExceptions: true,
                    handleRejections: true,
                }),
                new winston_1.default.transports.File({
                    filename: 'logs/error.log',
                    level: 'error',
                    handleExceptions: true,
                    handleRejections: true,
                }),
                new winston_1.default.transports.File({
                    filename: 'logs/combined.log',
                    handleExceptions: true,
                    handleRejections: true,
                }),
            ],
            exitOnError: false,
        });
        this.ensureLogsDirectory();
    }
    ensureLogsDirectory() {
        const fs = require('fs');
        const path = require('path');
        const logsDir = path.join(process.cwd(), 'logs');
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
    }
    error(message, meta) {
        this.winston.error(message, meta);
    }
    warn(message, meta) {
        this.winston.warn(message, meta);
    }
    info(message, meta) {
        this.winston.info(message, meta);
    }
    debug(message, meta) {
        this.winston.debug(message, meta);
    }
    /**
     * Get the underlying Winston logger instance
     */
    getWinstonLogger() {
        return this.winston;
    }
    /**
     * Close the logger and all transports
     */
    close() {
        this.winston.close();
    }
}
exports.BotLogger = BotLogger;
let logger;
/**
 * Get or create the logger instance
 */
const getLogger = (level, isDevelopment) => {
    if (!logger) {
        logger = new BotLogger(level, isDevelopment);
    }
    return logger;
};
exports.getLogger = getLogger;
//# sourceMappingURL=logger.js.map