"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const changelogParser_1 = require("../../utils/changelogParser");
const embeds_1 = require("../../utils/embeds");
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('changelog')
        .setDescription('Post changelog to configured channel')
        .addStringOption(option => option.setName('version')
        .setDescription('Specific version to post (default: latest)')
        .setRequired(false))
        .addChannelOption(option => option.setName('channel')
        .setDescription('Channel to post changelog (overrides default)')
        .setRequired(false))
        .addBooleanOption(option => option.setName('here')
        .setDescription('Post changelog in current channel instead of configured channel')
        .setRequired(false))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });
        try {
            const botClient = client || interaction.client;
            const parser = new changelogParser_1.ChangelogParser();
            const version = interaction.options.getString('version');
            const channelOption = interaction.options.getChannel('channel');
            const postHere = interaction.options.getBoolean('here') || false;
            if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Access Denied', '❌ You do not have permission to use this command.', true);
                return;
            }
            let changelogVersion;
            if (version) {
                changelogVersion = parser.getVersion(version);
                if (!changelogVersion) {
                    const availableVersions = parser.getAllVersions();
                    await embeds_1.InteractionUtils.replyError(interaction, 'Version Not Found', `❌ Version \`${version}\` not found in changelog.\n\n**Available versions:**\n${availableVersions.slice(0, 10).map(v => `• ${v}`).join('\n')}`, true);
                    return;
                }
            }
            else {
                changelogVersion = parser.getLatestVersion();
                if (!changelogVersion) {
                    await embeds_1.InteractionUtils.replyError(interaction, 'Changelog Error', '❌ Could not read changelog or no versions found.', true);
                    return;
                }
            }
            let targetChannel = null;
            if (postHere) {
                if (interaction.channel && interaction.channel.isTextBased()) {
                    targetChannel = interaction.channel;
                }
            }
            else if (channelOption) {
                if (channelOption.type === 0) { // GUILD_TEXT
                    targetChannel = channelOption;
                }
            }
            else if (botClient.config.CHANGELOG_CHANNEL_ID) {
                try {
                    const channel = await interaction.guild?.channels.fetch(botClient.config.CHANGELOG_CHANNEL_ID);
                    if (channel && channel.isTextBased()) {
                        targetChannel = channel;
                    }
                }
                catch (error) {
                    botClient.logger.warn('Failed to fetch configured changelog channel', {
                        channelId: botClient.config.CHANGELOG_CHANNEL_ID,
                        error: error.message
                    });
                }
            }
            if (!targetChannel) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Channel Error', '❌ No valid channel found. Please:\n• Set `CHANGELOG_CHANNEL_ID` in environment\n• Use `channel` option to specify a channel\n• Use `here: true` to post in current channel', true);
                return;
            }
            const embeds = parser.createVersionEmbeds(changelogVersion);
            try {
                for (const embed of embeds) {
                    await targetChannel.send({ embeds: [embed] });
                }
                await embeds_1.InteractionUtils.replySuccess(interaction, 'Changelog Posted!', `✅ Changelog for version **${changelogVersion.version}** has been posted to ${targetChannel}.`, true);
                botClient.logger.info('Changelog posted successfully', {
                    version: changelogVersion.version,
                    channelId: targetChannel.id,
                    channelName: targetChannel.name,
                    adminId: interaction.user.id,
                    adminUsername: interaction.user.username,
                    embedCount: embeds.length
                });
            }
            catch (error) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Post Failed', `❌ Failed to post changelog to ${targetChannel}. Check bot permissions.`, true);
                botClient.logger.error('Failed to post changelog', {
                    error: error.message,
                    channelId: targetChannel.id,
                    version: changelogVersion.version,
                    adminId: interaction.user.id
                });
            }
        }
        catch (error) {
            const botClient = client || interaction.client;
            botClient.logger.error('Error in changelog command', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
            await embeds_1.InteractionUtils.replyError(interaction, 'Command Error', 'An unexpected error occurred while processing the changelog.', true);
        }
    },
};
//# sourceMappingURL=changelog.js.map