{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/errorHandler.ts"], "names": [], "mappings": ";;;AAAA,2CAAyD;AAEzD,8CAA8D;AAE9D;;GAEG;AACH,MAAa,YAAY;IACf,OAAO,GAAyB,IAAI,CAAC;IACrC,MAAM,CAAS;IACf,UAAU,GAAW,CAAC,CAAC;IACvB,aAAa,GAAW,CAAC,CAAC;IACjB,gBAAgB,GAAG,CAAC,CAAC,CAAC,0BAA0B;IAChD,iBAAiB,GAAG,KAAK,CAAC,CAAC,WAAW;IAEvD,YAAY,MAAc,EAAE,aAA6B;QACvD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,IAAI,0BAAa,CAAC;gBAC/B,EAAE,EAAE,aAAa,CAAC,EAAE;gBACpB,KAAK,EAAE,aAAa,CAAC,KAAK;aAC3B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,KAAY,EAAE,OAAiC;QAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,yCAAyC;QACzC,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACtD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;QAEzB,MAAM,SAAS,GAAG;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO;YACP,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;QAEzD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC7D,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;oBAClD,KAAK,EAAG,YAAsB,CAAC,OAAO;iBACvC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,MAAe,EAAE,OAAyB;QACvE,MAAM,KAAK,GAAG,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAE3E,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;YAC5B,IAAI,EAAE,oBAAoB;YAC1B,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;SAC5B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,KAAY;QACxC,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;YAC5B,IAAI,EAAE,mBAAmB;SAC1B,CAAC,CAAC;QAEH,mDAAmD;QACnD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAY,EAAE,OAAiC;QAC9E,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,yBAAY,EAAE;iBAC7B,QAAQ,CAAC,cAAc,CAAC;iBACxB,cAAc,CAAC,aAAa,KAAK,CAAC,OAAO,QAAQ,CAAC;iBAClD,QAAQ,CAAC,qBAAW,CAAC,KAAK,CAAC;iBAC3B,YAAY,EAAE;iBACd,SAAS,CAAC;gBACT;oBACE,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,KAAK,CAAC,IAAI;oBACjB,MAAM,EAAE,IAAI;iBACb;gBACD;oBACE,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,aAAa,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,gBAAgB,QAAQ;oBAC/E,MAAM,EAAE,KAAK;iBACd;aACF,CAAC,CAAC;YAEL,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,CAAC,SAAS,CAAC;oBACd;wBACE,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE,eAAe,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ;wBACjF,MAAM,EAAE,KAAK;qBACd;iBACF,CAAC,CAAC;YACL,CAAC;YAED,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBACtB,MAAM,EAAE,CAAC,KAAK,CAAC;aAChB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,aAAa,EAAE,KAAK,CAAC,OAAO;gBAC5B,YAAY,EAAG,YAAsB,CAAC,OAAO;aAC9C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,KAAY;QACzC,MAAM,QAAQ,GAA2B;YACvC,iBAAiB,EAAE,mFAAmF;YACtG,eAAe,EAAE,gEAAgE;YACjF,iBAAiB,EAAE,oDAAoD;YACvE,gBAAgB,EAAE,mEAAmE;YACrF,cAAc,EAAE,sDAAsD;SACvE,CAAC;QAEF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,uDAAuD,CAAC;IACzF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CACd,EAA8B,EAC9B,YAAoD;QAEpD,OAAO,KAAK,EAAE,GAAG,IAAO,EAA0B,EAAE;YAClD,IAAI,CAAC;gBACH,OAAO,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,CAAC,KAAc,CAAC,CAAC;gBACnC,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CACvB,EAA8B,EAC9B,aAAqB,CAAC,EACtB,QAAgB,IAAI;QAEpB,OAAO,KAAK,EAAE,GAAG,IAAO,EAAc,EAAE;YACtC,IAAI,SAAgB,CAAC;YAErB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;gBACvD,IAAI,CAAC;oBACH,OAAO,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC3B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,SAAS,GAAG,KAAc,CAAC;oBAE3B,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;wBAC3B,MAAM,SAAS,CAAC;oBAClB,CAAC;oBAED,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;YAED,MAAM,SAAU,CAAC;QACnB,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;CACF;AApMD,oCAoMC"}