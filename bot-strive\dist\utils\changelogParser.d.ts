import { EmbedBuilder } from 'discord.js';
export interface ChangelogVersion {
    version: string;
    date: string;
    sections: ChangelogSection[];
}
export interface ChangelogSection {
    title: string;
    items: string[];
}
export interface ChangelogParseResult {
    success: boolean;
    versions: ChangelogVersion[];
    error?: string;
}
/**
 * Utility class for parsing and formatting changelog
 */
export declare class ChangelogParser {
    private changelogPath;
    constructor(changelogPath?: string);
    /**
     * Parse CHANGELOG.md file
     */
    parseChangelog(): ChangelogParseResult;
    /**
     * Parse versions from changelog content
     */
    private parseVersions;
    /**
     * Get latest version from changelog
     */
    getLatestVersion(): ChangelogVersion | null;
    /**
     * Get specific version from changelog
     */
    getVersion(versionNumber: string): ChangelogVersion | null;
    /**
     * Create Discord embed for a version
     */
    createVersionEmbed(version: ChangelogVersion): EmbedBuilder;
    /**
     * Create multiple embeds if content is too large
     */
    createVersionEmbeds(version: ChangelogVersion): EmbedBuilder[];
    /**
     * Get all available versions
     */
    getAllVersions(): string[];
}
//# sourceMappingURL=changelogParser.d.ts.map