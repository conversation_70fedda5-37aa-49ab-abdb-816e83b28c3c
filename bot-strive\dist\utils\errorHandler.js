"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHandler = void 0;
const discord_js_1 = require("discord.js");
const discord_1 = require("../types/discord");
/**
 * Centralized error handling utility
 */
class ErrorHandler {
    webhook = null;
    logger;
    errorCount = 0;
    lastErrorTime = 0;
    ERROR_RATE_LIMIT = 5; // Max 5 errors per minute
    ERROR_RATE_WINDOW = 60000; // 1 minute
    constructor(logger, webhookConfig) {
        this.logger = logger;
        if (webhookConfig) {
            this.webhook = new discord_js_1.WebhookClient({
                id: webhookConfig.id,
                token: webhookConfig.token,
            });
        }
    }
    /**
     * Handle and log an error
     */
    async handleError(error, context) {
        const now = Date.now();
        // Reset error count if window has passed
        if (now - this.lastErrorTime > this.ERROR_RATE_WINDOW) {
            this.errorCount = 0;
        }
        this.errorCount++;
        this.lastErrorTime = now;
        const errorInfo = {
            message: error.message,
            stack: error.stack,
            name: error.name,
            timestamp: new Date().toISOString(),
            context,
            errorCount: this.errorCount,
        };
        this.logger.error('Unhandled error occurred', errorInfo);
        if (this.webhook && this.errorCount <= this.ERROR_RATE_LIMIT) {
            try {
                await this.sendErrorToWebhook(error, context);
            }
            catch (webhookError) {
                this.logger.warn('Failed to send error to webhook', {
                    error: webhookError.message
                });
            }
        }
        else if (this.errorCount > this.ERROR_RATE_LIMIT) {
            this.logger.warn('Error rate limit exceeded, skipping webhook notification');
        }
    }
    /**
     * Handle unhandled promise rejections
     */
    async handleUnhandledRejection(reason, promise) {
        const error = reason instanceof Error ? reason : new Error(String(reason));
        await this.handleError(error, {
            type: 'unhandledRejection',
            promise: promise.toString(),
        });
    }
    /**
     * Handle uncaught exceptions
     */
    async handleUncaughtException(error) {
        await this.handleError(error, {
            type: 'uncaughtException',
        });
        // in production, we might want to exit the process
        process.exit(1);
    }
    /**
     * Send error information to Discord webhook
     */
    async sendErrorToWebhook(error, context) {
        if (!this.webhook)
            return;
        try {
            const embed = new discord_js_1.EmbedBuilder()
                .setTitle('🚨 Bot Error')
                .setDescription(`\`\`\`js\n${error.message}\`\`\``)
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp()
                .addFields([
                {
                    name: 'Error Type',
                    value: error.name,
                    inline: true,
                },
                {
                    name: 'Stack Trace',
                    value: `\`\`\`js\n${error.stack?.substring(0, 1000) || 'No stack trace'}\`\`\``,
                    inline: false,
                },
            ]);
            if (context) {
                embed.addFields([
                    {
                        name: 'Context',
                        value: `\`\`\`json\n${JSON.stringify(context, null, 2).substring(0, 1000)}\`\`\``,
                        inline: false,
                    },
                ]);
            }
            await this.webhook.send({
                embeds: [embed],
            });
        }
        catch (webhookError) {
            this.logger.error('Failed to send error to webhook', {
                originalError: error.message,
                webhookError: webhookError.message,
            });
        }
    }
    /**
     * Create a safe error message for users (without sensitive information)
     */
    static createUserFriendlyError(error) {
        const errorMap = {
            'ValidationError': 'The information you provided is not valid. Please check your input and try again.',
            'DatabaseError': 'There was a problem with the database. Please try again later.',
            'PermissionError': 'You do not have permission to perform this action.',
            'RateLimitError': 'You are doing that too often. Please wait a moment and try again.',
            'NetworkError': 'There was a network problem. Please try again later.',
        };
        return errorMap[error.name] || 'An unexpected error occurred. Please try again later.';
    }
    /**
     * Wrap an async function with error handling
     */
    static wrapAsync(fn, errorHandler) {
        return async (...args) => {
            try {
                return await fn(...args);
            }
            catch (error) {
                await errorHandler(error);
                return undefined;
            }
        };
    }
    /**
     * Create a retry wrapper for functions that might fail
     */
    static createRetryWrapper(fn, maxRetries = 3, delay = 1000) {
        return async (...args) => {
            let lastError;
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    return await fn(...args);
                }
                catch (error) {
                    lastError = error;
                    if (attempt === maxRetries) {
                        throw lastError;
                    }
                    await new Promise(resolve => setTimeout(resolve, delay * attempt));
                }
            }
            throw lastError;
        };
    }
    /**
     * Destroy the webhook client
     */
    destroy() {
        if (this.webhook) {
            this.webhook.destroy();
        }
    }
}
exports.ErrorHandler = ErrorHandler;
//# sourceMappingURL=errorHandler.js.map