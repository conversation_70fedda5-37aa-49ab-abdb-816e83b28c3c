import { Guild, Role } from 'discord.js';
import { Logger } from '../types/common';
export interface RoleAssignmentResult {
    success: boolean;
    roleAssigned?: boolean;
    roleName?: string;
    error?: string;
}
/**
 * Utility class for managing Discord roles
 */
export declare class RoleManager {
    private logger;
    constructor(logger: Logger);
    /**
     * Assign a role to a guild member
     */
    assignRole(guild: Guild, userId: string, roleId: string, context?: string): Promise<RoleAssignmentResult>;
    /**
     * Remove a role from a guild member
     */
    removeRole(guild: Guild, userId: string, roleId: string, context?: string): Promise<RoleAssignmentResult>;
    /**
     * Check if a member has a specific role
     */
    hasRole(guild: Guild, userId: string, roleId: string): Promise<boolean>;
    /**
     * Get all roles of a member
     */
    getMemberRoles(guild: Guild, userId: string): Promise<Role[]>;
}
//# sourceMappingURL=roleManager.d.ts.map