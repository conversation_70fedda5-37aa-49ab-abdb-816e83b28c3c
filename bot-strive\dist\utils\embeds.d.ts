import { Embed<PERSON>uild<PERSON>, ChatInputCommandInteraction, ButtonInteraction, ModalSubmitInteraction } from 'discord.js';
/**
 * Utility class for creating standardized embeds
 */
export declare class EmbedUtils {
    /**
     * Create a success embed
     */
    static success(title: string, description: string): EmbedBuilder;
    /**
     * Create an error embed
     */
    static error(title: string, description: string): EmbedBuilder;
    /**
     * Create a warning embed
     */
    static warning(title: string, description: string): EmbedBuilder;
    /**
     * Create an info embed
     */
    static info(title: string, description: string): EmbedBuilder;
    /**
     * Create a primary embed
     */
    static primary(title: string, description: string): EmbedBuilder;
}
/**
 * Interaction response utilities
 */
export declare class InteractionUtils {
    /**
     * Reply with a success message
     */
    static replySuccess(interaction: ChatInputCommandInteraction | ButtonInteraction | ModalSubmitInteraction, title: string, description: string, ephemeral?: boolean): Promise<void>;
    /**
     * Reply with an error message
     */
    static replyError(interaction: ChatInputCommandInteraction | ButtonInteraction | ModalSubmitInteraction, title: string, description: string, ephemeral?: boolean): Promise<void>;
    /**
     * Reply with a warning message
     */
    static replyWarning(interaction: ChatInputCommandInteraction | ButtonInteraction | ModalSubmitInteraction, title: string, description: string, ephemeral?: boolean): Promise<void>;
    /**
     * Reply with an info message
     */
    static replyInfo(interaction: ChatInputCommandInteraction | ButtonInteraction | ModalSubmitInteraction, title: string, description: string, ephemeral?: boolean): Promise<void>;
    /**
     * Defer reply if not already deferred or replied
     */
    static deferReply(interaction: ChatInputCommandInteraction | ButtonInteraction | ModalSubmitInteraction, ephemeral?: boolean): Promise<void>;
    /**
     * Edit reply with info message
     */
    static editReply(interaction: ChatInputCommandInteraction | ButtonInteraction | ModalSubmitInteraction, title: string, description: string): Promise<void>;
    /**
     * Check if interaction can be replied to
     */
    static canReply(interaction: ChatInputCommandInteraction | ButtonInteraction | ModalSubmitInteraction): boolean;
}
//# sourceMappingURL=embeds.d.ts.map