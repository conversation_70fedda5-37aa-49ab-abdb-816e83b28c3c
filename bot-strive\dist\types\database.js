"use strict";
/**
 * Database model interfaces and types
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseError = exports.DatabaseErrorType = void 0;
/**
 * Database error types
 */
var DatabaseErrorType;
(function (DatabaseErrorType) {
    DatabaseErrorType["CONNECTION_ERROR"] = "CONNECTION_ERROR";
    DatabaseErrorType["QUERY_ERROR"] = "QUERY_ERROR";
    DatabaseErrorType["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    DatabaseErrorType["DUPLICATE_ENTRY"] = "DUPLICATE_ENTRY";
    DatabaseErrorType["NOT_FOUND"] = "NOT_FOUND";
})(DatabaseErrorType || (exports.DatabaseErrorType = DatabaseErrorType = {}));
/**
 * Custom database error class
 */
class DatabaseError extends Error {
    type;
    originalError;
    constructor(message, type, originalError) {
        super(message);
        this.type = type;
        this.originalError = originalError;
        this.name = 'DatabaseError';
    }
}
exports.DatabaseError = DatabaseError;
//# sourceMappingURL=database.js.map