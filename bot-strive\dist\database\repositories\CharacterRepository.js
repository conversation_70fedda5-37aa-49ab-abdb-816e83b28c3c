"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CharacterRepository = void 0;
/**
 * Repository for character data operations
 */
class CharacterRepository {
    db;
    logger;
    constructor(db, logger) {
        this.db = db;
        this.logger = logger;
    }
    /**
     * Get online admins (based on recent LastLogin)
     */
    async getOnlineAdmins() {
        try {
            const thirtyMinutesAgo = Math.floor(Date.now() / 1000) - (30 * 60);
            const result = await this.db.query(`SELECT id, username, \`character\`, Admin as adminLevel, skin, LastLogin, AdminHide as isHidden 
         FROM characters 
         WHERE Admin > 0 AND LastLogin > ? 
         ORDER BY Admin DESC, LastLogin DESC`, [thirtyMinutesAgo]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Query failed') };
            }
            const onlineAdmins = (result.data || []).map((row) => ({
                id: row.id,
                username: row.username,
                character: row.character,
                adminLevel: row.adminLevel,
                skin: row.skin,
                lastLogin: row.LastLogin,
                isHidden: row.isHidden === 1
            }));
            this.logger.info('Online admins retrieved', {
                count: onlineAdmins.length,
                adminLevels: onlineAdmins.map(a => a.adminLevel)
            });
            return { success: true, data: onlineAdmins };
        }
        catch (error) {
            this.logger.error('Error getting online admins', { error });
            return { success: false, error: error };
        }
    }
    /**
     * Get character by username
     */
    async getCharacterByUsername(username) {
        try {
            const result = await this.db.query('SELECT * FROM characters WHERE username = ? LIMIT 1', [username]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Query failed') };
            }
            const character = result.data && result.data.length > 0 ? result.data[0] : null;
            return { success: true, data: character };
        }
        catch (error) {
            this.logger.error('Error getting character by username', { username, error });
            return { success: false, error: error };
        }
    }
    /**
     * Get character by Discord ID (through accounts table)
     */
    async getCharacterByDiscordId(discordId) {
        try {
            const result = await this.db.query(`SELECT c.* FROM characters c 
         JOIN accounts a ON c.username = a.username 
         WHERE a.discord_id = ? LIMIT 1`, [discordId]);
            if (!result.success) {
                return { success: false, error: new Error(result.error || 'Query failed') };
            }
            const character = result.data && result.data.length > 0 ? result.data[0] : null;
            return { success: true, data: character };
        }
        catch (error) {
            this.logger.error('Error getting character by Discord ID', { discordId, error });
            return { success: false, error: error };
        }
    }
    /**
     * Get admin level name
     */
    getAdminLevelName(level) {
        const adminLevels = {
            0: 'Player',
            1: 'Helper',
            2: 'Junior Admin',
            3: 'Admin',
            4: 'Senior Admin',
            5: 'Head Admin',
            6: 'Executive Admin',
            7: 'Co-Owner',
            8: 'Owner',
            9: 'Developer',
            10: 'Founder'
        };
        return adminLevels[level] || `Admin Level ${level}`;
    }
    /**
     * Get job name by ID
     */
    getJobName(jobId) {
        const jobs = {
            0: 'Unemployed',
            1: 'Taxi Driver',
            2: 'Bus Driver',
            3: 'Trucker',
            4: 'Mechanic',
            5: 'Medic',
            6: 'Police Officer',
            7: 'Lawyer',
            8: 'News Reporter',
            9: 'Hitman',
            10: 'Drug Dealer'
        };
        return jobs[jobId] || `Job ID ${jobId}`;
    }
    /**
     * Format money with commas
     */
    formatMoney(amount) {
        return amount.toLocaleString('en-US');
    }
    /**
     * Convert timestamp to readable date
     */
    formatTimestamp(timestamp) {
        if (timestamp === 0)
            return 'Never';
        return new Date(timestamp * 1000).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}
exports.CharacterRepository = CharacterRepository;
//# sourceMappingURL=CharacterRepository.js.map