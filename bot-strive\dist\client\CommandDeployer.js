"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommandDeployer = void 0;
const discord_js_1 = require("discord.js");
/**
 * Command deployment utility for registering slash commands with Discord
 */
class CommandDeployer {
    rest;
    client;
    logger;
    constructor(client, logger) {
        this.client = client;
        this.logger = logger;
        this.rest = new discord_js_1.REST({ version: '10' }).setToken(client.config.DISCORD_TOKEN);
    }
    /**
     * Deploy commands to Discord (guild-specific)
     */
    async deployGuildCommands() {
        try {
            const commands = this.getCommandsData();
            this.logger.info('Started refreshing application (/) commands', {
                count: commands.length,
                guildId: this.client.config.DISCORD_GUILD_ID,
            });
            const data = await this.rest.put(discord_js_1.Routes.applicationGuildCommands(this.client.config.DISCORD_CLIENT_ID, this.client.config.DISCORD_GUILD_ID), { body: commands });
            this.logger.info('Successfully reloaded application (/) commands', {
                count: data.length,
                guildId: this.client.config.DISCORD_GUILD_ID,
            });
        }
        catch (error) {
            this.logger.error('Failed to deploy guild commands', {
                error: error.message,
                guildId: this.client.config.DISCORD_GUILD_ID,
            });
            throw error;
        }
    }
    /**
     * Deploy commands globally (takes up to 1 hour to propagate)
     */
    async deployGlobalCommands() {
        try {
            const commands = this.getCommandsData();
            this.logger.info('Started refreshing global application (/) commands', {
                count: commands.length,
            });
            const data = await this.rest.put(discord_js_1.Routes.applicationCommands(this.client.config.DISCORD_CLIENT_ID), { body: commands });
            this.logger.info('Successfully reloaded global application (/) commands', {
                count: data.length,
            });
        }
        catch (error) {
            this.logger.error('Failed to deploy global commands', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Delete all guild commands
     */
    async deleteGuildCommands() {
        try {
            this.logger.info('Started deleting guild application (/) commands', {
                guildId: this.client.config.DISCORD_GUILD_ID,
            });
            await this.rest.put(discord_js_1.Routes.applicationGuildCommands(this.client.config.DISCORD_CLIENT_ID, this.client.config.DISCORD_GUILD_ID), { body: [] });
            this.logger.info('Successfully deleted all guild application (/) commands', {
                guildId: this.client.config.DISCORD_GUILD_ID,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete guild commands', {
                error: error.message,
                guildId: this.client.config.DISCORD_GUILD_ID,
            });
            throw error;
        }
    }
    /**
     * Delete all global commands
     */
    async deleteGlobalCommands() {
        try {
            this.logger.info('Started deleting global application (/) commands');
            await this.rest.put(discord_js_1.Routes.applicationCommands(this.client.config.DISCORD_CLIENT_ID), { body: [] });
            this.logger.info('Successfully deleted all global application (/) commands');
        }
        catch (error) {
            this.logger.error('Failed to delete global commands', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get commands data for deployment
     */
    getCommandsData() {
        const commands = [];
        for (const command of this.client.commands.values()) {
            if (command.data instanceof discord_js_1.SlashCommandBuilder || command.data instanceof discord_js_1.ContextMenuCommandBuilder) {
                commands.push(command.data.toJSON());
            }
            else {
                this.logger.warn('Invalid command data structure', {
                    commandName: 'data' in command.data ? command.data.name : 'unknown',
                });
            }
        }
        return commands;
    }
    /**
     * Get existing commands from Discord
     */
    async getExistingGuildCommands() {
        try {
            const commands = await this.rest.get(discord_js_1.Routes.applicationGuildCommands(this.client.config.DISCORD_CLIENT_ID, this.client.config.DISCORD_GUILD_ID));
            return commands;
        }
        catch (error) {
            this.logger.error('Failed to fetch existing guild commands', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get existing global commands from Discord
     */
    async getExistingGlobalCommands() {
        try {
            const commands = await this.rest.get(discord_js_1.Routes.applicationCommands(this.client.config.DISCORD_CLIENT_ID));
            return commands;
        }
        catch (error) {
            this.logger.error('Failed to fetch existing global commands', {
                error: error.message,
            });
            throw error;
        }
    }
}
exports.CommandDeployer = CommandDeployer;
//# sourceMappingURL=CommandDeployer.js.map