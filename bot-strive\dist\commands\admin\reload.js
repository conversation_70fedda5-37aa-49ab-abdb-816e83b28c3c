"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const HandlerManager_1 = require("../../client/HandlerManager");
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('reload')
        .setDescription('Reload all bot commands and handlers (Admin only)')
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    async execute(interaction, client) {
        if (!client) {
            await interaction.reply({
                content: '❌ Client not available',
                flags: 64
            });
            return;
        }
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            const errorEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('❌ Access Denied')
                .setDescription('You need Administrator permissions to use this command.')
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp();
            await interaction.reply({ embeds: [errorEmbed], flags: 64 });
            return;
        }
        await interaction.deferReply({ flags: 64 });
        try {
            const startTime = Date.now();
            const oldCommandCount = client.commands.size;
            const oldButtonCount = client.buttons.size;
            const oldModalCount = client.modals.size;
            client.commands.clear();
            client.buttons.clear();
            client.modals.clear();
            const handlerManager = new HandlerManager_1.HandlerManager(client, client.logger);
            await handlerManager.loadCommands();
            await handlerManager.loadButtons();
            await handlerManager.loadModals();
            const endTime = Date.now();
            const reloadTime = endTime - startTime;
            const deployer = client.commandDeployer;
            if (deployer) {
                await deployer.deployGuildCommands();
            }
            const embed = new discord_js_1.EmbedBuilder()
                .setTitle('🔄 Reload Complete')
                .setDescription('All commands and handlers have been successfully reloaded!')
                .setColor(discord_1.EmbedColors.SUCCESS)
                .addFields([
                {
                    name: '📊 Statistics',
                    value: [
                        `**Commands:** ${oldCommandCount} → ${client.commands.size}`,
                        `**Buttons:** ${oldButtonCount} → ${client.buttons.size}`,
                        `**Modals:** ${oldModalCount} → ${client.modals.size}`,
                        `**Reload Time:** ${reloadTime}ms`
                    ].join('\n'),
                    inline: false
                },
                {
                    name: '✅ Actions Completed',
                    value: [
                        '• Commands reloaded',
                        '• Button handlers reloaded',
                        '• Modal handlers reloaded',
                        '• Discord commands deployed'
                    ].join('\n'),
                    inline: false
                }
            ])
                .setTimestamp()
                .setFooter({
                text: `Reloaded by ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            });
            await interaction.editReply({ embeds: [embed] });
            client.logger.info('Commands reloaded by admin', {
                adminId: interaction.user.id,
                adminUsername: interaction.user.username,
                guildId: interaction.guildId,
                commandCount: client.commands.size,
                buttonCount: client.buttons.size,
                modalCount: client.modals.size,
                reloadTime
            });
        }
        catch (error) {
            client.logger.error('Error during reload command', {
                error: error.message,
                adminId: interaction.user.id,
                guildId: interaction.guildId
            });
            const errorEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('❌ Reload Failed')
                .setDescription('An error occurred while reloading commands and handlers.')
                .addFields([
                {
                    name: 'Error Details',
                    value: `\`\`\`${error.message}\`\`\``,
                    inline: false
                }
            ])
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp();
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            }
            else {
                await interaction.reply({ embeds: [errorEmbed], flags: 64 });
            }
        }
    },
};
//# sourceMappingURL=reload.js.map