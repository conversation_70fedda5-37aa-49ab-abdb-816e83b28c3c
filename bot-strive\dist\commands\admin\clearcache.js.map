{"version": 3, "file": "clearcache.js", "sourceRoot": "", "sources": ["../../../src/commands/admin/clearcache.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAiH;AAEjH,iDAAgE;AAChE,+CAAsD;AAEzC,QAAA,OAAO,GAAiB;IACnC,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC5B,OAAO,CAAC,YAAY,CAAC;SACrB,cAAc,CAAC,6DAA6D,CAAC;SAC7E,2BAA2B,CAAC,gCAAmB,CAAC,aAAa,CAAC;IAEjE,KAAK,CAAC,OAAO,CAAC,WAAwC,EAAE,MAAkB;QACxE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,OAAO,EAAE,sBAAsB;gBAC/B,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,8CAA8C;QAC9C,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,gCAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC3E,MAAM,UAAU,GAAG,IAAI,yBAAY,EAAE;iBAClC,QAAQ,CAAC,eAAe,CAAC;iBACzB,cAAc,CAAC,yDAAyD,CAAC;iBACzE,QAAQ,CAAC,qBAAW,CAAC,KAAK,CAAC;iBAC3B,YAAY,EAAE,CAAC;YAElB,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAED,MAAM,yBAAgB,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,wBAAwB;YACxB,MAAM,cAAc,GAAG;gBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;gBAC9B,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI;gBAC5B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI;gBAC1B,iBAAiB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC9F,CAAC;YAEF,2BAA2B;YAC3B,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxB,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAEtB,wCAAwC;YACxC,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACtB,kDAAkD;gBAClD,MAAM,EAAE,gBAAgB,EAAE,GAAG,wDAAa,wBAAwB,GAAC,CAAC;gBACpE,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;gBACzB,MAAM,CAAC,UAAU,GAAG,IAAI,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBAChE,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5B,CAAC;YAED,2CAA2C;YAC3C,MAAM,EAAE,cAAc,EAAE,GAAG,wDAAa,6BAA6B,GAAC,CAAC;YACvE,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAEjE,MAAM,cAAc,CAAC,YAAY,EAAE,CAAC;YACpC,MAAM,cAAc,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,cAAc,CAAC,UAAU,EAAE,CAAC;YAElC,6CAA6C;YAC7C,IAAI,gBAAgB,GAAG,KAAK,CAAC;YAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;YACxC,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC;oBACH,MAAM,QAAQ,CAAC,mBAAmB,EAAE,CAAC;oBACrC,gBAAgB,GAAG,IAAI,CAAC;gBAC1B,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE;wBAChE,KAAK,EAAG,WAAqB,CAAC,OAAO;qBACtC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,8CAA8C;YAC9C,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,IAAK,MAAc,CAAC,EAAE,EAAE,CAAC;gBACvB,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAI,MAAc,CAAC,EAAE,CAAC;oBACpC,gDAAgD;oBAChD,MAAM,QAAQ,CAAC,GAAG,EAAE,CAAC;oBAErB,+BAA+B;oBAC/B,MAAM,EAAE,eAAe,EAAE,GAAG,wDAAa,2BAA2B,GAAC,CAAC;oBACtE,MAAM,aAAa,GAAG,wDAAa,cAAc,GAAC,CAAC;oBACnD,MAAM,MAAM,GAAG,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC7D,MAAM,QAAQ,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC;oBAE5C,MAAM,WAAW,GAAG,IAAI,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBAEjE,4CAA4C;oBAC5C,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;oBAC9D,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;oBAE5F,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;wBAC3C,0EAA0E;wBAC1E,OAAQ,MAAc,CAAC,EAAE,CAAC;wBAE1B,oCAAoC;wBACpC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;4BAClC,KAAK,EAAE,WAAW;4BAClB,QAAQ,EAAE,KAAK;4BACf,UAAU,EAAE,IAAI;4BAChB,YAAY,EAAE,IAAI,CAAC,+CAA+C;yBACnE,CAAC,CAAC;wBACH,aAAa,GAAG,IAAI,CAAC;wBAErB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;oBACnE,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;gBAAC,OAAO,OAAO,EAAE,CAAC;oBACjB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;wBACnD,KAAK,EAAG,OAAiB,CAAC,OAAO;qBAClC,CAAC,CAAC;oBAEH,mEAAmE;oBACnE,IAAI,CAAC;wBACH,MAAM,EAAE,eAAe,EAAE,GAAG,wDAAa,2BAA2B,GAAC,CAAC;wBACtE,MAAM,aAAa,GAAG,wDAAa,cAAc,GAAC,CAAC;wBACnD,MAAM,MAAM,GAAG,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;wBAC7D,MAAM,QAAQ,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC;wBAE5C,MAAM,gBAAgB,GAAG,IAAI,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;wBACtE,MAAM,YAAY,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;wBAEtE,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;4BACzB,OAAQ,MAAc,CAAC,EAAE,CAAC;4BAC1B,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;gCAClC,KAAK,EAAE,gBAAgB;gCACvB,QAAQ,EAAE,KAAK;gCACf,UAAU,EAAE,IAAI;gCAChB,YAAY,EAAE,IAAI;6BACnB,CAAC,CAAC;4BACH,aAAa,GAAG,IAAI,CAAC;4BACrB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;wBACnE,CAAC;oBACH,CAAC;oBAAC,OAAO,aAAa,EAAE,CAAC;wBACvB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE;4BAC9D,KAAK,EAAG,aAAuB,CAAC,OAAO;yBACxC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,wCAAwC;YACxC,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;gBACd,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;YAEtC,MAAM,KAAK,GAAG,IAAI,yBAAY,EAAE;iBAC7B,QAAQ,CAAC,4BAA4B,CAAC;iBACtC,cAAc,CAAC,gEAAgE,CAAC;iBAChF,QAAQ,CAAC,qBAAW,CAAC,OAAO,CAAC;iBAC7B,SAAS,CAAC;gBACT;oBACE,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE;wBACL,aAAa,cAAc,CAAC,QAAQ,cAAc,MAAM,CAAC,QAAQ,CAAC,IAAI,WAAW;wBACjF,YAAY,cAAc,CAAC,OAAO,cAAc,MAAM,CAAC,OAAO,CAAC,IAAI,WAAW;wBAC9E,WAAW,cAAc,CAAC,MAAM,cAAc,MAAM,CAAC,MAAM,CAAC,IAAI,WAAW;wBAC3E,qBAAqB,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,mBAAmB,EAAE;wBAC1E,eAAe,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;wBAC9D,kBAAkB,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,EAAE;wBAC3D,WAAW,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,kBAAkB,EAAE;qBAClE,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE;wBACL,eAAe,SAAS,IAAI;wBAC5B,iBAAiB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;wBACxE,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;qBAC3E,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE;wBACL,+CAA+C;wBAC/C,0CAA0C;wBAC1C,sCAAsC;qBACvC,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;aACF,CAAC;iBACD,YAAY,EAAE;iBACd,SAAS,CAAC;gBACT,IAAI,EAAE,oBAAoB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACrD,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE;aAC7C,CAAC,CAAC;YAEL,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAEjD,6BAA6B;YAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAC3C,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC5B,aAAa,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,cAAc;gBACd,SAAS,EAAE;oBACT,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;oBAC9B,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI;oBAC5B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI;iBAC3B;gBACD,gBAAgB;gBAChB,aAAa;gBACb,SAAS;gBACT,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;aACnC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACtD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,KAAK,EAAG,KAAe,CAAC,KAAK;gBAC7B,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC5B,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,yBAAY,EAAE;iBAClC,QAAQ,CAAC,oBAAoB,CAAC;iBAC9B,cAAc,CAAC,yCAAyC,CAAC;iBACzD,SAAS,CAAC;gBACT;oBACE,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,SAAU,KAAe,CAAC,OAAO,QAAQ;oBAChD,MAAM,EAAE,KAAK;iBACd;aACF,CAAC;iBACD,QAAQ,CAAC,qBAAW,CAAC,KAAK,CAAC;iBAC3B,YAAY,EAAE,CAAC;YAElB,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAC"}