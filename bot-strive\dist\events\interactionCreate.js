"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = interactionCreateHandler;
const embeds_1 = require("../utils/embeds");
const errorHandler_1 = require("../utils/errorHandler");
/**
 * Interaction create event handler
 */
function interactionCreateHandler(client) {
    client.on('interactionCreate', async (interaction) => {
        try {
            client.monitoring.recordInteraction();
            // Handle slash commands
            if (interaction.isChatInputCommand()) {
                client.monitoring.recordCommand();
                await handleSlashCommand(interaction, client);
            }
            // Handle button interactions
            else if (interaction.isButton()) {
                await handleButtonInteraction(interaction, client);
            }
            // Handle modal submissions
            else if (interaction.isModalSubmit()) {
                await handleModalSubmission(interaction, client);
            }
            // Handle user context menu commands
            else if (interaction.isUserContextMenuCommand()) {
                await handleUserContextMenu(interaction, client);
            }
            // Handle message context menu commands
            else if (interaction.isMessageContextMenuCommand()) {
                await handleMessageContextMenu(interaction, client);
            }
        }
        catch (error) {
            client.logger.error('Error handling interaction', {
                interactionType: interaction.type,
                interactionId: interaction.id,
                userId: interaction.user.id,
                guildId: interaction.guildId,
                error: error.message,
            });
            await client.errorHandler.handleError(error, {
                source: 'interaction_handler',
                interactionType: interaction.type,
                interactionId: interaction.id,
                userId: interaction.user.id,
            });
            try {
                const repliableInteraction = interaction;
                if ('replied' in repliableInteraction && !repliableInteraction.replied && !repliableInteraction.deferred) {
                    const errorMessage = errorHandler_1.ErrorHandler.createUserFriendlyError(error);
                    await embeds_1.InteractionUtils.replyError(repliableInteraction, 'Error', errorMessage, true);
                }
            }
            catch (replyError) {
                client.logger.error('Failed to send error response to user', {
                    error: replyError.message,
                });
            }
        }
    });
}
/**
 * Handle slash command interactions
 */
async function handleSlashCommand(interaction, client) {
    const command = client.commands.get(interaction.commandName);
    if (!command) {
        client.logger.warn('Unknown command executed', {
            commandName: interaction.commandName,
            userId: interaction.user.id,
        });
        await embeds_1.InteractionUtils.replyError(interaction, 'Unknown Command', 'This command is not recognized.', true);
        return;
    }
    if (command.permissions && interaction.memberPermissions) {
        const hasPermission = command.permissions.every(permission => interaction.memberPermissions?.has(permission));
        if (!hasPermission) {
            await embeds_1.InteractionUtils.replyError(interaction, 'Insufficient Permissions', 'You do not have the required permissions to use this command.', true);
            return;
        }
    }
    if (command.ownerOnly) {
        // todo
    }
    client.logger.info('Executing slash command', {
        commandName: interaction.commandName,
        userId: interaction.user.id,
        guildId: interaction.guildId,
    });
    await command.execute(interaction, client);
}
/**
 * Handle button interactions
 */
async function handleButtonInteraction(interaction, client) {
    const button = client.buttons.get(interaction.customId);
    if (!button) {
        client.logger.warn('Unknown button interaction', {
            customId: interaction.customId,
            userId: interaction.user.id,
        });
        await embeds_1.InteractionUtils.replyError(interaction, 'Unknown Button', 'This button is not recognized.', true);
        return;
    }
    if (button.permissions && interaction.memberPermissions) {
        const hasPermission = button.permissions.every(permission => interaction.memberPermissions?.has(permission));
        if (!hasPermission) {
            await embeds_1.InteractionUtils.replyError(interaction, 'Insufficient Permissions', 'You do not have the required permissions to use this button.', true);
            return;
        }
    }
    client.logger.info('Executing button interaction', {
        customId: interaction.customId,
        userId: interaction.user.id,
        guildId: interaction.guildId,
    });
    await button.execute(interaction);
}
/**
 * Handle modal submission interactions
 */
async function handleModalSubmission(interaction, client) {
    const modal = client.modals.get(interaction.customId);
    if (!modal) {
        client.logger.warn('Unknown modal submission', {
            customId: interaction.customId,
            userId: interaction.user.id,
        });
        await embeds_1.InteractionUtils.replyError(interaction, 'Unknown Modal', 'This modal is not recognized.', true);
        return;
    }
    if (modal.permissions && interaction.memberPermissions) {
        const hasPermission = modal.permissions.every(permission => interaction.memberPermissions?.has(permission));
        if (!hasPermission) {
            await embeds_1.InteractionUtils.replyError(interaction, 'Insufficient Permissions', 'You do not have the required permissions to submit this modal.', true);
            return;
        }
    }
    client.logger.info('Executing modal submission', {
        customId: interaction.customId,
        userId: interaction.user.id,
        guildId: interaction.guildId,
    });
    await modal.execute(interaction);
}
/**
 * Handle user context menu interactions
 */
async function handleUserContextMenu(interaction, client) {
    const command = client.commands.get(interaction.commandName);
    if (!command) {
        await embeds_1.InteractionUtils.replyError(interaction, 'Unknown Command', 'This context menu command is not recognized.', true);
        return;
    }
    client.logger.info('Executing user context menu command', {
        commandName: interaction.commandName,
        userId: interaction.user.id,
        targetUserId: interaction.targetUser.id,
    });
    await command.execute(interaction);
}
/**
 * Handle message context menu interactions
 */
async function handleMessageContextMenu(interaction, client) {
    const command = client.commands.get(interaction.commandName);
    if (!command) {
        await embeds_1.InteractionUtils.replyError(interaction, 'Unknown Command', 'This context menu command is not recognized.', true);
        return;
    }
    client.logger.info('Executing message context menu command', {
        commandName: interaction.commandName,
        userId: interaction.user.id,
        targetMessageId: interaction.targetMessage.id,
    });
    await command.execute(interaction);
}
//# sourceMappingURL=interactionCreate.js.map