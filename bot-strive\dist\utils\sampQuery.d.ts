import { Logger } from '../types/common';
/**
 * SAMP Server Information
 */
export interface SAMPServerInfo {
    hostname: string;
    gamemode: string;
    language: string;
    players: number;
    maxPlayers: number;
    password: boolean;
    version: string;
}
/**
 * SAMP Player Information
 */
export interface SAMPPlayer {
    id: number;
    name: string;
    score: number;
    ping: number;
}
/**
 * SAMP Query Result
 */
export interface SAMPQueryResult {
    success: boolean;
    info?: SAMPServerInfo;
    players?: SAMPPlayer[];
    error?: string;
}
/**
 * SAMP Query Client
 * Implements the SAMP Query Mechanism as described in:
 * https://sampwiki.blast.hk/wiki/Query_Mechanism
 */
export declare class SAMPQuery {
    private logger;
    private activeQueries;
    constructor(logger: Logger);
    /**
     * Cleanup all active queries
     */
    cleanup(): void;
    /**
     * Query server information
     */
    queryServerInfo(host: string, port: number, timeout?: number): Promise<SAMPQueryResult>;
    /**
     * Query player list from server
     */
    queryPlayerList(host: string, port: number, timeout?: number): Promise<SAMPQueryResult>;
    /**
     * Query detailed server info with players
     */
    queryDetailedInfo(host: string, port: number, timeout?: number): Promise<SAMPQueryResult>;
    /**
     * Check if server is online
     */
    isServerOnline(host: string, port: number, timeout?: number): Promise<boolean>;
}
//# sourceMappingURL=sampQuery.d.ts.map