"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SAMPQuery = void 0;
const dgram = __importStar(require("dgram"));
/**
 * SAMP Query Client
 * Implements the SAMP Query Mechanism as described in:
 * https://sampwiki.blast.hk/wiki/Query_Mechanism
 */
class SAMPQuery {
    logger;
    activeQueries = new Set();
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Cleanup all active queries
     */
    cleanup() {
        this.activeQueries.forEach(socket => {
            try {
                socket.close();
            }
            catch (error) {
                this.logger.warn('Error closing socket during cleanup', {
                    error: error.message
                });
            }
        });
        this.activeQueries.clear();
    }
    /**
     * Query server information
     */
    async queryServerInfo(host, port, timeout = 5000) {
        return new Promise((resolve) => {
            const client = dgram.createSocket('udp4');
            this.activeQueries.add(client);
            let isResolved = false;
            const safeResolve = (result) => {
                if (isResolved)
                    return;
                isResolved = true;
                this.activeQueries.delete(client);
                try {
                    client.close();
                }
                catch (error) {
                    this.logger.warn('Error closing socket', { error: error.message });
                }
                resolve(result);
            };
            const timeoutId = setTimeout(() => {
                this.logger.warn('SAMP query timeout', { host, port, timeout });
                safeResolve({ success: false, error: 'Query timeout' });
            }, timeout);
            try {
                const packet = Buffer.alloc(11);
                packet.write('SAMP', 0); // Header
                const ipParts = host.split('.').map(part => parseInt(part));
                if (ipParts.length !== 4 || ipParts.some(part => isNaN(part) || part < 0 || part > 255)) {
                    clearTimeout(timeoutId);
                    safeResolve({ success: false, error: 'Invalid IP address format' });
                    return;
                }
                packet[4] = ipParts[0];
                packet[5] = ipParts[1];
                packet[6] = ipParts[2];
                packet[7] = ipParts[3];
                packet.writeUInt16LE(port, 8);
                packet[10] = 0x69; // 'i'
                client.on('message', (msg) => {
                    try {
                        clearTimeout(timeoutId);
                        client.close();
                        if (msg.length < 11) {
                            resolve({ success: false, error: 'Invalid response length' });
                            return;
                        }
                        if (msg.toString('ascii', 0, 4) !== 'SAMP') {
                            resolve({ success: false, error: 'Invalid response header' });
                            return;
                        }
                        let offset = 11;
                        const password = msg[offset] === 1;
                        offset++;
                        const players = msg.readUInt16LE(offset);
                        offset += 2;
                        const maxPlayers = msg.readUInt16LE(offset);
                        offset += 2;
                        const hostnameLength = msg.readUInt32LE(offset);
                        offset += 4;
                        const hostname = msg.toString('utf8', offset, offset + hostnameLength);
                        offset += hostnameLength;
                        const gamemodeLength = msg.readUInt32LE(offset);
                        offset += 4;
                        const gamemode = msg.toString('utf8', offset, offset + gamemodeLength);
                        offset += gamemodeLength;
                        const languageLength = msg.readUInt32LE(offset);
                        offset += 4;
                        const language = msg.toString('utf8', offset, offset + languageLength);
                        const info = {
                            hostname,
                            gamemode,
                            language,
                            players,
                            maxPlayers,
                            password,
                            version: '0.3.7/0.3DL'
                        };
                        resolve({ success: true, info });
                    }
                    catch (error) {
                        this.logger.error('Error parsing SAMP response', { error: error.message });
                        resolve({ success: false, error: 'Failed to parse server response' });
                    }
                });
                client.on('error', (error) => {
                    clearTimeout(timeoutId);
                    client.close();
                    this.logger.error('SAMP query socket error', { error: error.message });
                    resolve({ success: false, error: `Socket error: ${error.message}` });
                });
                client.send(packet, port, host, (error) => {
                    if (error) {
                        clearTimeout(timeoutId);
                        client.close();
                        this.logger.error('Failed to send SAMP query', { error: error.message });
                        resolve({ success: false, error: `Failed to send query: ${error.message}` });
                    }
                });
            }
            catch (error) {
                clearTimeout(timeoutId);
                client.close();
                this.logger.error('SAMP query error', { error: error.message });
                resolve({ success: false, error: error.message });
            }
        });
    }
    /**
     * Query player list from server
     */
    async queryPlayerList(host, port, timeout = 5000) {
        return new Promise((resolve) => {
            const client = dgram.createSocket('udp4');
            const timeoutId = setTimeout(() => {
                client.close();
                resolve({ success: false, error: 'Query timeout' });
            }, timeout);
            try {
                const packet = Buffer.alloc(11);
                packet.write('SAMP', 0); // Header
                const ipParts = host.split('.').map(part => parseInt(part));
                if (ipParts.length !== 4 || ipParts.some(part => isNaN(part) || part < 0 || part > 255)) {
                    clearTimeout(timeoutId);
                    client.close();
                    resolve({ success: false, error: 'Invalid IP address format' });
                    return;
                }
                packet[4] = ipParts[0];
                packet[5] = ipParts[1];
                packet[6] = ipParts[2];
                packet[7] = ipParts[3];
                packet.writeUInt16LE(port, 8);
                packet[10] = 0x64; // 'd'
                client.on('message', (msg) => {
                    try {
                        clearTimeout(timeoutId);
                        client.close();
                        if (msg.length < 11) {
                            resolve({ success: false, error: 'Invalid response length' });
                            return;
                        }
                        if (msg.toString('ascii', 0, 4) !== 'SAMP') {
                            resolve({ success: false, error: 'Invalid response header' });
                            return;
                        }
                        let offset = 11;
                        const playerCount = msg.readUInt16LE(offset);
                        offset += 2;
                        const players = [];
                        for (let i = 0; i < playerCount && offset < msg.length; i++) {
                            const id = msg[offset];
                            offset++;
                            const nameLength = msg[offset];
                            offset++;
                            if (!nameLength || offset + nameLength > msg.length)
                                break;
                            const name = msg.toString('utf8', offset, offset + nameLength);
                            offset += nameLength;
                            if (offset + 4 > msg.length)
                                break;
                            const score = msg.readInt32LE(offset);
                            offset += 4;
                            if (offset + 4 > msg.length)
                                break;
                            const ping = msg.readUInt32LE(offset);
                            offset += 4;
                            players.push({ id: id, name, score, ping });
                        }
                        resolve({ success: true, players });
                    }
                    catch (error) {
                        this.logger.error('Error parsing SAMP player list response', { error: error.message });
                        resolve({ success: false, error: 'Failed to parse player list response' });
                    }
                });
                client.on('error', (error) => {
                    clearTimeout(timeoutId);
                    client.close();
                    this.logger.error('SAMP player list query socket error', { error: error.message });
                    resolve({ success: false, error: `Socket error: ${error.message}` });
                });
                client.send(packet, port, host, (error) => {
                    if (error) {
                        clearTimeout(timeoutId);
                        client.close();
                        this.logger.error('Failed to send SAMP player list query', { error: error.message });
                        resolve({ success: false, error: `Failed to send query: ${error.message}` });
                    }
                });
            }
            catch (error) {
                clearTimeout(timeoutId);
                client.close();
                this.logger.error('SAMP player list query error', { error: error.message });
                resolve({ success: false, error: error.message });
            }
        });
    }
    /**
     * Query detailed server info with players
     */
    async queryDetailedInfo(host, port, timeout = 5000) {
        const serverInfo = await this.queryServerInfo(host, port, timeout);
        if (!serverInfo.success) {
            return serverInfo;
        }
        const playerList = await this.queryPlayerList(host, port, timeout);
        return {
            success: true,
            info: serverInfo.info,
            players: playerList.success ? (playerList.players || []) : []
        };
    }
    /**
     * Check if server is online
     */
    async isServerOnline(host, port, timeout = 3000) {
        const result = await this.queryServerInfo(host, port, timeout);
        return result.success;
    }
}
exports.SAMPQuery = SAMPQuery;
//# sourceMappingURL=sampQuery.js.map