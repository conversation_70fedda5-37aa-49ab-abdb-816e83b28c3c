"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const AccountRepository_1 = require("../../database/repositories/AccountRepository");
const embeds_1 = require("../../utils/embeds");
/**
 * Rebind button handler - generates a new verification code
 */
const rebindButton = {
    customId: 'button-rebind',
    async execute(interaction) {
        try {
            const client = interaction.client;
            const accountRepo = new AccountRepository_1.AccountRepository(client.db, client.logger);
            const accountResult = await accountRepo.findByDiscordId(interaction.user.id);
            if (!accountResult.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Database Error', 'There was an error checking your account status. Please try again later.', true);
                return;
            }
            if (!accountResult.data) {
                await embeds_1.InteractionUtils.replyWarning(interaction, 'No Account Found', '⚠️ You do not have a registered account. Please use the Register button to create one first.', true);
                return;
            }
            const account = accountResult.data;
            const newCode = Math.floor(Math.random() * 999999) + 1;
            const updateResult = await accountRepo.update(account.id, {
                discord_code: newCode,
            });
            if (!updateResult.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Update Failed', 'Failed to generate a new verification code. Please try again later.', true);
                return;
            }
            try {
                const dmMessage = `🔄 **Account Rebind Successful**\n\n` +
                    `Your new verification code is: \`${newCode}\`\n\n` +
                    `**Username:** \`${account.username}\`\n` +
                    `**Server:** \`${client.config.SAMP_SERVER_IP}:${client.config.SAMP_SERVER_PORT}\`\n\n` +
                    `Use this new code to verify your account in-game.`;
                await interaction.user.send(dmMessage);
                await embeds_1.InteractionUtils.replySuccess(interaction, 'Rebind Successful', '✅ A new verification code has been generated and sent to your DMs.', true);
                client.logger.info('Account rebind successful', {
                    userId: interaction.user.id,
                    username: account.username,
                    newCode,
                });
            }
            catch (dmError) {
                await embeds_1.InteractionUtils.replySuccess(interaction, 'Rebind Successful', `✅ New verification code generated: \`${newCode}\`\n\n` +
                    `⚠️ Could not send DM. Make sure your DMs are open for future notifications.`, true);
                client.logger.warn('Failed to send rebind DM', {
                    userId: interaction.user.id,
                    error: dmError.message,
                });
            }
        }
        catch (error) {
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await embeds_1.InteractionUtils.replyError(interaction, 'Error', 'An unexpected error occurred during the rebind process.', true);
                }
                catch (replyError) {
                    const client = interaction.client;
                    client.logger.error('Failed to send error response to user', {
                        error: replyError.message
                    });
                }
            }
            const client = interaction.client;
            client.logger.error('Error in rebind button', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
        }
    },
};
exports.default = rebindButton;
//# sourceMappingURL=rebindButton.js.map