import { DatabaseConnection } from '../../types/database';
import { Logger, AsyncResult } from '../../types/common';
export interface Character {
    id: number;
    username: string;
    character: string;
    email: string;
    Created: number;
    gender: number;
    Birthdate: string;
    origin: string;
    skin: number;
    Money: number;
    BankMoney: number;
    Admin: number;
    LastLogin: number;
    PlayingHours: number;
    Minutes: number;
    Health: number;
    Faction: number;
    FactionRank: number;
    Job: number;
    AdminHide: number;
    VIP: number;
    VIPTime: number;
    age: number;
    weight: number;
    height: number;
}
export interface OnlineAdmin {
    id: number;
    username: string;
    character: string;
    adminLevel: number;
    skin: number;
    lastLogin: number;
    isHidden: boolean;
}
/**
 * Repository for character data operations
 */
export declare class CharacterRepository {
    private db;
    private logger;
    constructor(db: DatabaseConnection, logger: Logger);
    /**
     * Get online admins (based on recent LastLogin)
     */
    getOnlineAdmins(): AsyncResult<OnlineAdmin[]>;
    /**
     * Get character by username
     */
    getCharacterByUsername(username: string): AsyncResult<Character | null>;
    /**
     * Get character by Discord ID (through accounts table)
     */
    getCharacterByDiscordId(discordId: string): AsyncResult<Character | null>;
    /**
     * Get admin level name
     */
    getAdminLevelName(level: number): string;
    /**
     * Get job name by ID
     */
    getJobName(jobId: number): string;
    /**
     * Format money with commas
     */
    formatMoney(amount: number): string;
    /**
     * Convert timestamp to readable date
     */
    formatTimestamp(timestamp: number): string;
}
//# sourceMappingURL=CharacterRepository.d.ts.map