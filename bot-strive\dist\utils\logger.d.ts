import winston from 'winston';
import { Logger } from '../types/common';
/**
 * Custom logger implementation using Winston
 */
export declare class BotLogger implements Logger {
    private winston;
    constructor(level?: string, isDevelopment?: boolean);
    private ensureLogsDirectory;
    error(message: string, meta?: Record<string, unknown>): void;
    warn(message: string, meta?: Record<string, unknown>): void;
    info(message: string, meta?: Record<string, unknown>): void;
    debug(message: string, meta?: Record<string, unknown>): void;
    /**
     * Get the underlying Winston logger instance
     */
    getWinstonLogger(): winston.Logger;
    /**
     * Close the logger and all transports
     */
    close(): void;
}
/**
 * Get or create the logger instance
 */
export declare const getLogger: (level?: string, isDevelopment?: boolean) => BotLogger;
//# sourceMappingURL=logger.d.ts.map