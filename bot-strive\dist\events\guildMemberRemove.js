"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
const discord_js_1 = require("discord.js");
const discord_1 = require("../types/discord");
/**
 * Guild member remove event handler
 * Sends leave message when members leave the server
 */
function default_1(client) {
    client.on('guildMemberRemove', async (member) => {
        try {
            client.logger.info('Member left server - processing leave message', {
                userId: member.id,
                username: member.user?.username || 'Unknown',
                guildId: member.guild.id,
                isPartial: member.partial,
                isBot: member.user?.bot || false
            });
            if (member.partial) {
                try {
                    await member.fetch();
                }
                catch (error) {
                    client.logger.warn('Could not fetch partial member data for leave message', {
                        userId: member.id,
                        guildId: member.guild.id
                    });
                    return;
                }
            }
            if (member.user?.bot) {
                client.logger.debug('Skipping leave message for bot', {
                    userId: member.user.id,
                    username: member.user.username
                });
                return;
            }
            await sendLeaveMessage(member, client);
        }
        catch (error) {
            client.logger.error('Error in guildMemberRemove event', {
                error: error.message,
                stack: error.stack,
                userId: member.id,
                guildId: member.guild.id
            });
        }
    });
}
/**
 * Send leave message when member leaves
 */
async function sendLeaveMessage(member, client) {
    try {
        client.logger.info('Attempting to send leave message', {
            userId: member.user.id,
            username: member.user.username,
            guildId: member.guild.id,
            leaveEnabled: client.config.LEAVE_ENABLED,
            welcomeChannelId: client.config.WELCOME_CHANNEL_ID
        });
        if (!client.config.LEAVE_ENABLED) {
            client.logger.warn('Leave message skipped - LEAVE_ENABLED is false', {
                userId: member.user.id,
                guildId: member.guild.id,
                leaveEnabled: client.config.LEAVE_ENABLED
            });
            return;
        }
        if (!client.config.WELCOME_CHANNEL_ID) {
            client.logger.warn('Leave message skipped - WELCOME_CHANNEL_ID not configured', {
                userId: member.user.id,
                guildId: member.guild.id
            });
            return;
        }
        const leaveChannel = await member.guild.channels.fetch(client.config.WELCOME_CHANNEL_ID);
        if (!leaveChannel || !leaveChannel.isTextBased()) {
            client.logger.warn('Leave channel not found or not text-based', {
                channelId: client.config.WELCOME_CHANNEL_ID,
                userId: member.user.id,
                guildId: member.guild.id
            });
            return;
        }
        const joinedAt = member.joinedAt;
        const leftAt = new Date();
        let membershipDuration = 'Unknown duration';
        if (joinedAt) {
            const durationMs = leftAt.getTime() - joinedAt.getTime();
            const days = Math.floor(durationMs / (1000 * 60 * 60 * 24));
            const hours = Math.floor((durationMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
            if (days > 0) {
                membershipDuration = `${days} day${days !== 1 ? 's' : ''}, ${hours} hour${hours !== 1 ? 's' : ''}`;
            }
            else if (hours > 0) {
                membershipDuration = `${hours} hour${hours !== 1 ? 's' : ''}, ${minutes} minute${minutes !== 1 ? 's' : ''}`;
            }
            else {
                membershipDuration = `${minutes} minute${minutes !== 1 ? 's' : ''}`;
            }
        }
        const leaveEmbed = new discord_js_1.EmbedBuilder()
            .setTitle(`👋 Member Left`)
            .setDescription(`**${member.user.username}** has left ${member.guild.name}`)
            .setColor(discord_1.EmbedColors.ERROR)
            .addFields([
            {
                name: '👤 Member Info',
                value: [
                    `**User:** ${member.user.username}`,
                    `**Tag:** ${member.user.tag}`,
                    `**ID:** \`${member.user.id}\``,
                    `**Left:** <t:${Math.floor(leftAt.getTime() / 1000)}:F>`
                ].join('\n'),
                inline: true
            },
            {
                name: '📊 Membership',
                value: [
                    `**Duration:** ${membershipDuration}`,
                    `**Joined:** ${joinedAt ? `<t:${Math.floor(joinedAt.getTime() / 1000)}:D>` : 'Unknown'}`,
                    `**Members Now:** ${member.guild.memberCount}`,
                    `**Account Created:** <t:${Math.floor(member.user.createdTimestamp / 1000)}:D>`
                ].join('\n'),
                inline: true
            }
        ])
            .setThumbnail(member.user.displayAvatarURL())
            .setTimestamp();
        if (member.roles.cache.size > 1) {
            const roles = member.roles.cache
                .filter(role => role.name !== '@everyone')
                .map(role => role.name)
                .slice(0, 10);
            if (roles.length > 0) {
                leaveEmbed.addFields([{
                        name: '🎭 Roles',
                        value: roles.join(', ') + (member.roles.cache.size > 11 ? '...' : ''),
                        inline: false
                    }]);
            }
        }
        const guildIcon = member.guild.iconURL();
        if (guildIcon) {
            leaveEmbed.setFooter({
                text: `Goodbye from ${member.guild.name}`,
                iconURL: guildIcon
            });
        }
        else {
            leaveEmbed.setFooter({
                text: `Goodbye from ${member.guild.name}`
            });
        }
        await leaveChannel.send({
            embeds: [leaveEmbed]
        });
        client.logger.info('Leave message sent successfully', {
            userId: member.user.id,
            username: member.user.username,
            guildId: member.guild.id,
            channelId: client.config.WELCOME_CHANNEL_ID,
            membershipDuration
        });
    }
    catch (error) {
        client.logger.error('Error sending leave message', {
            error: error.message,
            stack: error.stack,
            userId: member.user.id,
            guildId: member.guild.id,
            channelId: client.config.WELCOME_CHANNEL_ID
        });
    }
}
//# sourceMappingURL=guildMemberRemove.js.map