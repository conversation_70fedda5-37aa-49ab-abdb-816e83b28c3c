"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const embeds_1 = require("../../utils/embeds");
/**
 * Setup panel command for account registration
 */
const setupPanelCommand = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('setuppanel')
        .setDescription('Display the account registration panel')
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    async execute(interaction) {
        if (!interaction.channel) {
            await embeds_1.InteractionUtils.replyError(interaction, 'Error', 'This command can only be used in a channel.', true);
            return;
        }
        const client = interaction.client;
        const embed = new discord_js_1.EmbedBuilder()
            .setAuthor({
            name: 'Strive Roleplay',
            iconURL: client.config?.ICON_URL || undefined
        })
            .setTitle('Account Control Panel Strive Roleplay')
            .setDescription('Use the navigation below to manage your account.')
            .setThumbnail(client.config?.ICON_URL || null)
            .setColor(discord_1.EmbedColors.ERROR)
            .setFooter({ text: 'Strive Roleplay' })
            .setTimestamp();
        const buttons = new discord_js_1.ActionRowBuilder()
            .addComponents(new discord_js_1.ButtonBuilder()
            .setCustomId('button-register')
            .setLabel('Register')
            .setStyle(discord_js_1.ButtonStyle.Success)
            .setEmoji('📃'), new discord_js_1.ButtonBuilder()
            .setCustomId('button-checkaccount')
            .setLabel('Check Account')
            .setStyle(discord_js_1.ButtonStyle.Primary)
            .setEmoji('📍'), new discord_js_1.ButtonBuilder()
            .setCustomId('button-rebind')
            .setLabel('Rebind')
            .setStyle(discord_js_1.ButtonStyle.Primary)
            .setEmoji('♻️'), new discord_js_1.ButtonBuilder()
            .setCustomId('reset_password')
            .setLabel('Reset Password')
            .setStyle(discord_js_1.ButtonStyle.Secondary)
            .setEmoji('🔐'));
        try {
            if (!interaction.channel || !('send' in interaction.channel)) {
                throw new Error('Cannot send message to this channel type');
            }
            await interaction.channel.send({
                embeds: [embed],
                components: [buttons],
            });
            await embeds_1.InteractionUtils.replySuccess(interaction, 'Panel Created', 'Account control panel has been successfully created.', true);
        }
        catch (error) {
            await embeds_1.InteractionUtils.replyError(interaction, 'Error', 'Failed to create the account panel. Please try again.', true);
            throw error;
        }
    },
    permissions: ['Administrator'],
    ownerOnly: false,
};
exports.default = setupPanelCommand;
//# sourceMappingURL=setupPanel.js.map