{"version": 3, "file": "changelogParser.js", "sourceRoot": "", "sources": ["../../src/utils/changelogParser.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,2CAA0C;AAC1C,8CAA+C;AAmB/C;;GAEG;AACH,MAAa,eAAe;IAClB,aAAa,CAAS;IAE9B,YAAY,aAAsB;QAChC,IAAI,CAAC,aAAa,GAAG,aAAa,IAAI,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC;YACH,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBACvC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,EAAE;oBACZ,KAAK,EAAE,6BAA6B;iBACrC,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAE7C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,EAAE;gBACZ,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,OAAe;QACnC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,IAAI,cAAc,GAA4B,IAAI,CAAC;QACnD,IAAI,cAAc,GAA4B,IAAI,CAAC;QAEnD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAEhC,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACzE,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,cAAc,EAAE,CAAC;oBACnB,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAChC,CAAC;gBACD,cAAc,GAAG;oBACf,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE;oBAC9B,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE;oBAC3B,QAAQ,EAAE,EAAE;iBACb,CAAC;gBACF,cAAc,GAAG,IAAI,CAAC;gBACtB,SAAS;YACX,CAAC;YAED,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YACvD,IAAI,YAAY,IAAI,cAAc,EAAE,CAAC;gBACnC,cAAc,GAAG;oBACf,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE;oBAC5B,KAAK,EAAE,EAAE;iBACV,CAAC;gBACF,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC7C,SAAS;YACX,CAAC;YAED,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAClD,IAAI,SAAS,IAAI,cAAc,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChD,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,SAAS;YACX,CAAC;QACH,CAAC;QAED,IAAI,cAAc,EAAE,CAAC;YACnB,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,aAAqB;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,aAAa,CAAC,IAAI,IAAI,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAyB;QAC1C,MAAM,KAAK,GAAG,IAAI,yBAAY,EAAE;aAC7B,QAAQ,CAAC,0BAA0B,OAAO,CAAC,OAAO,EAAE,CAAC;aACrD,cAAc,CAAC,qBAAqB,OAAO,CAAC,IAAI,EAAE,CAAC;aACnD,QAAQ,CAAC,qBAAW,CAAC,IAAI,CAAC;aAC1B,YAAY,EAAE;aACd,SAAS,CAAC,EAAE,IAAI,EAAE,6BAA6B,EAAE,CAAC,CAAC;QAEtD,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE3D,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI;oBAC1C,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK;oBACrC,CAAC,CAAC,QAAQ,CAAC;gBAEb,KAAK,CAAC,SAAS,CAAC,CAAC;wBACf,IAAI,EAAE,OAAO,CAAC,KAAK;wBACnB,KAAK,EAAE,aAAa,IAAI,UAAU;wBAClC,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,OAAyB;QAC3C,MAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,MAAM,iBAAiB,GAAG,CAAC,CAAC;QAE5B,MAAM,SAAS,GAAG,IAAI,yBAAY,EAAE;aACjC,QAAQ,CAAC,0BAA0B,OAAO,CAAC,OAAO,EAAE,CAAC;aACrD,cAAc,CAAC,qBAAqB,OAAO,CAAC,IAAI,EAAE,CAAC;aACnD,QAAQ,CAAC,qBAAW,CAAC,IAAI,CAAC;aAC1B,YAAY,EAAE;aACd,SAAS,CAAC,EAAE,IAAI,EAAE,6BAA6B,EAAE,CAAC,CAAC;QAEtD,IAAI,YAAY,GAAG,SAAS,CAAC;QAC7B,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,IAAI,UAAU,IAAI,iBAAiB,EAAE,CAAC;oBACpC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC1B,YAAY,GAAG,IAAI,yBAAY,EAAE;yBAC9B,QAAQ,CAAC,0BAA0B,OAAO,CAAC,OAAO,cAAc,CAAC;yBACjE,QAAQ,CAAC,qBAAW,CAAC,IAAI,CAAC;yBAC1B,YAAY,EAAE,CAAC;oBAClB,UAAU,GAAG,CAAC,CAAC;gBACjB,CAAC;gBAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE3D,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI;oBAC1C,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK;oBACrC,CAAC,CAAC,QAAQ,CAAC;gBAEb,YAAY,CAAC,SAAS,CAAC,CAAC;wBACtB,IAAI,EAAE,OAAO,CAAC,KAAK;wBACnB,KAAK,EAAE,aAAa,IAAI,UAAU;wBAClC,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC,CAAC;gBAEJ,UAAU,EAAE,CAAC;YACf,CAAC;QACH,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;CACF;AAvMD,0CAuMC"}