"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const os = __importStar(require("os"));
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('info')
        .setDescription('Display bot information and statistics'),
    async execute(interaction, client) {
        if (!client) {
            await interaction.reply({
                content: '❌ Client not available',
                flags: 64
            });
            return;
        }
        await interaction.deferReply();
        try {
            const metrics = client.monitoring.getMetrics();
            const healthReport = client.monitoring.generateHealthReport();
            const uptimeMs = metrics.uptime;
            const uptimeHours = Math.floor(uptimeMs / (1000 * 60 * 60));
            const uptimeMinutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
            const uptimeSeconds = Math.floor((uptimeMs % (1000 * 60)) / 1000);
            const memoryUsageMB = Math.round(metrics.memoryUsage.rss / 1024 / 1024);
            const heapUsedMB = Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024);
            const heapTotalMB = Math.round(metrics.memoryUsage.heapTotal / 1024 / 1024);
            const totalMemoryGB = Math.round(os.totalmem() / 1024 / 1024 / 1024);
            const freeMemoryGB = Math.round(os.freemem() / 1024 / 1024 / 1024);
            const guildCount = client.guilds.cache.size;
            const userCount = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);
            const healthEmoji = healthReport.status === 'healthy' ? '🟢' :
                healthReport.status === 'warning' ? '🟡' : '🔴';
            const embed = new discord_js_1.EmbedBuilder()
                .setTitle('🤖 Bot Information')
                .setDescription(`**${client.user?.username}** - Discord Bot Statistics`)
                .setColor(healthReport.status === 'healthy' ? discord_1.EmbedColors.SUCCESS :
                healthReport.status === 'warning' ? discord_1.EmbedColors.WARNING : discord_1.EmbedColors.ERROR)
                .setThumbnail(client.user?.displayAvatarURL() || null)
                .addFields([
                {
                    name: '📊 Bot Statistics',
                    value: [
                        `**Servers:** ${guildCount}`,
                        `**Users:** ${userCount.toLocaleString()}`,
                        `**Commands:** ${client.commands.size}`,
                        `**Buttons:** ${client.buttons.size}`,
                        `**Modals:** ${client.modals.size}`
                    ].join('\n'),
                    inline: true
                },
                {
                    name: '⚡ Performance',
                    value: [
                        `**Uptime:** ${uptimeHours}h ${uptimeMinutes}m ${uptimeSeconds}s`,
                        `**Memory:** ${memoryUsageMB}MB / ${totalMemoryGB}GB`,
                        `**Heap:** ${heapUsedMB}MB / ${heapTotalMB}MB`,
                        `**Ping:** ${Math.round(client.ws.ping)}ms`,
                        `**Health:** ${healthEmoji} ${healthReport.status.toUpperCase()}`
                    ].join('\n'),
                    inline: true
                },
                {
                    name: '📈 Activity Stats',
                    value: [
                        `**Commands Executed:** ${metrics.commandsExecuted.toLocaleString()}`,
                        `**Interactions Handled:** ${metrics.interactionsHandled.toLocaleString()}`,
                        `**Database Queries:** ${metrics.databaseQueries.toLocaleString()}`,
                        `**Errors:** ${metrics.errors}`,
                        `**Server Query Rate:** ${client.monitoring.getServerQuerySuccessRate()}`
                    ].join('\n'),
                    inline: false
                },
                {
                    name: '🔧 Technical Info',
                    value: [
                        `**Node.js:** ${process.version}`,
                        `**Discord.js:** v${discord_js_1.version}`,
                        `**Platform:** ${os.platform()} ${os.arch()}`,
                        `**CPU Cores:** ${os.cpus().length}`,
                        `**Free Memory:** ${freeMemoryGB}GB / ${totalMemoryGB}GB`
                    ].join('\n'),
                    inline: true
                },
                {
                    name: '🌐 Environment',
                    value: [
                        `**Environment:** ${client.config.NODE_ENV}`,
                        `**Log Level:** ${client.config.LOG_LEVEL}`,
                        `**Database:** Connected ✅`,
                        `**SAMP Server:** ${client.config.SAMP_SERVER_IP}:${client.config.SAMP_SERVER_PORT}`,
                        `**Monitoring:** Active ✅`
                    ].join('\n'),
                    inline: true
                }
            ])
                .setTimestamp();
            if (healthReport.issues.length > 0) {
                embed.addFields([
                    {
                        name: '⚠️ Health Issues',
                        value: healthReport.issues.map(issue => `• ${issue}`).join('\n'),
                        inline: false
                    }
                ]);
            }
            const footerData = {
                text: `Bot ID: ${client.user?.id} • Requested by ${interaction.user.username}`
            };
            if (interaction.user) {
                footerData.iconURL = interaction.user.displayAvatarURL();
            }
            embed.setFooter(footerData);
            await interaction.editReply({ embeds: [embed] });
            client.logger.info('Bot info command executed', {
                userId: interaction.user.id,
                username: interaction.user.username,
                guildId: interaction.guildId,
                healthStatus: healthReport.status,
                uptime: uptimeMs,
                memoryUsage: memoryUsageMB
            });
        }
        catch (error) {
            client.logger.error('Error in bot info command', {
                error: error.message,
                userId: interaction.user.id,
            });
            const errorEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('❌ Error')
                .setDescription('Failed to retrieve bot information. Please try again later.')
                .setColor(discord_1.EmbedColors.ERROR)
                .setTimestamp();
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            }
            else {
                await interaction.reply({ embeds: [errorEmbed], flags: 64 });
            }
        }
    },
};
//# sourceMappingURL=botinfo.js.map