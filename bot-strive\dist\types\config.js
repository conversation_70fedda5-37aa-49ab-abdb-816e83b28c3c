"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigSchema = void 0;
const zod_1 = require("zod");
/**
 * Environment configuration schema using Zod for validation
 */
exports.ConfigSchema = zod_1.z.object({
    DISCORD_TOKEN: zod_1.z.string().min(1, 'Discord token is required'),
    DISCORD_CLIENT_ID: zod_1.z.string().min(1, 'Discord client ID is required'),
    DISCORD_GUILD_ID: zod_1.z.string().min(1, 'Discord guild ID is required'),
    BOT_PREFIX: zod_1.z.string().default('!'),
    NODE_ENV: zod_1.z.enum(['development', 'production', 'test']).default('development'),
    LOG_LEVEL: zod_1.z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    DB_HOST: zod_1.z.string().default('localhost'),
    DB_PORT: zod_1.z.coerce.number().int().min(1).max(65535).default(3306),
    DB_USER: zod_1.z.string().min(1, 'Database user is required'),
    DB_PASSWORD: zod_1.z.string().default(''),
    DB_NAME: zod_1.z.string().min(1, 'Database name is required'),
    SAMP_SERVER_IP: zod_1.z.string().default('localhost'),
    SAMP_SERVER_PORT: zod_1.z.coerce.number().int().min(1).max(65535).default(7777),
    ERROR_WEBHOOK_ID: zod_1.z.string().optional(),
    ERROR_WEBHOOK_TOKEN: zod_1.z.string().optional(),
    ROLE_UCP: zod_1.z.string().optional(),
    ROLE_UNVERIFIED: zod_1.z.string().optional(),
    // Welcome System Configuration
    WELCOME_ENABLED: zod_1.z.coerce.boolean().default(false),
    WELCOME_CHANNEL_ID: zod_1.z.string().optional(),
    WELCOME_MESSAGE: zod_1.z.string().optional(),
    WELCOME_BANNER_URL: zod_1.z.string().url().optional(),
    LEAVE_ENABLED: zod_1.z.coerce.boolean().default(false),
    // Changelog Configuration
    CHANGELOG_CHANNEL_ID: zod_1.z.string().optional(),
    ICON_URL: zod_1.z.string().url().optional(),
});
//# sourceMappingURL=config.js.map