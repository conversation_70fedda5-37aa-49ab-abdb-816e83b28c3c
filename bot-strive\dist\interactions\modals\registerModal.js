"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const discord_1 = require("../../types/discord");
const AccountRepository_1 = require("../../database/repositories/AccountRepository");
const validation_1 = require("../../utils/validation");
const embeds_1 = require("../../utils/embeds");
const roleManager_1 = require("../../utils/roleManager");
/**
 * Simple registration modal handler - username only
 */
const registerModal = {
    customId: 'modal-register',
    async execute(interaction) {
        try {
            const client = interaction.client;
            const accountRepo = new AccountRepository_1.AccountRepository(client.db, client.logger);
            const username = interaction.fields.getTextInputValue('reg-name');
            const validation = validation_1.ValidationUtils.validateUsername(username);
            if (!validation.isValid) {
                await embeds_1.InteractionUtils.replyWarning(interaction, 'Invalid Username', `⚠️ **Validation Error:**\n${validation.errors.join('\n')}`, true);
                return;
            }
            const existingUsername = await accountRepo.findByUsername(username);
            if (!existingUsername.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Database Error', 'There was an error checking username availability. Please try again later.', true);
                return;
            }
            if (existingUsername.data) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Username Taken', `❌ **Username "${username}" is already taken!**\n\nPlease choose a different username and try again.`, true);
                return;
            }
            const verificationCode = Math.floor(Math.random() * 999999) + 1;
            const createResult = await accountRepo.create({
                username,
                discord_id: interaction.user.id,
                discord_code: verificationCode,
                password: '',
            });
            if (!createResult.success) {
                let errorMessage = 'Failed to create account. Please try again later.';
                if (createResult.error?.message.includes('already exists') ||
                    createResult.error?.message.includes('Duplicate')) {
                    errorMessage = '❌ **Error!**\nThis username or Discord account is already registered.';
                }
                await embeds_1.InteractionUtils.replyError(interaction, 'Registration Failed', errorMessage, true);
                return;
            }
            const account = createResult.data;
            let roleAssigned = false;
            let roleAssignmentError = null;
            let roleName;
            if (client.config.ROLE_UCP && interaction.guild) {
                const roleManager = new roleManager_1.RoleManager(client.logger);
                const roleResult = await roleManager.assignRole(interaction.guild, interaction.user.id, client.config.ROLE_UCP, 'registration');
                if (roleResult.success && roleResult.roleAssigned) {
                    roleAssigned = true;
                    roleName = roleResult.roleName;
                }
                else {
                    roleAssignmentError = roleResult.error || 'Unknown role assignment error';
                }
            }
            else {
                if (!client.config.ROLE_UCP) {
                    client.logger.info('Role assignment skipped - ROLE_UCP not configured', {
                        userId: interaction.user.id,
                        accountId: account.id
                    });
                }
                else if (!interaction.guild) {
                    client.logger.warn('Role assignment skipped - interaction not in guild', {
                        userId: interaction.user.id,
                        accountId: account.id
                    });
                }
            }
            try {
                const dmEmbed = new discord_js_1.EmbedBuilder()
                    .setTitle('🎉 Registration Successful!')
                    .setDescription('Welcome to **Strive Roleplay**! Your account has been created successfully.')
                    .setColor(discord_1.EmbedColors.SUCCESS)
                    .addFields([
                    {
                        name: '👤 Account Information',
                        value: [
                            `**Username:** \`${account.username}\``,
                            `**Discord:** ${interaction.user.username}`,
                            `**Account ID:** \`${account.id}\``
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '🔐 Verification Code',
                        value: `\`${account.discord_code}\``,
                        inline: false
                    },
                    {
                        name: '🎮 Server Information',
                        value: [
                            `**IP:** \`${client.config.SAMP_SERVER_IP}:${client.config.SAMP_SERVER_PORT}\``,
                            `**Copy:** \`/connect ${client.config.SAMP_SERVER_IP}:${client.config.SAMP_SERVER_PORT}\``
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '📋 Next Steps',
                        value: [
                            '1. Join the server using the IP above',
                            `2. Login with UCP username: \`${account.username}\``,
                            `3. Enter verification code: \`${account.discord_code}\``,
                            '4. Set your password during registration in-game',
                            '5. Create your character (format: Firstname_Lastname)',
                            '6. Start your roleplay journey!'
                        ].join('\n'),
                        inline: false
                    }
                ])
                    .setTimestamp()
                    .setFooter({ text: 'Strive Roleplay' });
                await interaction.user.send({ embeds: [dmEmbed] });
                let successMessage = 'Your account has been created successfully! Check your DMs for verification details.';
                if (roleAssigned && roleName) {
                    successMessage += `\n\n✅ **${roleName} role assigned automatically!**`;
                }
                else if (roleAssignmentError) {
                    successMessage += '\n\n⚠️ **Note:** Role assignment failed, but your account is still registered. Please contact an admin if needed.';
                }
                await embeds_1.InteractionUtils.replySuccess(interaction, 'Registration Complete!', successMessage, true);
                client.logger.info('User registration successful', {
                    userId: interaction.user.id,
                    username: account.username,
                    accountId: account.id,
                    verificationCode: account.discord_code,
                    guildId: interaction.guildId,
                    roleAssigned,
                    roleAssignmentError,
                    roleId: client.config.ROLE_UCP,
                    roleName
                });
            }
            catch (dmError) {
                await embeds_1.InteractionUtils.replyWarning(interaction, 'Registration Complete!', `⚠️ **Registration successful, but could not send DM!**\n\n` +
                    `**Username:** \`${account.username}\`\n` +
                    `**Verification Code:** \`${account.discord_code}\`\n` +
                    `**Server:** \`${client.config.SAMP_SERVER_IP}:${client.config.SAMP_SERVER_PORT}\`\n\n` +
                    `Please save your verification code and enable DMs for future notifications.`, true);
                client.logger.warn('Registration completed but DM failed', {
                    userId: interaction.user.id,
                    username: account.username,
                    error: dmError.message,
                });
            }
        }
        catch (error) {
            await embeds_1.InteractionUtils.replyError(interaction, 'Registration Error', 'An unexpected error occurred during registration. Please try again later.', true);
            const client = interaction.client;
            client.logger.error('Error in registration modal', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id
            });
        }
    },
};
exports.default = registerModal;
//# sourceMappingURL=registerModal.js.map