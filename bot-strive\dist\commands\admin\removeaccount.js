"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.command = void 0;
const discord_js_1 = require("discord.js");
const AccountRepository_1 = require("../../database/repositories/AccountRepository");
const discord_1 = require("../../types/discord");
const embeds_1 = require("../../utils/embeds");
exports.command = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('removeaccount')
        .setDescription('Remove a user account completely from the database')
        .addBooleanOption(option => option.setName('confirm')
        .setDescription('Confirm that you want to permanently delete this account')
        .setRequired(true))
        .addStringOption(option => option.setName('username')
        .setDescription('Username of the account to remove')
        .setRequired(false))
        .addUserOption(option => option.setName('user')
        .setDescription('Discord user whose account to remove')
        .setRequired(false))
        .addStringOption(option => option.setName('reason')
        .setDescription('Reason for account removal')
        .setRequired(false))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });
        try {
            const botClient = client || interaction.client;
            const accountRepo = new AccountRepository_1.AccountRepository(botClient.db, botClient.logger);
            const username = interaction.options.getString('username');
            const user = interaction.options.getUser('user');
            const reason = interaction.options.getString('reason') || 'No reason provided';
            const confirm = interaction.options.getBoolean('confirm', true);
            if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Access Denied', '❌ You do not have permission to use this command.', true);
                return;
            }
            if (!confirm) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Confirmation Required', '❌ You must confirm the account deletion by setting `confirm` to `true`.\n\n⚠️ **Warning**: This action is permanent and cannot be undone!', true);
                return;
            }
            if (!username && !user) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Invalid Input', '❌ You must provide either a username or select a Discord user.', true);
                return;
            }
            let existingAccount;
            let searchTerm = '';
            if (username) {
                existingAccount = await accountRepo.findByUsername(username);
                searchTerm = `username: \`${username}\``;
            }
            else if (user) {
                existingAccount = await accountRepo.findByDiscordId(user.id);
                searchTerm = `Discord user: ${user.username} (\`${user.id}\`)`;
            }
            else {
                await embeds_1.InteractionUtils.replyError(interaction, 'Invalid Input', '❌ No search criteria provided.', true);
                return;
            }
            if (!existingAccount.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Database Error', 'There was an error checking the account. Please try again later.', true);
                return;
            }
            if (!existingAccount.data) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Account Not Found', `❌ No account found with ${searchTerm}`, true);
                return;
            }
            const account = existingAccount.data;
            const deleteResult = await accountRepo.delete(account.id);
            if (!deleteResult.success) {
                await embeds_1.InteractionUtils.replyError(interaction, 'Deletion Failed', 'Failed to delete the account. Please try again later.', true);
                return;
            }
            const successEmbed = new discord_js_1.EmbedBuilder()
                .setTitle('✅ Account Removed Successfully')
                .setDescription(`Account **${username}** has been completely removed from the database.`)
                .setColor(discord_1.EmbedColors.SUCCESS)
                .addFields([
                {
                    name: '👤 Removed Account',
                    value: [
                        `**Username:** \`${account.username}\``,
                        `**Account ID:** \`${account.id}\``,
                        `**Discord ID:** \`${account.discord_id}\``,
                        `**Discord Code:** \`${account.discord_code}\``
                    ].join('\n'),
                    inline: false
                },
                {
                    name: '👮 Admin Action',
                    value: [
                        `**Admin:** ${interaction.user.username}`,
                        `**Admin ID:** \`${interaction.user.id}\``,
                        `**Reason:** ${reason}`,
                        `**Timestamp:** <t:${Math.floor(Date.now() / 1000)}:F>`
                    ].join('\n'),
                    inline: false
                }
            ])
                .setTimestamp()
                .setFooter({ text: 'Account Removal System' });
            await interaction.editReply({ embeds: [successEmbed] });
            botClient.logger.info('Account removed by admin', {
                removedAccount: {
                    id: account.id,
                    username: account.username,
                    discordId: account.discord_id,
                    discordCode: account.discord_code
                },
                admin: {
                    id: interaction.user.id,
                    username: interaction.user.username
                },
                reason,
                guildId: interaction.guildId,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            const botClient = client || interaction.client;
            botClient.logger.error('Error in removeaccount command', {
                error: error.message,
                stack: error.stack,
                userId: interaction.user.id,
                username: interaction.options.getString('username')
            });
            await embeds_1.InteractionUtils.replyError(interaction, 'Command Error', 'An unexpected error occurred while removing the account.', true);
        }
    },
};
//# sourceMappingURL=removeaccount.js.map