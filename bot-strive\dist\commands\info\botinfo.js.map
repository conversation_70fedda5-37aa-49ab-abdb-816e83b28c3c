{"version": 3, "file": "botinfo.js", "sourceRoot": "", "sources": ["../../../src/commands/info/botinfo.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAmH;AAEnH,iDAAgE;AAChE,uCAAyB;AAEZ,QAAA,OAAO,GAAiB;IACnC,IAAI,EAAE,IAAI,gCAAmB,EAAE;SAC5B,OAAO,CAAC,MAAM,CAAC;SACf,cAAc,CAAC,wCAAwC,CAAC;IAE3D,KAAK,CAAC,OAAO,CAAC,WAAwC,EAAE,MAAkB;QACxE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,WAAW,CAAC,KAAK,CAAC;gBACtB,OAAO,EAAE,wBAAwB;gBACjC,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,WAAW,CAAC,UAAU,EAAE,CAAC;QAE/B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAE9D,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;YAChC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;YAC9E,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YAElE,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;YACxE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;YAC1E,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;YAE5E,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;YACrE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;YAEnE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;YAC5C,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAEzF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC3C,YAAY,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YAEnE,MAAM,KAAK,GAAG,IAAI,yBAAY,EAAE;iBAC7B,QAAQ,CAAC,oBAAoB,CAAC;iBAC9B,cAAc,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,6BAA6B,CAAC;iBACvE,QAAQ,CAAC,YAAY,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAW,CAAC,OAAO,CAAC,CAAC;gBAC1D,YAAY,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAW,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAW,CAAC,KAAK,CAAC;iBACpF,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAI,IAAI,CAAC;iBACrD,SAAS,CAAC;gBACT;oBACE,IAAI,EAAE,mBAAmB;oBACzB,KAAK,EAAE;wBACL,gBAAgB,UAAU,EAAE;wBAC5B,cAAc,SAAS,CAAC,cAAc,EAAE,EAAE;wBAC1C,iBAAiB,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;wBACvC,gBAAgB,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;wBACrC,eAAe,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;qBACpC,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,IAAI;iBACb;gBACD;oBACE,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE;wBACL,eAAe,WAAW,KAAK,aAAa,KAAK,aAAa,GAAG;wBACjE,eAAe,aAAa,QAAQ,aAAa,IAAI;wBACrD,aAAa,UAAU,QAAQ,WAAW,IAAI;wBAC9C,aAAa,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI;wBAC3C,eAAe,WAAW,IAAI,YAAY,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE;qBAClE,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,IAAI;iBACb;gBACD;oBACE,IAAI,EAAE,mBAAmB;oBACzB,KAAK,EAAE;wBACL,0BAA0B,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,EAAE;wBACrE,6BAA6B,OAAO,CAAC,mBAAmB,CAAC,cAAc,EAAE,EAAE;wBAC3E,yBAAyB,OAAO,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE;wBACnE,eAAe,OAAO,CAAC,MAAM,EAAE;wBAC/B,0BAA0B,MAAM,CAAC,UAAU,CAAC,yBAAyB,EAAE,EAAE;qBAC1E,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,mBAAmB;oBACzB,KAAK,EAAE;wBACL,gBAAgB,OAAO,CAAC,OAAO,EAAE;wBACjC,oBAAoB,oBAAU,EAAE;wBAChC,iBAAiB,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;wBAC7C,kBAAkB,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE;wBACpC,oBAAoB,YAAY,QAAQ,aAAa,IAAI;qBAC1D,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,IAAI;iBACb;gBACD;oBACE,IAAI,EAAE,gBAAgB;oBACtB,KAAK,EAAE;wBACL,oBAAoB,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;wBAC5C,kBAAkB,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;wBAC3C,2BAA2B;wBAC3B,oBAAoB,MAAM,CAAC,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;wBACpF,0BAA0B;qBAC3B,CAAC,IAAI,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE,IAAI;iBACb;aACF,CAAC;iBACD,YAAY,EAAE,CAAC;YAElB,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,KAAK,CAAC,SAAS,CAAC;oBACd;wBACE,IAAI,EAAE,kBAAkB;wBACxB,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;wBAChE,MAAM,EAAE,KAAK;qBACd;iBACF,CAAC,CAAC;YACL,CAAC;YAED,MAAM,UAAU,GAAuC;gBACrD,IAAI,EAAE,WAAW,MAAM,CAAC,IAAI,EAAE,EAAE,mBAAmB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE;aAC/E,CAAC;YAEF,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;gBACrB,UAAU,CAAC,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3D,CAAC;YAED,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAE5B,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBAC9C,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC3B,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;gBACnC,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,YAAY,EAAE,YAAY,CAAC,MAAM;gBACjC,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,aAAa;aAC3B,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC/C,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,yBAAY,EAAE;iBAClC,QAAQ,CAAC,SAAS,CAAC;iBACnB,cAAc,CAAC,6DAA6D,CAAC;iBAC7E,QAAQ,CAAC,qBAAW,CAAC,KAAK,CAAC;iBAC3B,YAAY,EAAE,CAAC;YAElB,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAC"}