"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HandlerManager = void 0;
const glob_1 = require("glob");
const util_1 = require("util");
const path_1 = __importDefault(require("path"));
const globPromise = (0, util_1.promisify)(glob_1.glob);
/**
 * Handler manager for loading commands, buttons, modals, and events
 */
class HandlerManager {
    client;
    logger;
    constructor(client, logger) {
        this.client = client;
        this.logger = logger;
    }
    /**
     * Load all handlers (commands, buttons, modals, events)
     */
    async loadAll() {
        try {
            await Promise.all([
                this.loadCommands(),
                this.loadButtons(),
                this.loadModals(),
                this.loadEvents(),
            ]);
            this.logger.info('All handlers loaded successfully', {
                commands: this.client.commands.size,
                buttons: this.client.buttons.size,
                modals: this.client.modals.size,
            });
        }
        catch (error) {
            this.logger.error('Failed to load handlers', { error: error.message });
            throw error;
        }
    }
    /**
     * Load slash commands from the commands directory
     */
    async loadCommands() {
        try {
            const commandFiles = await globPromise(path_1.default.join(process.cwd(), 'dist', 'commands', '**', '*.js').replace(/\\/g, '/'));
            let loadedCount = 0;
            for (const filePath of commandFiles) {
                try {
                    if (this.client.config.NODE_ENV === 'development') {
                        delete require.cache[require.resolve(filePath)];
                    }
                    const commandModule = await Promise.resolve(`${filePath}`).then(s => __importStar(require(s)));
                    const command = commandModule.default || commandModule.command || commandModule;
                    if (this.isValidCommand(command)) {
                        this.client.registerCommand(command);
                        loadedCount++;
                    }
                    else {
                        this.logger.warn('Invalid command file', { filePath });
                    }
                }
                catch (error) {
                    this.logger.error('Failed to load command', {
                        filePath,
                        error: error.message,
                    });
                }
            }
            this.logger.info('Commands loaded', { count: loadedCount });
        }
        catch (error) {
            this.logger.error('Failed to load commands', { error: error.message });
            throw error;
        }
    }
    /**
     * Load button handlers from the interactions/buttons directory
     */
    async loadButtons() {
        try {
            const buttonFiles = await globPromise(path_1.default.join(process.cwd(), 'dist', 'interactions', 'buttons', '**', '*.js').replace(/\\/g, '/'));
            let loadedCount = 0;
            for (const filePath of buttonFiles) {
                try {
                    if (this.client.config.NODE_ENV === 'development') {
                        delete require.cache[require.resolve(filePath)];
                    }
                    const buttonModule = await Promise.resolve(`${filePath}`).then(s => __importStar(require(s)));
                    const button = buttonModule.default || buttonModule;
                    if (this.isValidButton(button)) {
                        this.client.registerButton(button);
                        loadedCount++;
                    }
                    else {
                        this.logger.warn('Invalid button file', { filePath });
                    }
                }
                catch (error) {
                    this.logger.error('Failed to load button', {
                        filePath,
                        error: error.message,
                    });
                }
            }
            this.logger.info('Buttons loaded', { count: loadedCount });
        }
        catch (error) {
            this.logger.error('Failed to load buttons', { error: error.message });
            throw error;
        }
    }
    /**
     * Load modal handlers from the interactions/modals directory
     */
    async loadModals() {
        try {
            const modalFiles = await globPromise(path_1.default.join(process.cwd(), 'dist', 'interactions', 'modals', '**', '*.js').replace(/\\/g, '/'));
            let loadedCount = 0;
            for (const filePath of modalFiles) {
                try {
                    if (this.client.config.NODE_ENV === 'development') {
                        delete require.cache[require.resolve(filePath)];
                    }
                    const modalModule = await Promise.resolve(`${filePath}`).then(s => __importStar(require(s)));
                    const modal = modalModule.default || modalModule;
                    if (this.isValidModal(modal)) {
                        this.client.registerModal(modal);
                        loadedCount++;
                    }
                    else {
                        this.logger.warn('Invalid modal file', { filePath });
                    }
                }
                catch (error) {
                    this.logger.error('Failed to load modal', {
                        filePath,
                        error: error.message,
                    });
                }
            }
            this.logger.info('Modals loaded', { count: loadedCount });
        }
        catch (error) {
            this.logger.error('Failed to load modals', { error: error.message });
            throw error;
        }
    }
    /**
     * Load event handlers from the events directory
     */
    async loadEvents() {
        try {
            const eventFiles = await globPromise(path_1.default.join(process.cwd(), 'dist', 'events', '*.js').replace(/\\/g, '/'));
            let loadedCount = 0;
            for (const filePath of eventFiles) {
                try {
                    if (this.client.config.NODE_ENV === 'development') {
                        delete require.cache[require.resolve(filePath)];
                    }
                    const eventModule = await Promise.resolve(`${filePath}`).then(s => __importStar(require(s)));
                    const eventHandler = eventModule.default || eventModule;
                    if (typeof eventHandler === 'function') {
                        eventHandler(this.client);
                        loadedCount++;
                    }
                    else {
                        this.logger.warn('Invalid event file', { filePath });
                    }
                }
                catch (error) {
                    this.logger.error('Failed to load event', {
                        filePath,
                        error: error.message,
                    });
                }
            }
            this.logger.info('Events loaded', { count: loadedCount });
        }
        catch (error) {
            this.logger.error('Failed to load events', { error: error.message });
            throw error;
        }
    }
    /**
     * Validate command structure
     */
    isValidCommand(command) {
        return (typeof command === 'object' &&
            command !== null &&
            'data' in command &&
            'execute' in command &&
            typeof command.execute === 'function');
    }
    /**
     * Validate button structure
     */
    isValidButton(button) {
        return (typeof button === 'object' &&
            button !== null &&
            'customId' in button &&
            'execute' in button &&
            typeof button.customId === 'string' &&
            typeof button.execute === 'function');
    }
    /**
     * Validate modal structure
     */
    isValidModal(modal) {
        return (typeof modal === 'object' &&
            modal !== null &&
            'customId' in modal &&
            'execute' in modal &&
            typeof modal.customId === 'string' &&
            typeof modal.execute === 'function');
    }
}
exports.HandlerManager = HandlerManager;
//# sourceMappingURL=HandlerManager.js.map