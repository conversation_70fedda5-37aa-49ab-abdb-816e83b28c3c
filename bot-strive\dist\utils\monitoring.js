"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringSystem = void 0;
/**
 * Monitoring and analytics system
 */
class MonitoringSystem {
    logger;
    metrics;
    startTime;
    monitoringInterval;
    isRunning = false;
    constructor(_client, logger) {
        this.logger = logger;
        this.startTime = Date.now();
        this.metrics = {
            uptime: 0,
            memoryUsage: process.memoryUsage(),
            commandsExecuted: 0,
            interactionsHandled: 0,
            databaseQueries: 0,
            errors: 0,
            serverQuerySuccess: 0,
            serverQueryFailed: 0,
        };
    }
    /**
     * Start monitoring system
     */
    start() {
        if (this.isRunning) {
            this.logger.warn('Monitoring system is already running');
            return;
        }
        this.logger.info('Starting monitoring system');
        this.monitoringInterval = setInterval(() => {
            try {
                this.updateMetrics();
                this.logMetrics();
            }
            catch (error) {
                this.logger.error('Error in monitoring interval', {
                    error: error.message
                });
            }
        }, 30000);
        this.isRunning = true;
        this.logger.info('Bot monitoring started', {
            startTime: new Date(this.startTime).toISOString(),
            nodeVersion: process.version,
            platform: process.platform,
        });
    }
    /**
     * Stop monitoring system
     */
    stop() {
        if (!this.isRunning) {
            this.logger.warn('Monitoring system is not running');
            return;
        }
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
        }
        this.isRunning = false;
        this.logger.info('Monitoring system stopped');
    }
    /**
     * Update performance metrics
     */
    updateMetrics() {
        this.metrics.uptime = Date.now() - this.startTime;
        this.metrics.memoryUsage = process.memoryUsage();
    }
    /**
     * Log current metrics
     */
    logMetrics() {
        const uptimeHours = Math.floor(this.metrics.uptime / (1000 * 60 * 60));
        const uptimeMinutes = Math.floor((this.metrics.uptime % (1000 * 60 * 60)) / (1000 * 60));
        this.logger.info('Performance metrics', {
            uptime: `${uptimeHours}h ${uptimeMinutes}m`,
            memoryUsageMB: Math.round(this.metrics.memoryUsage.rss / 1024 / 1024),
            heapUsedMB: Math.round(this.metrics.memoryUsage.heapUsed / 1024 / 1024),
            commandsExecuted: this.metrics.commandsExecuted,
            interactionsHandled: this.metrics.interactionsHandled,
            databaseQueries: this.metrics.databaseQueries,
            errors: this.metrics.errors,
            serverQuerySuccessRate: this.getServerQuerySuccessRateInternal(),
        });
    }
    /**
     * Record command execution
     */
    recordCommand() {
        this.metrics.commandsExecuted++;
    }
    /**
     * Record interaction handling
     */
    recordInteraction() {
        this.metrics.interactionsHandled++;
    }
    /**
     * Record database query
     */
    recordDatabaseQuery() {
        this.metrics.databaseQueries++;
    }
    /**
     * Record error
     */
    recordError(error) {
        this.metrics.errors++;
        this.metrics.lastError = error;
    }
    /**
     * Record server query success
     */
    recordServerQuerySuccess() {
        this.metrics.serverQuerySuccess++;
    }
    /**
     * Record server query failure
     */
    recordServerQueryFailed() {
        this.metrics.serverQueryFailed++;
    }
    /**
     * Get server query success rate
     */
    getServerQuerySuccessRate() {
        const total = this.metrics.serverQuerySuccess + this.metrics.serverQueryFailed;
        if (total === 0)
            return '0%';
        return `${Math.round((this.metrics.serverQuerySuccess / total) * 100)}%`;
    }
    /**
     * Get server query success rate (private method for internal use)
     */
    getServerQuerySuccessRateInternal() {
        return this.getServerQuerySuccessRate();
    }
    /**
     * Get current metrics
     */
    getMetrics() {
        this.updateMetrics();
        return { ...this.metrics };
    }
    /**
     * Generate health report
     */
    generateHealthReport() {
        const metrics = this.getMetrics();
        const issues = [];
        let status = 'healthy';
        const memoryUsageMB = metrics.memoryUsage.rss / 1024 / 1024;
        if (memoryUsageMB > 500) {
            issues.push(`High memory usage: ${Math.round(memoryUsageMB)}MB`);
            status = 'warning';
        }
        if (memoryUsageMB > 1000) {
            status = 'critical';
        }
        const errorRate = metrics.errors / Math.max(metrics.interactionsHandled, 1);
        if (errorRate > 0.1) {
            issues.push(`High error rate: ${Math.round(errorRate * 100)}%`);
            status = status === 'critical' ? 'critical' : 'warning';
        }
        const total = metrics.serverQuerySuccess + metrics.serverQueryFailed;
        if (total > 0) {
            const successRate = metrics.serverQuerySuccess / total;
            if (successRate < 0.8) {
                issues.push(`Low server query success rate: ${Math.round(successRate * 100)}%`);
                status = status === 'critical' ? 'critical' : 'warning';
            }
        }
        return {
            status,
            issues,
            metrics,
        };
    }
    /**
     * Log health report
     */
    logHealthReport() {
        const report = this.generateHealthReport();
        if (report.status === 'healthy') {
            this.logger.info('Health check: All systems operational', {
                status: report.status,
                uptime: Math.floor(report.metrics.uptime / 1000),
            });
        }
        else {
            this.logger.warn('Health check: Issues detected', {
                status: report.status,
                issues: report.issues,
                uptime: Math.floor(report.metrics.uptime / 1000),
            });
        }
    }
}
exports.MonitoringSystem = MonitoringSystem;
//# sourceMappingURL=monitoring.js.map