import { Client, Collection, Slash<PERSON><PERSON>mandBuilder, ChatInputCommandInteraction, ButtonInteraction, ModalSubmitInteraction, ContextMenuCommandBuilder, UserContextMenuCommandInteraction, MessageContextMenuCommandInteraction } from 'discord.js';
/**
 * Extended Discord client with custom properties
 */
export interface ExtendedClient extends Client {
    commands: Collection<string, SlashCommand>;
    buttons: Collection<string, ButtonHandler>;
    modals: Collection<string, ModalHandler>;
    config: import('./config').Config;
    isReady(): this is Client<true>;
}
/**
 * Slash command interface
 */
export interface SlashCommand {
    data: SlashCommandBuilder | ContextMenuCommandBuilder;
    execute: (interaction: ChatInputCommandInteraction, client?: any) => Promise<void>;
    cooldown?: number;
    permissions?: string[];
    ownerOnly?: boolean;
}
/**
 * Button interaction handler interface
 */
export interface ButtonHandler {
    customId: string;
    execute: (interaction: ButtonInteraction) => Promise<void>;
    permissions?: string[];
    cooldown?: number;
}
/**
 * Modal interaction handler interface
 */
export interface ModalHandler {
    customId: string;
    execute: (interaction: ModalSubmitInteraction) => Promise<void>;
    permissions?: string[];
}
/**
 * Event handler interface
 */
export interface EventHandler {
    name: string;
    once: boolean;
    execute: (...args: any[]) => Promise<void>;
}
/**
 * Context menu command types
 */
export type ContextMenuCommand = UserContextMenuCommand | MessageContextMenuCommand;
export interface UserContextMenuCommand {
    data: ContextMenuCommandBuilder;
    execute: (interaction: UserContextMenuCommandInteraction) => Promise<void>;
    permissions?: string[];
    ownerOnly?: boolean;
}
export interface MessageContextMenuCommand {
    data: ContextMenuCommandBuilder;
    execute: (interaction: MessageContextMenuCommandInteraction) => Promise<void>;
    permissions?: string[];
    ownerOnly?: boolean;
}
/**
 * Command execution result
 */
export interface CommandResult {
    success: boolean;
    message?: string;
    error?: Error;
}
/**
 * Embed color constants
 */
export declare enum EmbedColors {
    SUCCESS = 65280,
    ERROR = 16711680,
    WARNING = 16776960,
    INFO = 39423,
    PRIMARY = 5793266
}
/**
 * Bot status information
 */
export interface BotStatus {
    isOnline: boolean;
    currentPlayers: number;
    maxPlayers: number;
    serverIp: string;
    serverPort: number;
}
/**
 * SAMP server query response
 */
export interface SampQueryResponse {
    online: number;
    maxplayers: number;
    hostname: string;
    gamemode: string;
    language: string;
}
/**
 * Webhook configuration for error logging
 */
export interface WebhookConfig {
    id: string;
    token: string;
}
/**
 * Error logging webhook payload
 */
export interface ErrorWebhookPayload {
    content: string;
    embeds?: Array<{
        title?: string;
        description?: string;
        color?: number;
        timestamp?: string;
        fields?: Array<{
            name: string;
            value: string;
            inline?: boolean;
        }>;
    }>;
}
//# sourceMappingURL=discord.d.ts.map